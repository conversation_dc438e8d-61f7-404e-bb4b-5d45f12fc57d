<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import AppSidebar from './components/AppSidebar.vue';
import MainView from './components/MainView.vue';
import RightBar from './components/RightBar.vue';
import HelpDialog from './components/HelpDialog.vue';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { MessageSquare, Home } from 'lucide-vue-next';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'vue-sonner';
import { Button } from '@/components/ui/button';

// 引入 Sonner 样式
import 'vue-sonner/style.css';

const router = useRouter();
const route = useRoute();

// 路由过渡动画名称
const transitionName = ref('slide-left');

// 监听路由变化，设置过渡方向
router.beforeEach((to, from, next) => {
  if (from.path === '/' && to.path === '/chat') {
    transitionName.value = 'slide-left';
  } else if (from.path === '/chat' && to.path === '/') {
    transitionName.value = 'slide-right';
  } else {
    transitionName.value = 'fade';
  }
  next();
});

// 判断是否是 Landing 页面
const isLandingPage = computed(() => route.meta.isLanding === true);

// Session 选择状态
const selectedSessionId = ref<string>('');
const selectedSessionName = ref<string>('');

// Sidebar 引用
const sidebarRef = ref<InstanceType<typeof AppSidebar> | null>(null);
// RightBar 引用
const rightBarRef = ref<InstanceType<typeof RightBar> | null>(null);
// ChatPanel 引用
const chatPanelRef = ref<any>(null);

// 处理 session 选择事件
const handleSelectSession = (sessionId: string, sessionName: string) => {
  console.log('[App] Session selected:', { sessionId, sessionName });
  
  // 如果是同一个 session，先清空再设置，强制触发 watch
  if (selectedSessionId.value === sessionId) {
    selectedSessionId.value = '';
    // 使用 nextTick 确保响应式系统检测到变化
    setTimeout(() => {
      selectedSessionId.value = sessionId;
      selectedSessionName.value = sessionName;
    }, 0);
  } else {
    selectedSessionId.value = sessionId;
    selectedSessionName.value = sessionName;
  }
  
  // 导航到对话页面
  router.push('/chat');
};

// 处理新 session 创建事件
const handleSessionCreated = async (sessionId: string) => {
  console.log('[App] New session created:', sessionId);

  // 立即更新当前选中状态,让用户看到新对话
  selectedSessionId.value = sessionId;
  selectedSessionName.value = '新对话';

  // 短暂延迟,确保后端数据库事务完成
  await new Promise(resolve => setTimeout(resolve, 300));

  // 刷新侧边栏的 session 列表
  if (sidebarRef.value) {
    console.log('[App] Refreshing sidebar sessions...');
    await sidebarRef.value.refreshSessions();
    console.log('[App] Sidebar sessions refreshed');

    // 更新会话名称(如果刷新后获得了更准确的名称)
    const sessions = sidebarRef.value.getSessions?.();
    if (sessions) {
      const newSession = sessions.find((s: any) => s.session_id === sessionId);
      if (newSession && newSession.session_name) {
        selectedSessionName.value = newSession.session_name;
        console.log('[App] Updated session name:', newSession.session_name);
      }
    }
  } else {
    console.warn('[App] Sidebar ref is null, cannot refresh');
  }
};

// 处理 ChatPanel 挂载完成
const handleChatPanelMounted = (instance: any) => {
  console.log('[App] ChatPanel mounted, saving ref...');
  chatPanelRef.value = instance;
};

// 处理消息完成事件（AI 回答完成）
const handleMessageCompleted = async () => {
  console.log('[App] Message completed...');

  // 1. 刷新侧边栏（更新会话列表、标题、时间）
  if (sidebarRef.value) {
    // 稍微延迟一下，确保后端数据已落库
    setTimeout(async () => {
      console.log('[App] Refreshing sidebar sessions...');
      await sidebarRef.value?.refreshSessions();
    }, 500);
  }

  // 2. 不再刷新当前会话的消息，因为消息已经通过流式传输实时显示了
  // 刷新会导致界面重新渲染，造成"闪烁"效果

  // 3. 刷新工作区文件
  if (rightBarRef.value) {
    await rightBarRef.value.refreshWorkspaceFiles();
    console.log('[App] Workspace files refreshed');
  }
};

// 监听通知事件
const handleNotification = (event: CustomEvent) => {
  const { title, message, type } = event.detail;

  if (type === 'success') {
    toast.success(title, { description: message, duration: 2000 });
  } else if (type === 'error') {
    toast.error(title, { description: message, duration: 2000 });
  } else {
    toast(title, { description: message, duration: 2000 });
  }
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener('show-notification', handleNotification as EventListener);
});

onUnmounted(() => {
  window.removeEventListener('show-notification', handleNotification as EventListener);
});
</script>

<template>
  <!-- Landing 页面独立渲染 -->
  <div v-if="isLandingPage">
    <router-view v-slot="{ Component }">
      <transition :name="transitionName" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <Toaster />
  </div>

  <!-- 应用主体布局 -->
  <SidebarProvider v-else>
    <AppSidebar ref="sidebarRef" @select-session="handleSelectSession" />
    <SidebarInset class="flex flex-col h-screen">
      <header class="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger class="-ml-1" />
        <Separator orientation="vertical" class="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem class="hidden md:block">
              <BreadcrumbLink href="#">
                <MessageSquare class="h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator class="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage v-if="selectedSessionName">
                {{ selectedSessionName }}
              </BreadcrumbPage>
              <BreadcrumbPage v-else class="text-muted-foreground">
                新对话
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <div class="ml-auto flex items-center gap-2">
          <Button variant="ghost" size="sm" as-child>
            <a href="https://ai4ec.ac.cn/apps/eagent" target="_blank" rel="noopener noreferrer">
              <Home :size="16" />
            </a>
          </Button>
          <HelpDialog />
        </div>
      </header>
      <div class="flex flex-1 min-h-0">
        <MainView
          class="flex-1"
          :selected-session-id="selectedSessionId"
          @session-created="handleSessionCreated"
          @chat-panel-mounted="handleChatPanelMounted"
          @message-completed="handleMessageCompleted"
        />
        <RightBar ref="rightBarRef" />
      </div>
    </SidebarInset>
    <Toaster />
  </SidebarProvider>
</template>

<style scoped>
/* 确保应用占满全屏 */
</style>

<style>
/* 路由过渡动画 - 向左滑动 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-30%);
}

/* 路由过渡动画 - 向右滑动 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-30%);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* 路由过渡动画 - 淡入淡出 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
