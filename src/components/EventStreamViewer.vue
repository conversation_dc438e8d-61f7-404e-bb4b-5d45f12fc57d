<script setup lang="ts">
import { computed, ref, watch, onBeforeUnmount } from 'vue';
import { <PERSON>, Wrench, CheckCircle, MessageSquare, AlertCircle, X, ChevronDown, Brain, Loader2 } from 'lucide-vue-next';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import MarkdownRenderer from '@/components/MarkdownRenderer.vue';
import MolView from '@/components/MolView.vue';
import MoleculeGenStreamViewer from '@/components/MoleculeGenStreamViewer.vue';

// Session 数据结构（与后端返回的数据一致）
interface ToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
}

interface ReasoningStep {
  title: string;
  description?: string;
  status?: 'pending' | 'running' | 'completed' | 'failed';
  reasoning?: string;
  action?: string;
  result?: string;
  reasoning_content?: string;
  confidence?: number;
}

interface SessionMessage {
  content: string;
  from_history: boolean;
  stop_after_tool_call: boolean;
  role: 'system' | 'user' | 'assistant' | 'tool';
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  tool_name?: string;
  tool_args?: Record<string, any>;
  tool_call_error?: boolean;
  created_at: number;
  run_id: string;
  metrics?: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    cache_read_tokens?: number;
  };
  reasoning_steps?: ReasoningStep[];
}

// 分子生成进度消息
interface MoleculeProgressMessage {
  type: string;
  level?: string;
  message?: string;
  timestamp: number;
  layer?: number;
  total_generated?: number;
  unique_mols?: number;
  filtered_count?: number;
  scored_count?: number;
  best_smiles?: string;
  best_score?: number;
  top_n?: number;
  total_results?: number;
  duration?: number;
}

interface MoleculeProgressData {
  session_id: string;
  status: string;
  messages: MoleculeProgressMessage[];
  summary?: any;
}

const props = defineProps<{
  messages: SessionMessage[];
  disableMoleculeGen?: boolean; // 禁用分子生成检测（用于模态框中的实例）
}>();

const emit = defineEmits<{
  close: [];
}>();

// 事件内容折叠状态 (AI 消息和工具调用详情)
const eventContentOpenStates = ref<Record<string, boolean>>({});

// 分子生成进度状态 (轮询方式)
const moleculeProgress = ref<MoleculeProgressData | null>(null);
const moleculeProgressInterval = ref<number | null>(null);
const moleculeProgressLastTimestamp = ref<number>(0);
// SSE 连接
const moleculeSSEConnection = ref<EventSource | null>(null);

// 监听 moleculeProgress 变化
watch(moleculeProgress, (newVal) => {
  console.log('[EventStreamViewer] moleculeProgress changed:', newVal);
  console.log('[EventStreamViewer] currentSmiles:', newVal?.summary?.best_smiles);
}, { deep: true });

// 3D 分子查看器状态
const showMoleculeViewer = ref(false);
const selectedMolecule = ref<{ smiles: string; properties?: any }>({ smiles: '' });
const selectedMoleculeXYZ = ref('');
const show3DView = ref(false); // 默认显示 2D 视图

// 计算属性：获取所有事件（扁平化结构）
const events = computed(() => {
  const allEvents: Array<{
    id: string;
    type: 'user' | 'thinking' | 'tool_call' | 'tool_result';
    timestamp: number;
    data: any;
  }> = [];

  props.messages.forEach((msg) => {
    if (msg.role === 'user') {
      allEvents.push({
        id: `user-${msg.created_at}`,
        type: 'user',
        timestamp: msg.created_at,
        data: msg,
      });
    } else if (msg.role === 'assistant') {
      // AI 消息拆分为：思考过程和工具调用
      if (msg.content) {
        allEvents.push({
          id: `thinking-${msg.created_at}`,
          type: 'thinking',
          timestamp: msg.created_at,
          data: {
            content: msg.content,
            metrics: msg.metrics,
            run_id: msg.run_id,
            created_at: msg.created_at,
          },
        });
      }
      // 工具调用作为独立事件
      if (msg.tool_calls && msg.tool_calls.length > 0) {
        msg.tool_calls.forEach((toolCall, index) => {
          allEvents.push({
            id: `tool-call-${toolCall.id}`,
            type: 'tool_call',
            timestamp: msg.created_at + index * 0.001, // 微调时间戳保持顺序
            data: {
              ...toolCall,
              run_id: msg.run_id,
            },
          });
        });
      }
    } else if (msg.role === 'tool') {
      // 工具返回结果 - 添加到事件列表供 findToolResult 使用
      // 使用 tool_calls[0].id 作为 tool_call_id
      const toolCallId = msg.tool_call_id || msg.tool_calls?.[0]?.id;
      allEvents.push({
        id: `tool-result-${toolCallId}`,
        type: 'tool_result',
        timestamp: msg.created_at,
        data: {
          ...msg,
          tool_call_id: toolCallId, // 确保工具调用ID存在
        },
      });
    }
  });

  // 按时间戳排序
  return allEvents.sort((a, b) => a.timestamp - b.timestamp);
});

// 计算属性：用于渲染的事件（过滤掉独立的工具结果事件）
const displayEvents = computed(() => {
  return events.value.filter(e => e.type !== 'tool_result');
});

// 计算属性：事件统计
const eventStats = computed(() => {
  const userCount = events.value.filter(e => e.type === 'user').length;
  const thinkingCount = events.value.filter(e => e.type === 'thinking').length;
  const toolCallCount = events.value.filter(e => e.type === 'tool_call').length;
  const toolResultCount = events.value.filter(e => e.type === 'tool_result').length;
  const errorCount = events.value.filter(e => e.data?.tool_call_error).length;

  return {
    total: events.value.length,
    user: userCount,
    thinking: thinkingCount,
    toolCalls: toolCallCount,
    toolResults: toolResultCount,
    errors: errorCount,
  };
});

// 获取事件图标
const getEventIcon = (event: any) => {
  if (event.type === 'user') {
    return MessageSquare;
  } else if (event.type === 'assistant') {
    return MessageSquare;
  } else if (event.type === 'tool_result') {
    return event.data?.tool_call_error ? AlertCircle : CheckCircle;
  } else if (event.type === 'thinking') {
    return Brain;
  } else if (event.type === 'tool_call') {
    return Wrench;
  }
  return Clock;
};

// 获取事件类型颜色
const getEventTypeColor = (type: string, hasError: boolean) => {
  if (hasError) return 'text-red-500';

  switch (type) {
    case 'user':
      return 'text-blue-500';
    case 'thinking':
      return 'text-purple-500';
    case 'tool_call':
      return 'text-amber-500';
    case 'tool_result':
      return 'text-green-500';
    default:
      return 'text-muted-foreground';
  }
};

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 检测是否有分子生成工具调用
const hasMoleculeGenerationTool = computed(() => {
  const result = events.value.some(e =>
    e.type === 'tool_call' &&
    e.data.function?.name === 'generate_molecules_stream'
  );
  console.log('[EventStreamViewer] hasMoleculeGenerationTool:', result);
  console.log('[EventStreamViewer] All events:', events.value.map(e => ({ type: e.type, functionName: e.data.function?.name })));
  return result;
});

// 提取分子生成工具的输入参数
const moleculeGenInputArgs = computed(() => {
  const toolCallEvent = events.value.find(e =>
    e.type === 'tool_call' &&
    e.data.function?.name === 'generate_molecules_stream'
  );

  if (toolCallEvent?.data.function?.arguments) {
    try {
      const args = JSON.parse(toolCallEvent.data.function.arguments);
      console.log('[EventStreamViewer] moleculeGenInputArgs:', args);
      return args;
    } catch (e) {
      console.error('[EventStreamViewer] Failed to parse tool args:', e);
    }
  }
  return null;
});

// 提取 session_id（从第一个 assistant 消息的 run_id）
const sessionId = computed(() => {
  const assistantMsg = props.messages.find(m => m.role === 'assistant');
  // 从 run_id 提取或使用其他方式获取 session_id
  // 暂时使用 run_id 作为标识
  return assistantMsg?.run_id || '';
});

// 提取分子生成的 run_id
const moleculeGenRunId = computed(() => {
  // 直接从消息的 run_id 字段获取（session API 已经提供）
  for (const msg of props.messages) {
    if (msg.run_id && msg.tool_calls?.some((tc: any) =>
      tc.function?.name === 'generate_molecules_stream'
    )) {
      return msg.run_id;
    }
  }

  return null;
});

// 格式化进度消息
const formatProgressMessage = (msg: MoleculeProgressMessage): string => {
  if (msg.message) return msg.message;

  switch (msg.type) {
    case 'generation':
      return `第 ${msg.layer} 层: 生成 ${msg.total_generated} 个分子`;
    case 'filtering':
      return `筛选后剩余 ${msg.filtered_count} 个分子`;
    case 'scoring':
      return `最佳: ${msg.best_smiles} (评分: ${msg.best_score?.toFixed(2)})`;
    case 'selection':
      return `选择 Top ${msg.top_n} 个候选分子`;
    case 'layer_end':
      return `第 ${msg.layer} 层完成 (耗时: ${msg.duration?.toFixed(1)}s)`;
    case 'finish':
      return `✓ 生成完成！共 ${msg.total_results} 个结果 (耗时: ${(msg.duration! / 60).toFixed(1)} 分钟)`;
    default:
      return '';
  }
};

// 解析工具调用参数
const parseArguments = (argsStr: string) => {
  try {
    const args = JSON.parse(argsStr);
    return args;
  } catch {
    // 如果解析失败，返回空对象
    return {};
  }
};

// 格式化参数值
const formatArgValue = (value: any): string => {
  if (typeof value === 'string') {
    return value;
  } else if (typeof value === 'number') {
    return value.toString();
  } else if (typeof value === 'boolean') {
    return value ? 'true' : 'false';
  } else if (Array.isArray(value)) {
    return `[${value.join(', ')}]`;
  } else if (typeof value === 'object' && value !== null) {
    return JSON.stringify(value, null, 2);
  }
  return String(value);
};

// 查找工具调用对应的结果
const findToolResult = (toolCallId: string) => {
  const result = events.value.find(e =>
    e.type === 'tool_result' &&
    e.data.tool_call_id === toolCallId
  );

  // 如果没找到结果，检查是否有任何 tool_result 消息
  if (!result) {
    const allToolResults = events.value.filter(e => e.type === 'tool_result');
    console.log('[EventStreamViewer] Tool result not found:', {
      toolCallId,
      allToolResults: allToolResults.map(e => ({
        tool_call_id: e.data.tool_call_id,
        hasContent: !!e.data.content,
        contentPreview: e.data.content?.substring(0, 50)
      }))
    });
  }

  return result;
};

// 判断是否是分子生成工具
const isMoleculeGenerationTool = (toolName?: string) => {
  return toolName === 'generate_molecules_stream';
};

// 用于跟踪当前正在轮询的 runId
const currentPollingRunId = ref<string | null>(null);

// 轮询分子生成进度
const startMoleculeProgressPolling = (runId: string) => {
  console.log('[EventStreamViewer] Starting molecule progress monitoring with runId:', runId);

  // ✅ 防止重复触发
  if (currentPollingRunId.value === runId) {
    console.log('[EventStreamViewer] Already monitoring runId:', runId, ', skipping');
    return;
  }

  // 清除之前的连接
  if (moleculeProgressInterval.value) {
    clearInterval(moleculeProgressInterval.value);
    moleculeProgressInterval.value = null;
  }
  if (moleculeSSEConnection.value) {
    moleculeSSEConnection.value.close();
    moleculeSSEConnection.value = null;
  }

  // 更新当前监控的 runId
  currentPollingRunId.value = runId;

  // ✅ 触发自定义事件，通知 RightBar 打开进度查看器
  window.dispatchEvent(new CustomEvent('open-molecule-gen', {
    detail: { runId: runId }
  }));

  // ✅ 使用 SSE 流式传输（实时更新）
  console.log('[EventStreamViewer] Using SSE for real-time updates');
  connectMoleculeProgressSSE(runId);
};

// ✅ 修复中文编码问题（SSE 可能返回了乱码）
const fixEncoding = (str: string): string => {
  // 检测是否包含 UTF-8 被错误解析为 Latin-1 的特征字符
  // 例如: è¾"å¥åˆ†å­ -> 输入分子
  if (/\u00c3[\u0080-\u00bf]/.test(str) || /[\u00e0-\u00ff]{2,}/.test(str)) {
    try {
      // 将 Latin-1 解码的字节重新解释为 UTF-8
      const bytes = new Uint8Array(str.length);
      for (let i = 0; i < str.length; i++) {
        bytes[i] = str.charCodeAt(i) & 0xff;
      }
      return new TextDecoder('utf-8').decode(bytes);
    } catch (e) {
      console.warn('[EventStreamViewer] Encoding fix failed:', e);
      return str;
    }
  }
  return str;
};

// 递归修复所有字符串字段
const fixData = (obj: any): any => {
  if (typeof obj === 'string') {
    return fixEncoding(obj);
  } else if (Array.isArray(obj)) {
    return obj.map(fixData);
  } else if (obj && typeof obj === 'object') {
    const fixed: any = {};
    for (const [key, value] of Object.entries(obj)) {
      fixed[key] = fixData(value);
    }
    return fixed;
  }
  return obj;
};

// 使用 SSE 连接分子生成进度（实时流式传输）
const connectMoleculeProgressSSE = (runId: string) => {
  console.log('[EventStreamViewer] Connecting to SSE for runId:', runId);
  console.log('[EventStreamViewer] Full SSE URL:', `/apps/eagent/api/molecule-stream/${runId}`);

  // 关闭之前的连接
  if (moleculeSSEConnection.value) {
    console.log('[EventStreamViewer] Closing previous SSE connection');
    moleculeSSEConnection.value.close();
  }

  try {
    const eventSource = new EventSource(`/apps/eagent/api/molecule-stream/${runId}`);
    console.log('[EventStreamViewer] EventSource created successfully');

    // ✅ 初始化 moleculeProgress
    if (!moleculeProgress.value || moleculeProgress.value.session_id !== runId) {
      moleculeProgress.value = {
        session_id: runId,
        status: 'running',
        messages: [],
        summary: undefined,
      };
      console.log('[EventStreamViewer] Initialized moleculeProgress');
    }

    // ✅ 添加打开事件
    eventSource.onopen = () => {
      console.log('[EventStreamViewer] SSE connection opened successfully');
    };

    eventSource.addEventListener('connected', (event) => {
      console.log('[EventStreamViewer] SSE connected event received:', event.data);
    });

    eventSource.addEventListener('status', (event) => {
      const data = JSON.parse(event.data);
      if (moleculeProgress.value) {
        moleculeProgress.value.status = data.status;
      }
    });

    eventSource.addEventListener('log', (event) => {
      const data = JSON.parse(event.data);
      const fixedData = fixData(data);

      // 更新日志（即使 message 为空也记录）
      if (moleculeProgress.value) {
        if (!moleculeProgress.value.messages) {
          moleculeProgress.value.messages = [];
        }
        // 映射 level 字符串到正确的类型
        const level = fixedData.level || 'info';
        const validLevel = ['info', 'warning', 'error', 'success'].includes(level) ? level as any : 'info';

        moleculeProgress.value.messages.push({
          type: 'log',
          level: validLevel,
          message: fixedData.message || '',  // 允许空消息
          timestamp: fixedData.timestamp || Date.now() / 1000
        });
        console.log('[EventStreamViewer] Log message added:', fixedData.message?.substring(0, 50));
      }
    });

    eventSource.addEventListener('complete', (event) => {
      const data = JSON.parse(event.data);
      const fixedData = fixData(data);
      // 任务完成
      if (moleculeProgress.value) {
        moleculeProgress.value.status = 'completed';
        moleculeProgress.value.summary = fixedData.summary;
      }
      // 关闭 SSE 连接
      eventSource.close();
      moleculeSSEConnection.value = null;
    });

    eventSource.addEventListener('serverError', (event) => {
      const data = JSON.parse(event.data);
      const fixedData = fixData(data);
      // 服务器错误
      if (moleculeProgress.value) {
        moleculeProgress.value.status = 'error';
      }
      console.error('[EventStreamViewer] SSE error:', fixedData.message);
      eventSource.close();
      moleculeSSEConnection.value = null;
    });

    eventSource.addEventListener('timeout', () => {
      console.error('[EventStreamViewer] SSE connection closed, will retry with polling...');
        eventSource.close();
        // 降级为轮询
        if (!moleculeProgressInterval.value) {
          console.log('[EventStreamViewer] Falling back to polling');
          moleculeProgressInterval.value = window.setInterval(() => {
            fetchMoleculeProgress(runId);
          }, 2000);
        }
    })

    // eventSource.onmessage = (event) => {
    //   try {
    //     console.log('[EventStreamViewer] SSE received:', event.data);
    //     const data = JSON.parse(event.data);

    //     const fixedData = fixData(data);

    //     console.log('[EventStreamViewer] SSE received:', fixedData);
    //     console.log('[EventStreamViewer] moleculeProgress.value:', moleculeProgress.value);
    //     console.log('[EventStreamViewer] messages count:', moleculeProgress.value?.messages?.length);

    //     if (fixedData.type === 'connected') {
    //       // 连接成功确认
    //       console.log('[EventStreamViewer] SSE connected event received');
    //     } else if (fixedData.type === 'status') {
    //       // 更新状态
    //       if (moleculeProgress.value) {
    //         moleculeProgress.value.status = fixedData.status;
    //       }
    //     } else if (fixedData.type === 'log') {
    //       // 更新日志（即使 message 为空也记录）
    //       if (moleculeProgress.value) {
    //         if (!moleculeProgress.value.messages) {
    //           moleculeProgress.value.messages = [];
    //         }
    //         // 映射 level 字符串到正确的类型
    //         const level = fixedData.level || 'info';
    //         const validLevel = ['info', 'warning', 'error', 'success'].includes(level) ? level as any : 'info';

    //         moleculeProgress.value.messages.push({
    //           type: 'log',
    //           level: validLevel,
    //           message: fixedData.message || '',  // 允许空消息
    //           timestamp: fixedData.timestamp || Date.now() / 1000
    //         });
    //         console.log('[EventStreamViewer] Log message added:', fixedData.message?.substring(0, 50));
    //       }
    //     } else if (fixedData.type === 'complete') {
    //       // 任务完成
    //       if (moleculeProgress.value) {
    //         moleculeProgress.value.status = 'completed';
    //         moleculeProgress.value.summary = fixedData.summary;
    //       }
    //       // 关闭 SSE 连接
    //       eventSource.close();
    //       moleculeSSEConnection.value = null;
    //     } else if (fixedData.type === 'error') {
    //       // 错误
    //       if (moleculeProgress.value) {
    //         moleculeProgress.value.status = 'error';
    //       }
    //       console.error('[EventStreamViewer] SSE error:', fixedData.message);
    //       eventSource.close();
    //       moleculeSSEConnection.value = null;
    //     }
    //   } catch (e) {
    //     console.error('[EventStreamViewer] Failed to parse SSE data:', e, event.data);
    //   }
    // };

    // ✅ 添加错误处理
    eventSource.onerror = (error) => {
      console.error('[EventStreamViewer] SSE connection error:', error);
      console.error('[EventStreamViewer] EventSource readyState:', eventSource.readyState);

      // readyState: 0=CONNECTING, 1=OPEN, 2=CLOSED
      if (eventSource.readyState === 2) {
        console.error('[EventStreamViewer] SSE connection closed, will retry with polling...');
        eventSource.close();
        // 降级为轮询
        if (!moleculeProgressInterval.value) {
          console.log('[EventStreamViewer] Falling back to polling');
          moleculeProgressInterval.value = window.setInterval(() => {
            fetchMoleculeProgress(runId);
          }, 2000);
        }
      }
    };

    moleculeSSEConnection.value = eventSource;
  } catch (error) {
    console.error('[EventStreamViewer] Failed to create SSE connection:', error);
  }
};

// 获取分子生成进度（降级为轮询，如果 SSE 不可用）
const fetchMoleculeProgress = async (runId: string) => {
  try {
    // 使用完整的 API 路径（包含 /apps/eagent 前缀）
    const response = await fetch(`/apps/eagent/api/molecule-progress/${runId}`);
    if (!response.ok) {
      console.warn('[EventStreamViewer] Failed to fetch progress:', response.status);
      return;
    }

    const data = await response.json();
    console.log('[EventStreamViewer] Fetched molecule progress:', data);

    moleculeProgress.value = data;

    // 如果任务完成或失败，停止轮询
    if (data.status === 'completed' || data.status === 'error' || data.status === 'timeout') {
      if (moleculeProgressInterval.value) {
        clearInterval(moleculeProgressInterval.value);
        moleculeProgressInterval.value = null;
      }
    }
  } catch (error) {
    console.error('[EventStreamViewer] Error fetching molecule progress:', error);
  }
};

// 监听消息变化，自动提取分子生成进度数据
watch(() => props.messages, (newMessages) => {
  // ✅ 如果禁用分子生成检测，直接返回
  if (props.disableMoleculeGen) {
    return;
  }

  console.log('[EventStreamViewer] Messages changed, checking for molecule generation data...');
  console.log('[EventStreamViewer] Messages:', newMessages.map(m => ({ role: m.role, tool_name: m.tool_name })));

  // 查找分子生成工具的结果
  for (const msg of newMessages) {
    console.log('[EventStreamViewer] Checking message:', {
      role: msg.role,
      tool_name: msg.tool_name,
      hasContent: !!msg.content,
      hasToolCalls: !!msg.tool_calls,
      toolCallsCount: msg.tool_calls?.length || 0
    });

    // 检查是否是分子生成工具的调用或结果
    const isToolResult = msg.role === 'tool' && msg.tool_name === 'generate_molecules_stream';

    // ✅ 修复：检查 tool_calls 而不是 events
    const isAssistantWithToolCall = msg.role === 'assistant' &&
      msg.tool_calls?.some((tc: any) => tc.function?.name === 'generate_molecules_stream');

    console.log('[EventStreamViewer] isToolResult:', isToolResult, 'isAssistantWithToolCall:', isAssistantWithToolCall);

    if (isToolResult || isAssistantWithToolCall) {
      console.log('[EventStreamViewer] Found molecule generation tool message, role:', msg.role);

      // 直接从消息的 run_id 字段获取（session API 已经提供）
      const runId = msg.run_id;
      console.log('[EventStreamViewer] Extracted runId:', runId);
      console.log('[EventStreamViewer] currentPollingRunId:', currentPollingRunId.value);
      console.log('[EventStreamViewer] Full message object:', msg);

      // 检查是否已经在监听这个 run_id
      if (runId && currentPollingRunId.value !== runId) {
        // 关闭旧的 SSE 连接
        if (moleculeSSEConnection.value) {
          console.log('[EventStreamViewer] Closing old SSE connection for run_id:', currentPollingRunId.value);
          moleculeSSEConnection.value.close();
          moleculeSSEConnection.value = null;
        }

        console.log('[EventStreamViewer] Starting SSE for run_id:', runId);
        startMoleculeProgressPolling(runId);
        currentPollingRunId.value = runId;
        break;
      } else if (!runId) {
        console.warn('[EventStreamViewer] No run_id found in message');
        console.warn('[EventStreamViewer] Message keys:', Object.keys(msg));
      } else {
        console.log('[EventStreamViewer] Already monitoring run_id:', runId, ', skipping...');
      }
    }

    // 尝试解析 JSON 进度数据（兼容性）
    try {
      if (typeof msg.content === 'string') {
        const data = JSON.parse(msg.content);
        console.log('[EventStreamViewer] Parsed JSON from message content:', data);
        // 检查是否包含进度数据
        if (data.status || data.messages || data.summary) {
          console.log('[EventStreamViewer] Found molecule generation data in JSON:', data);
          moleculeProgress.value = data;
          break;
        }
      }
    } catch (e) {
      // 不是 JSON，忽略错误
    }
  }
}, { deep: true, immediate: true });

// 组件卸载时清理轮询和 SSE 连接
onBeforeUnmount(() => {
  // 清理轮询
  if (moleculeProgressInterval.value) {
    clearInterval(moleculeProgressInterval.value);
    moleculeProgressInterval.value = null;
  }

  // 清理 SSE 连接
  if (moleculeSSEConnection.value) {
    console.log('[EventStreamViewer] Closing SSE connection on unmount');
    moleculeSSEConnection.value.close();
    moleculeSSEConnection.value = null;
  }

  currentPollingRunId.value = null;
});

// 获取进度消息图标
const getProgressMessageIcon = (type: string) => {
  const icons: Record<string, string> = {
    log: '📝',
    generation: '⚙️',
    filtering: '🔍',
    scoring: '📊',
    selection: '✅',
    layer_end: '📌',
    finish: '🎉',
  };
  return icons[type] || '•';
};

// 查看分子 3D 结构
const viewMolecule3D = async (smiles: string, properties?: any) => {
  selectedMolecule.value = { smiles, properties };
  selectedMoleculeXYZ.value = '';
  show3DView.value = false; // 默认显示 2D
  showMoleculeViewer.value = true;

  try {
    const response = await fetch('/toolbox/smiles_to_xyz', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'apikey',
      },
      body: JSON.stringify({ smiles }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate 3D structure');
    }

    const data = await response.json();
    selectedMoleculeXYZ.value = data.xyz;
  } catch (error) {
    console.error('[EventStreamViewer] Failed to generate 3D structure:', error);
  }
};

// 格式化工具结果内容（自动识别 JSON）
const formatToolResultContent = (content: string) => {
  if (!content) return '';
  try {
    const trimmed = content.trim();
    // 简单的 JSON 探测
    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
      (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
      const json = JSON.parse(content);
      return '```json\n' + JSON.stringify(json, null, 2) + '\n```';
    }
  } catch (e) {
    // 解析失败，按原样返回
  }
  return content;
};

// 取消分子生成
const cancelMoleculeGeneration = () => {
  console.log('[EventStreamViewer] Cancel molecule generation requested');
  // TODO: 实现取消逻辑，可能需要调用后端 API
  if (moleculeProgressInterval.value) {
    clearInterval(moleculeProgressInterval.value);
    moleculeProgressInterval.value = null;
  }
};
</script>

<template>
  <div class="event-stream-viewer h-full flex flex-col">
    <!-- 标题和统计 -->
    <div class="flex-shrink-0 px-4 py-3 border-b flex items-center justify-between gap-2">
      <div class="w-7"></div>
      <div class="flex items-center gap-3 min-w-0">
        <h3 class="text-base font-semibold flex-shrink-0">工具调用</h3>
        <Badge v-if="eventStats.errors > 0" variant="destructive" class="text-xs flex-shrink-0">
          {{ eventStats.errors }} 错误
        </Badge>
      </div>
      <Button
        variant="ghost"
        size="icon"
        class="h-7 w-7 flex-shrink-0"
        title="关闭"
        @click="emit('close')"
      >
        <X class="h-4 w-4" />
      </Button>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-hidden">
      <!-- 分子生成工具 - 使用专门的组件 -->
      <MoleculeGenStreamViewer 
        v-if="hasMoleculeGenerationTool"
        :input-smiles="moleculeProgress?.summary?.input_smiles || moleculeGenInputArgs?.smiles || ''"
        :logs="moleculeProgress?.messages?.map(m => ({ level: (m.level || 'info') as 'info' | 'warning' | 'error' | 'success', message: m.message || '', timestamp: m.timestamp })) || []"
        :current-smiles="moleculeProgress?.summary?.best_smiles || moleculeGenInputArgs?.smiles"
        :current-properties="moleculeProgress?.summary?.best_smiles ? { volume: 0, diffusion: 0, adsorption: 0, score: moleculeProgress.summary.best_score } : undefined"
        :status="(moleculeProgress?.status || 'running') as any"
        :current-layer="moleculeProgress?.summary?.current_layer"
        :total-layers="moleculeProgress?.summary?.total_layers"
        :progress="moleculeProgress?.summary?.progress"
        :summary="moleculeProgress?.summary"
        @close="emit('close')"
        @cancel="cancelMoleculeGeneration"
      />

      <!-- 通用工具调用视图 (Stepper 样式) -->
      <div v-else class="overflow-y-auto p-6 h-full">
        <div v-if="displayEvents.length === 0" class="flex flex-col items-center justify-center h-full text-muted-foreground">
          <Clock class="h-12 w-12 mb-2 opacity-50" />
          <p class="text-sm">暂无事件</p>
        </div>

        <div v-else class="stepper-container">
        <div
          v-for="(event, index) in displayEvents"
          :key="event.id"
          class="stepper-item"
        >
          <!-- 左侧：步骤指示器 -->
          <div class="stepper-indicator-wrapper">
            <div
              class="stepper-indicator"
              :class="event.data?.tool_call_error ? 'error' : ''"
            >
              <component
                :is="getEventIcon(event)"
                class="h-4 w-4"
                :class="getEventTypeColor(event.type, event.data?.tool_call_error)"
              />
            </div>
            <!-- 连接线（不是最后一个项目时显示） -->
            <div
              v-if="index < displayEvents.length - 1"
              class="stepper-connector"
            ></div>
          </div>

          <!-- 思考过程 (可折叠) -->
          <Collapsible
            v-if="event.type === 'thinking'"
            :open="eventContentOpenStates[event.id]"
            @update:open="(val) => eventContentOpenStates[event.id] = val"
          >
            <CollapsibleTrigger class="event-content-trigger">
              <div class="flex items-center gap-2 text-sm">
                <span class="font-medium">思考过程</span>
                <Badge variant="outline" class="text-xs">
                  {{ event.data.content?.length || 0 }} 字符
                </Badge>
                <ChevronDown class="h-4 w-4 transition-transform ml-auto" :class="eventContentOpenStates[event.id] ? 'rotate-180' : ''" />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent class="event-content-collapsible">
              <div class="stepper-description">
                <MarkdownRenderer :content="event.data.content" />
              </div>
            </CollapsibleContent>
          </Collapsible>

          <!-- 工具调用 (可折叠) -->
          <Collapsible
            v-if="event.type === 'tool_call'"
            :open="eventContentOpenStates[event.id]"
            @update:open="(val) => eventContentOpenStates[event.id] = val"
          >
            <CollapsibleTrigger class="tool-call-trigger">
              <div class="flex items-center gap-2 text-xs">
                <span class="font-medium">{{ event.data.function?.name }}</span>
                <Badge variant="outline" class="text-xs">
                  工具
                </Badge>
                <ChevronDown class="h-3 w-3 transition-transform ml-auto" :class="eventContentOpenStates[event.id] ? 'rotate-180' : ''" />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div class="tool-call-content">
                <!-- 参数 -->
                <div v-if="event.data.function?.arguments" class="tool-info">
                  <div class="text-xs text-muted-foreground mb-2">参数:</div>
                  <div class="tool-args">
                    <div v-for="(value, key) in parseArguments(event.data.function.arguments)" :key="key" class="arg-item">
                      <span class="arg-key">{{ key }}:</span>
                      <span class="arg-value">{{ formatArgValue(value) }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="text-muted-foreground text-xs mb-3">
                  （此工具调用没有参数）
                </div>

                <!-- 分子生成进度已在主视图中显示，此处不再重复显示 -->
                <!-- <div v-if="isMoleculeGenerationTool(event.data.function?.name)" class="molecule-progress-section">
                  <div class="text-xs text-muted-foreground mb-2">
                    生成进度:
                  </div>

                  <div v-if="moleculeProgress" class="molecule-progress-messages">
                    <div
                      v-for="(msg, idx) in moleculeProgress.messages.slice(-10)"
                      :key="idx"
                      :class="['progress-message-item', `level-${msg.level}`]"
                    >
                      <span class="message-icon">{{ getProgressMessageIcon(msg.type) }}</span>
                      <span class="message-text">{{ formatProgressMessage(msg) }}</span>
                    </div>
                  </div>
                  <div v-if="moleculeProgress?.summary" class="progress-summary mt-2 pt-2 border-t">
                    <div class="text-xs text-muted-foreground">
                      总计: {{ moleculeProgress.summary.total_generated || 0 }} 个分子，
                      {{ moleculeProgress.summary.total_results || 0 }} 个结果
                    </div>
                  </div>
                </div> -->

                <!-- 结果 -->
                <div class="tool-result-section">
                  <div class="text-xs text-muted-foreground mb-2">结果:</div>
                  <div v-if="findToolResult(event.data.id)" class="tool-result-content">
                    <MarkdownRenderer :content="formatToolResultContent(findToolResult(event.data.id)?.data?.content || '')" />
                  </div>
                  <div v-else class="tool-result-pending text-muted-foreground text-xs italic">
                    <Loader2 class="h-3 w-3 inline animate-spin mr-1" />
                    等待工具执行完成...
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          <!-- 用户消息内容 (始终显示) -->
          <div
            v-if="event.type === 'user'"
            class="stepper-description"
          >
            {{ event.data.content?.substring(0, 200) }}{{ event.data.content?.length > 200 ? '...' : '' }}
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 3D 分子查看器对话框 -->
    <Dialog v-model:open="showMoleculeViewer">
      <DialogContent class="max-w-3xl">
        <DialogHeader>
          <DialogTitle>分子结构查看器</DialogTitle>
          <DialogDescription>
            {{ selectedMolecule.smiles }}
          </DialogDescription>
        </DialogHeader>

        <!-- 2D/3D 切换按钮 -->
        <div class="flex gap-2 mb-4">
          <Button
            :variant="!show3DView ? 'default' : 'outline'"
            size="sm"
            @click="show3DView = false"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <line x1="3" y1="9" x2="21" y2="9"/>
              <line x1="9" y1="21" x2="9" y2="9"/>
            </svg>
            2D 结构
          </Button>
          <Button
            :variant="show3DView ? 'default' : 'outline'"
            size="sm"
            @click="show3DView = true"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
              <polygon points="12 2 2 7 12 12 22 7 12 2"/>
              <line x1="2" y1="17" x2="12" y2="22"/>
              <line x1="22" y1="17" x2="12" y2="22"/>
              <line x1="2" y1="12" x2="12" y2="17"/>
              <line x1="22" y1="12" x2="12" y2="17"/>
            </svg>
            3D 结构
          </Button>
        </div>

        <!-- 2D 视图: SMILES -->
        <div v-if="!show3DView" class="viewer-container">
          <div class="smiles-display">
            <div class="text-sm text-muted-foreground mb-2">SMILES:</div>
            <code class="text-lg">{{ selectedMolecule.smiles }}</code>
          </div>
          <div v-if="selectedMolecule.properties" class="properties-grid mt-4">
            <div class="prop-item">
              <span class="prop-label">体积</span>
              <span class="prop-value">{{ selectedMolecule.properties.volume?.toFixed(2) }} Å³</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">扩散系数</span>
              <span class="prop-value">{{ selectedMolecule.properties.diffusion_coeff?.toFixed(4) }}</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">吸附能</span>
              <span class="prop-value">{{ selectedMolecule.properties.adsorption_energy?.toFixed(4) }} kcal/mol</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">评分</span>
              <span class="prop-value score">{{ selectedMolecule.properties.score?.toFixed(6) }}</span>
            </div>
          </div>
        </div>

        <!-- 3D 视图 -->
        <div v-else class="viewer-container">
          <MolView
            v-if="selectedMoleculeXYZ"
            :model-content="selectedMoleculeXYZ"
            model-format="xyz"
            class="h-96"
            :embedded="true"
          />
          <div v-else class="loading-placeholder">
            <Loader2 class="h-8 w-8 animate-spin" />
            <p>生成 3D 结构中...</p>
          </div>
          <div v-if="selectedMolecule.properties" class="properties-grid mt-4">
            <div class="prop-item">
              <span class="prop-label">体积</span>
              <span class="prop-value">{{ selectedMolecule.properties.volume?.toFixed(2) }} Å³</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">扩散系数</span>
              <span class="prop-value">{{ selectedMolecule.properties.diffusion_coeff?.toFixed(4) }}</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">吸附能</span>
              <span class="prop-value">{{ selectedMolecule.properties.adsorption_energy?.toFixed(4) }} kcal/mol</span>
            </div>
            <div class="prop-item">
              <span class="prop-label">评分</span>
              <span class="prop-value score">{{ selectedMolecule.properties.score?.toFixed(6) }}</span>
            </div>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <div class="mt-4 flex justify-end">
          <Button variant="outline" @click="showMoleculeViewer = false">
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
.event-stream-viewer {
  background: linear-gradient(to bottom, hsl(var(--background)) 0%, hsl(var(--muted) / 0.3) 100%);
}

/* Stepper 容器 */
.stepper-container {
  position: relative;
  padding-left: 20px;
}

/* Stepper 项目 */
.stepper-item {
  display: flex;
  gap: 12px;
  position: relative;
  margin-bottom: 24px;
  flex: 1;
  align-items: flex-start;
  /* 顶部对齐 */
}

/* 步骤指示器包装器 */
.stepper-indicator-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
  padding-top: 0;
  /* 移除，让图标自然对齐 */
}

/* 步骤指示器圆圈 */
.stepper-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: hsl(var(--muted) / 0.5);
  border: 2px solid hsl(var(--border));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  transition: all 0.2s;
}

.stepper-indicator:hover {
  border-color: hsl(var(--primary) / 0.5);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
}

.stepper-indicator.error {
  border-color: hsl(var(--destructive) / 0.5);
  background: hsl(var(--destructive) / 0.1);
}

/* 连接线 */
.stepper-connector {
  position: absolute;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: calc(100% + 24px);
  background: hsl(var(--border));
  z-index: 1;
}

/* 类型徽章 */
.stepper-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background: hsl(var(--muted) / 0.8);
  color: hsl(var(--muted-foreground));
  border: 1px solid hsl(var(--border));
}

/* 描述文本 */
.stepper-description {
  font-size: 0.875rem;
  line-height: 1.5;
  color: hsl(var(--foreground));
}

/* 代码块 */
.stepper-code {
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.5rem;
  overflow-x: auto;
}

.stepper-code pre {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.75rem;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 工具结果 */
.stepper-result {
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  max-width: 100%;
}

.stepper-result.error {
  background: hsl(var(--destructive) / 0.1);
  border-color: hsl(var(--destructive) / 0.3);
  color: hsl(var(--destructive));
}

/* 滚动条样式 */
.event-stream-viewer::-webkit-scrollbar {
  width: 6px;
}

.event-stream-viewer::-webkit-scrollbar-track {
  background: transparent;
}

.event-stream-viewer::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.event-stream-viewer::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 推理步骤折叠样式 */
.reasoning-collapsible {
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  background: hsl(var(--accent) / 0.3);
  overflow: hidden;
  margin-top: 12px;
}

.reasoning-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
  background: none;
  border: none;
  text-align: left;
}

.reasoning-trigger:hover {
  background: hsl(var(--accent) / 0.5);
}

.reasoning-content {
  padding: 12px;
  border-top: 1px solid hsl(var(--border));
  background: hsl(var(--accent) / 0.2);
}

.reasoning-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reasoning-step {
  padding: 10px;
  border-left: 3px solid hsl(var(--primary));
  background: hsl(var(--muted) / 0.3);
  border-radius: 4px;
}

.reasoning-step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.reasoning-step-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: hsl(var(--foreground));
}

.reasoning-confidence {
  font-size: 0.75rem;
  padding: 0.15rem 0.4rem;
  background: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary));
  border-radius: 9999px;
  font-weight: 500;
}

.reasoning-step-text {
  font-size: 0.8rem;
  line-height: 1.5;
  color: hsl(var(--foreground));
  margin-top: 6px;
}

.reasoning-step-text strong {
  color: hsl(var(--primary));
  margin-right: 4px;
}

/* 分子进度卡片 */
.molecule-progress-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stepper-indicator.progress {
  border-color: hsl(var(--primary) / 0.5);
  background: hsl(var(--primary) / 0.1);
}

.stepper-badge.progress {
  background: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary));
  border-color: hsl(var(--primary) / 0.3);
}

/* 事件内容折叠样式 */
.event-content-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
}

.event-content-trigger:hover {
  background: hsl(var(--muted) / 0.5);
}

.event-content-collapsible {
  padding: 12px;
  margin-top: 8px;
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  background: hsl(var(--muted) / 0.2);
}

/* 工具调用列表样式 */
.tool-calls-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.tool-call-item {
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  background: hsl(var(--muted) / 0.1);
}

.tool-call-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
}

.tool-call-trigger:hover {
  background: hsl(var(--muted) / 0.5);
}

.tool-call-content {
  padding: 8px;
}

.tool-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
  line-height: 1.5;
}

.info-key {
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  flex-shrink: 0;
  min-width: 70px;
}

.info-value {
  color: hsl(var(--foreground));
  word-break: break-all;
}

.tool-args {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.arg-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
  line-height: 1.5;
}

.arg-key {
  font-weight: 500;
  color: hsl(var(--primary));
  flex-shrink: 0;
  min-width: 80px;
}

.arg-value {
  color: hsl(var(--foreground));
  word-break: break-all;
}

/* 分子生成进度区域 */
.molecule-progress-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid hsl(var(--border));
}

.molecule-progress-messages {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background: hsl(var(--muted) / 0.15);
  border-radius: 6px;
}

.progress-message-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  line-height: 1.4;
  padding: 4px 6px;
  border-radius: 4px;
  transition: background-color 0.15s;
}

.progress-message-item:hover {
  background: hsl(var(--muted) / 0.3);
}

.progress-message-item.level-info {
  color: hsl(var(--foreground));
}

.progress-message-item.level-success {
  color: hsl(var(--primary));
  background: hsl(var(--primary) / 0.1);
}

.progress-message-item.level-warning {
  color: hsl(var(--warning));
  background: hsl(var(--warning) / 0.1);
}

.progress-message-item.level-error {
  color: hsl(var(--destructive));
  background: hsl(var(--destructive) / 0.1);
}

.message-icon {
  flex-shrink: 0;
  font-size: 12px;
}

.message-text {
  flex: 1;
  word-break: break-word;
}

.progress-summary {
  font-size: 11px;
  color: hsl(var(--muted-foreground));
}

/* 工具结果区域 */
.tool-result-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid hsl(var(--border));
}

.tool-result-content {
  font-size: 12px;
  line-height: 1.6;
  color: hsl(var(--foreground));
  background: hsl(var(--muted) / 0.2);
  padding: 8px 12px;
  border-radius: 6px;
  max-height: 300px;
  overflow-y: auto;
}

.tool-result-pending {
  padding: 8px 12px;
  background: hsl(var(--muted) / 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

/* 流式分子生成状态 */
.stream-status {
  padding: 8px 12px;
  background: hsl(var(--muted) / 0.2);
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
}

/* 3D 分子查看器 */
.viewer-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: hsl(var(--muted-foreground));
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 12px;
  background: hsl(var(--muted) / 0.3);
  border-radius: 6px;
  margin-top: 12px;
}

.prop-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.prop-label {
  font-size: 10px;
  color: hsl(var(--muted-foreground));
  font-weight: 500;
  text-transform: uppercase;
}

.prop-value {
  font-size: 14px;
  color: hsl(var(--foreground));
  font-weight: 500;
}

.prop-value.score {
  color: hsl(var(--primary));
  font-weight: 600;
}

/* SMILES 显示 */
.smiles-display {
  padding: 16px;
  background: hsl(var(--muted) / 0.3);
  border-radius: 8px;
  text-align: center;
}

.smiles-display code {
  display: block;
  word-break: break-all;
  font-family: 'Courier New', monospace;
  color: hsl(var(--foreground));
}
</style>
