<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { RouterLink, useRouter } from 'vue-router';
import { MessageSquare, Settings as SettingsIcon, Beaker, History, Settings, Clock, ChevronRight, Pencil, Check, X, LayoutDashboard, Wrench, Cpu, Activity, FlaskConical, Store, BookOpen, FileText, Network, ListTodo, Monitor, Atom, FolderOpen, Zap, LogOut, Users, Bot, GitBranch, Trash, Ticket, Key, FileCheck } from 'lucide-vue-next';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { API_BASE_URL } from '@/config/api.config';
import ModeToggle from './ModeToggle.vue';
import UserSettings from './UserSettings.vue';
import HelpDialog from './HelpDialog.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/toast';
import { navigationConfig } from '@/config/navigation.config';
import { useTaskQueueStore } from '@/stores/taskQueue';
import { useAuthStore } from '@/stores/auth';

// 定义 emit
const emit = defineEmits<{
  selectSession: [sessionId: string, sessionName: string]
}>();

// Session 类型定义
interface Session {
  session_id: string;
  session_name: string;
  session_state: Record<string, any>;
  created_at: string;
  updated_at: string;
  agent_id?: string; // 可选的智能体 ID
}

// Agent 类型定义
interface Agent {
  id: string;
  name: string;
  db_id?: string;
  description?: string;
  instructions?: string;
  tools?: Array<{ name: string; description?: string }>;
  model?: {
    name: string;
    model: string;
    provider: string;
  };
}

interface SessionResponse {
  data: Session[];
  meta: {
    page: number;
    limit: number;
    total_pages: number;
    total_count: number;
    search_time_ms: number;
  };
}

// Session 状态管理
const sessions = ref<Session[]>([]);
const agents = ref<Agent[]>([]); // 智能体列表
const isLoadingSessions = ref(false);
const isLoadingAgents = ref(false);
const currentSessionId = ref<string>('');
const dbId = ref<string>(''); // 数据库 ID

// 重命名状态
const editingSessionId = ref<string>('');
const editingSessionName = ref<string>('');

// 删除确认状态
const sessionToDelete = ref<{ id: string; name: string } | null>(null)
const isDeleteDialogOpen = ref(false)

// 用户设置对话框引用
const userSettingsRef = ref<InstanceType<typeof UserSettings> | null>(null);

// 用户配置状态
interface UserProfile {
  name: string;
  email: string;
  avatar: string;
}

const userProfile = ref<UserProfile>({
  name: '',
  email: '',
  avatar: '',
});

// 初始化任务队列 store
const taskQueueStore = useTaskQueueStore();

// 初始化认证 store
const authStore = useAuthStore();
const router = useRouter();

// 计算是否已登录
const isLoggedIn = computed(() => authStore.isAuthenticated);

// 加载用户配置
const loadUserProfile = () => {
  const savedProfile = localStorage.getItem('userProfile');
  if (savedProfile) {
    try {
      userProfile.value = JSON.parse(savedProfile);
    } catch (error) {
      console.error('Failed to load user profile:', error);
    }
  }
};

// 获取用户名首字母
const getUserInitials = (name: string) => {
  if (!name) return 'U';
  const words = name.trim().split(/\s+/).filter(w => w.length > 0);
  if (words.length === 0) return 'U';
  if (words.length === 1 && words[0]) {
    return words[0].substring(0, 2).toUpperCase();
  }
  const first = words[0];
  const last = words[words.length - 1];
  if (first && last && first[0] && last[0]) {
    return (first[0] + last[0]).toUpperCase();
  }
  return 'U';
};

// 登出处理
const handleLogout = async () => {
  try {
    await authStore.logout();
    toast({
      title: '登出成功',
      description: '您已安全退出登录',
    });
    router.push('/');
  } catch (error) {
    console.error('登出失败:', error);
    toast({
      title: '登出失败',
      description: '请稍后重试',
      variant: 'destructive',
    });
  }
};

// 跳转到登录页
const goToLogin = () => {
  router.push('/login');
};

// 显示的用户信息（优先使用认证 store，其次使用本地配置）
const displayUserName = computed(() => {
  if (isLoggedIn.value && authStore.user) {
    return authStore.userName;
  }
  return userProfile.value.name || '用户';
});

const displayUserEmail = computed(() => {
  if (isLoggedIn.value && authStore.user) {
    return authStore.userEmail;
  }
  return userProfile.value.email || '点击设置个人信息';
});

// 打开用户设置
const openUserSettings = () => {
  userSettingsRef.value?.openSettings();
};

// 处理用户配置更新
const handleProfileUpdated = (profile: UserProfile) => {
  userProfile.value = profile;
};

// 获取 sessions 列表及其 agent_id
const fetchSessions = async () => {
  isLoadingSessions.value = true;
  try {
    // 首先获取配置以获得 db_id
    const configResponse = await fetch('/api/config');
    if (!configResponse.ok) {
      console.error('[AppSidebar] Failed to fetch config');
      return;
    }
    const config = await configResponse.json();
    dbId.value = config.databases?.[0]; // 获取第一个数据库 ID

    if (!dbId.value) {
      console.error('[AppSidebar] No database ID found in config');
      return;
    }

    // 添加查询参数：按创建时间降序排列，获取最新的会话
    // 使用自定义 API /api/custom/sessions 来支持基于 Token 的用户隔离
    const params = new URLSearchParams({
      type: 'agent',
      sort_by: 'created_at',
      sort_order: 'desc',
      limit: '5', // 增加限制以确保获取足够的会话
      db_id: dbId.value,
    });

    const response = await fetch(`${API_BASE_URL}/api/custom/sessions?${params.toString()}`);
    if (response.ok) {
      const data: SessionResponse = await response.json();
      const sessionsWithData = data.data;
      console.log('[AppSidebar] Fetched sessions:', sessionsWithData.length, sessionsWithData);

      // 后端现在直接返回 agent_id，无需额外请求
      // 按 updated_at 降序排序（最新的在最上面）
      sessions.value = sessionsWithData.sort((a, b) => {
        const dateA = new Date(a.updated_at).getTime();
        const dateB = new Date(b.updated_at).getTime();
        return dateB - dateA; // 降序：新的在前
      });
      console.log('[AppSidebar] Sessions (sorted):', sessions.value);
      console.log('[AppSidebar] First session agent_id:', sessions.value[0]?.agent_id);
    } else {
      const errorText = await response.text();
      console.error('[AppSidebar] Failed to fetch sessions:', response.status, response.statusText, errorText);
    }
  } catch (error) {
    console.error('[AppSidebar] Error fetching sessions:', error);
  } finally {
    isLoadingSessions.value = false;
  }
};

// 获取 agents 列表
const fetchAgents = async () => {
  isLoadingAgents.value = true;
  try {
    const response = await fetch('/api/agents');
    if (response.ok) {
      const data = await response.json();
      agents.value = data;
      console.log('[AppSidebar] Fetched agents:', agents.value.length, agents.value);
    } else {
      console.error('[AppSidebar] Failed to fetch agents:', response.statusText);
    }
  } catch (error) {
    console.error('[AppSidebar] Error fetching agents:', error);
  } finally {
    isLoadingAgents.value = false;
  }
};

// 根据 agent_id 获取智能体信息
const getAgentById = (agentId?: string): Agent | undefined => {
  if (!agentId) return undefined;
  return agents.value.find(agent => agent.id === agentId);
};

// 获取智能体的显示标识（第一个字符）
const getAgentBadge = (agentId?: string): { char: string; color: string; tooltip: string } => {
  const agent = getAgentById(agentId);
  if (!agent) {
    return { char: '通', color: 'bg-gray-500', tooltip: '通用智能体' }; // 默认灰色
  }

  // 根据智能体名称生成标识字符
  let char = agent.name.charAt(0);

  // 特殊处理：电镀智能体显示"镀"而不是"电"
  if (agent.name.includes('电镀')) {
    char = '镀';
  }

  // 根据不同的智能体返回不同的颜色
  const colorMap: Record<string, string> = {
    '镀': 'bg-blue-500', // 电镀
    '通': 'bg-green-500', // 通用
    '化': 'bg-purple-500', // 化学
    '物': 'bg-orange-500', // 物理
    '材': 'bg-pink-500', // 材料
  };

  // 查找匹配的颜色
  const color = Object.keys(colorMap).find(key => agent.name.includes(key))
    ? colorMap[Object.keys(colorMap).find(key => agent.name.includes(key))!]
    : 'bg-gray-500';

  // 生成 tooltip 提示文本
  const tooltip = `这是${agent.name}`;

  return { char, color, tooltip };
};

// 选择 session
const selectSession = (sessionId: string, sessionName: string) => {
  currentSessionId.value = sessionId;
  // 发射事件给父组件
  emit('selectSession', sessionId, sessionName);
};

// 格式化时间
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天';
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
  }
};

// 开始重命名
const startRename = (sessionId: string, currentName: string) => {
  editingSessionId.value = sessionId;
  editingSessionName.value = currentName;
};

// 取消重命名
const cancelRename = () => {
  editingSessionId.value = '';
  editingSessionName.value = '';
};

// 保存重命名
const saveRename = async (sessionId: string) => {
  if (!editingSessionName.value.trim()) {
    toast({
      title: '错误',
      description: '会话名称不能为空',
      variant: 'destructive',
    });
    return;
  }

  try {
    const params = new URLSearchParams({
      type: 'agent',
      db_id: dbId.value,
    });
    const response = await fetch(`${API_BASE_URL}/api/sessions/${sessionId}/rename?${params.toString()}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_name: editingSessionName.value.trim(),
      }),
    });

    if (response.ok) {
      // 更新本地 session 名称
      const session = sessions.value.find(s => s.session_id === sessionId);
      if (session) {
        session.session_name = editingSessionName.value.trim();
      }
      
      toast({
        title: '成功',
        description: '会话已重命名',
      });
      
      // 清除编辑状态
      cancelRename();
    } else {
      const errorData = await response.json().catch(() => ({}));
      toast({
        title: '重命名失败',
        description: errorData.detail || '请稍后重试',
        variant: 'destructive',
      });
    }
  } catch (error) {
    console.error('[AppSidebar] Error renaming session:', error);
    toast({
      title: '网络错误',
      description: '无法连接到服务器',
      variant: 'destructive',
    });
  }
};

// 准备删除会话
const prepareDeleteSession = (sessionId: string, sessionName: string) => {
  sessionToDelete.value = { id: sessionId, name: sessionName }
  isDeleteDialogOpen.value = true
}

// 确认删除会话
const confirmDeleteSession = async () => {
  if (!sessionToDelete.value) return
  
  const { id: sessionId, name: sessionName } = sessionToDelete.value
  
  try {
    const params = new URLSearchParams({
      type: 'agent',
      db_id: dbId.value,
    });
    
    // 使用 custom/sessions 路径
    const response = await fetch(`${API_BASE_URL}/api/custom/sessions/${sessionId}?${params.toString()}`, {
      method: 'DELETE',
    });

    if (response.ok) {
      // 从列表中移除
      sessions.value = sessions.value.filter(s => s.session_id !== sessionId);
      
      // 如果删除的是当前会话，清除选中状态或选择最新的
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = '';
        if (sessions.value.length > 0) {
          selectSession(sessions.value[0].session_id, sessions.value[0].session_name);
        } else {
            // TODO: 跳转到新建会话页或清空聊天
            emit('selectSession', '', '');
        }
      }

      toast({
        title: '删除成功',
        description: `会话 "${sessionName}" 已删除`,
      });
    } else {
      const errorData = await response.json().catch(() => ({}));
      toast({
        title: '删除失败',
        description: errorData.detail || '请稍后重试',
        variant: 'destructive',
      });
    }
  } catch (error) {
    console.error('[AppSidebar] Error deleting session:', error);
    toast({
      title: '网络错误',
      description: '无法连接到服务器',
      variant: 'destructive',
    });
  } finally {
    isDeleteDialogOpen.value = false
    sessionToDelete.value = null
  }
};

// 组件挂载时获取 sessions
onMounted(() => {
  fetchSessions();
  fetchAgents(); // 获取智能体列表
  loadUserProfile();

  // 监听 localStorage 变化（用于用户设置更新）
  window.addEventListener('storage', (e) => {
    if (e.key === 'userProfile' && e.newValue) {
      try {
        userProfile.value = JSON.parse(e.newValue);
      } catch (error) {
        console.error('Failed to parse user profile:', error);
      }
    }
  });
});

// 暴露方法供父组件调用
defineExpose({
  fetchSessions,
  refreshSessions: fetchSessions, // 别名，更语义化
  getSessions: () => sessions.value, // 暴露当前 sessions 数据
});
</script>

<template>
  <Sidebar collapsible="icon">
    <!-- Header -->
    <SidebarHeader>
      <SidebarMenu>
        <!-- 已登录用户信息 -->
        <SidebarMenuItem v-if="isLoggedIn">
          <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
            <Avatar class="size-8 rounded-lg">
              <AvatarImage :src="userProfile.avatar" :alt="displayUserName" />
              <AvatarFallback class="rounded-lg">
                {{ getUserInitials(displayUserName) }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ displayUserName }}</span>
              <span class="truncate text-xs">{{ displayUserEmail }}</span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              class="ml-auto -mr-2 h-8 w-8"
              @click="handleLogout"
              title="登出"
            >
              <LogOut class="h-4 w-4" />
            </Button>
          </SidebarMenuButton>
        </SidebarMenuItem>

        <!-- 未登录提示 -->
        <SidebarMenuItem v-else>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            @click="goToLogin"
          >
            <Avatar class="size-8 rounded-lg">
              <AvatarFallback class="rounded-lg bg-primary/10 text-primary">
                ?
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">未登录</span>
              <span class="truncate text-xs text-muted-foreground">点击登录</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>

    <!-- Content -->
    <SidebarContent>
      <!-- 功能导航 -->
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu>
            <!-- 对话 -->
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <RouterLink to="/chat">
                  <MessageSquare />
                  <span>对话</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 智能体配置 -->
            <SidebarMenuItem>
              <SidebarMenuButton v-if="authStore.isSuperuser" asChild>
                <RouterLink to="/agent-config">
                  <Bot />
                  <span>智能体配置</span>
                </RouterLink>
              </SidebarMenuButton>
              <SidebarMenuButton v-else disabled class="opacity-50 cursor-not-allowed">
                <Bot />
                <span>智能体配置</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 模型配置 -->
            <SidebarMenuItem v-if="!navigationConfig.modelConfig.hidden">
              <SidebarMenuButton asChild>
                <RouterLink to="/model-config">
                  <SettingsIcon />
                  <span>模型配置</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 用户管理（可折叠，仅管理员） -->
            <Collapsible
              v-if="authStore.isSuperuser"
              as-child
              class="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton tooltip="用户管理">
                    <Users />
                    <span>用户管理</span>
                    <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/users">
                          <Users />
                          <span>账号管理</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/invite-codes">
                          <Ticket />
                          <span>邀请码</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/invite-audit">
                          <FileCheck />
                          <span>申请审核</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <!-- 工具广场 -->
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <RouterLink to="/tools">
                  <Store />
                  <span>工具广场</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- Workflow -->
            <SidebarMenuItem>
              <SidebarMenuButton v-if="authStore.isSuperuser" asChild>
                <RouterLink to="/workflow">
                  <GitBranch />
                  <span>Workflow</span>
                </RouterLink>
              </SidebarMenuButton>
              <SidebarMenuButton v-else disabled class="opacity-50 cursor-not-allowed">
                <GitBranch />
                <span>Workflow</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 任务队列 -->
            <SidebarMenuItem>
              <SidebarMenuButton disabled class="opacity-50 cursor-not-allowed">
                <ListTodo />
                <span>任务队列</span>
                <Badge
                  v-if="taskQueueStore.activeTasks.length > 0"
                  variant="default"
                  class="ml-auto text-xs"
                >
                  {{ taskQueueStore.activeTasks.length }}
                </Badge>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 仪表盘（可折叠） -->
            <Collapsible
              v-if="authStore.isSuperuser"
              as-child
              class="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton tooltip="仪表盘">
                    <LayoutDashboard />
                    <span>仪表盘</span>
                    <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem v-if="!navigationConfig.dashboard.toolCalls.hidden">
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/dashboard/tool-calls">
                          <Wrench />
                          <span>工具调用</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem v-if="!navigationConfig.dashboard.modelCalls.hidden">
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/dashboard/model-calls">
                          <Cpu />
                          <span>大模型调用</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem v-if="!navigationConfig.dashboard.apiAlive.hidden">
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/dashboard/api-alive">
                          <Activity />
                          <span>API-Alive</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem v-if="!navigationConfig.dashboard.systemMonitor.hidden">
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/dashboard/system-monitor">
                          <Monitor />
                          <span>系统监控</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <SidebarMenuItem v-if="!authStore.isSuperuser">
              <SidebarMenuButton disabled class="opacity-50 cursor-not-allowed">
                <LayoutDashboard />
                <span>仪表盘</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 添加剂库（可折叠） -->
            <Collapsible
              as-child
              default-open
              class="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton tooltip="添加剂库">
                    <Beaker />
                    <span>添加剂库</span>
                    <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/molecules">
                          <Atom />
                          <span>分子库</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/commercial">
                          <FlaskConical />
                          <span>商业化库</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/adsorption">
                          <Zap />
                          <span>吸附能库</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <!-- 文献库（可折叠） -->
            <Collapsible
              v-if="authStore.isSuperuser"
              as-child
              class="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton tooltip="文献库">
                    <BookOpen />
                    <span>文献库</span>
                    <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/literature/pdf">
                          <FileText />
                          <span>PDF文献</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem>
                      <SidebarMenuSubButton asChild>
                        <RouterLink to="/literature/graph">
                          <Network />
                          <span>知识图谱</span>
                        </RouterLink>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>

            <SidebarMenuItem v-if="!authStore.isSuperuser">
              <SidebarMenuButton disabled class="opacity-50 cursor-not-allowed">
                <BookOpen />
                <span>文献库</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 资料库 -->
            <SidebarMenuItem v-if="authStore.isSuperuser">
              <SidebarMenuButton asChild>
                <RouterLink to="/storage">
                  <FolderOpen />
                  <span>资料库</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem v-else>
              <SidebarMenuButton disabled class="opacity-50 cursor-not-allowed">
                <FolderOpen />
                <span>资料库</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- Demo -->
            <SidebarMenuItem v-if="!navigationConfig.demo.hidden">
              <SidebarMenuButton asChild>
                <RouterLink to="/demo">
                  <FlaskConical />
                  <span>Demo</span>
                </RouterLink>
              </SidebarMenuButton>
            </SidebarMenuItem>

            <!-- 历史记录（可折叠，包含会话列表） -->
            <Collapsible
              as-child
              default-open
              class="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger as-child>
                  <SidebarMenuButton tooltip="历史记录">
                    <History />
                    <span>历史记录</span>
                    <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    <SidebarMenuSubItem v-if="isLoadingSessions">
                      <SidebarMenuSubButton disabled>
                        <Clock class="animate-spin" />
                        <span>加载中...</span>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem v-else-if="sessions.length === 0">
                      <SidebarMenuSubButton disabled>
                        <Clock />
                        <span>暂无会话</span>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                    <SidebarMenuSubItem v-else v-for="session in sessions" :key="session.session_id">
                      <!-- 编辑模式 -->
                      <div v-if="editingSessionId === session.session_id" class="flex items-center gap-1 px-2 py-1.5">
                        <Input
                          v-model="editingSessionName"
                          class="h-7 text-sm flex-1"
                          @keyup.enter="saveRename(session.session_id)"
                          @keyup.esc="cancelRename"
                          autofocus
                        />
                        <Button
                          size="icon"
                          variant="ghost"
                          class="h-7 w-7 shrink-0"
                          @click="saveRename(session.session_id)"
                        >
                          <Check class="h-4 w-4" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          class="h-7 w-7 shrink-0"
                          @click="cancelRename"
                        >
                          <X class="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <!-- 正常显示模式 -->
                      <div v-else class="group/item flex items-center gap-1">
                        <!-- 智能体标识 -->
                        <div
                          v-if="session.agent_id"
                          :class="['flex-shrink-0 w-5 h-5 rounded flex items-center justify-center text-xs font-semibold text-white', getAgentBadge(session.agent_id).color]"
                          :title="getAgentBadge(session.agent_id).tooltip"
                        >
                          {{ getAgentBadge(session.agent_id).char }}
                        </div>

                        <SidebarMenuSubButton
                          @click="selectSession(session.session_id, session.session_name)"
                          :isActive="currentSessionId === session.session_id"
                          class="flex-1"
                        >
                          <span class="truncate flex-1">{{ session.session_name }}</span>
                          <span class="text-xs text-muted-foreground">
                            {{ formatDate(session.updated_at) }}
                          </span>
                        </SidebarMenuSubButton>
                        <div class="opacity-0 group-hover/item:opacity-100 transition-opacity flex items-center -gap-1">
                          <Button
                            size="icon"
                            variant="ghost"
                            class="h-6 w-6 shrink-0"
                            @click.stop="startRename(session.session_id, session.session_name)"
                            title="重命名"
                          >
                            <Pencil class="h-3 w-3" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            class="h-6 w-6 shrink-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                            @click.stop="prepareDeleteSession(session.session_id, session.session_name)"
                            title="删除"
                          >
                            <Trash class="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>

    <!-- Footer -->
    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton @click="openUserSettings">
            <Settings />
            <span>设置</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarMenuItem>
          <HelpDialog variant="sidebar" />
        </SidebarMenuItem>
        <SidebarMenuItem>
          <ModeToggle />
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>

    <SidebarRail />
    
    <!-- 用户设置对话框 -->
    <UserSettings ref="userSettingsRef" @profile-updated="handleProfileUpdated" />

    <!-- 删除确认 Alert Dialog -->
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除会话?</AlertDialogTitle>
          <AlertDialogDescription>
            确定要删除会话 "{{ sessionToDelete?.name }}" 吗？此操作无法撤销，会话记录将永久丢失。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction 
            @click="confirmDeleteSession"
            class="bg-destructive hover:bg-destructive/90"
          >
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </Sidebar>
</template>
