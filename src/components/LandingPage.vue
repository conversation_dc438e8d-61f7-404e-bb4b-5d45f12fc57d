<template>
  <div class="landing-page">
    <!-- 背景动画 -->
    <div class="bg-animation">
      <!-- 网格线 -->
      <div class="grid-lines"></div>
      <!-- 动态光晕层 -->
      <div class="aurora-layer">
        <div class="aurora aurora-1"></div>
        <div class="aurora aurora-2"></div>
        <div class="aurora aurora-3"></div>
      </div>
      <!-- 分子点阵 -->
      <div class="molecule-grid">
        <div v-for="i in 60" :key="i" class="molecule-dot" :style="getMoleculeStyle(i)"></div>
      </div>
      <!-- 光线条 -->
      <div class="light-beams">
        <div class="beam beam-1"></div>
        <div class="beam beam-2"></div>
        <div class="beam beam-3"></div>
      </div>
      <!-- 渐变光球 -->
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
      <!-- 六边形网格 -->
      <div class="hex-pattern"></div>
      <!-- 闪烁光点 -->
      <div class="sparkle-container">
        <div v-for="i in 20" :key="'sparkle-'+i" class="sparkle" :style="getSparkleStyle(i)"></div>
      </div>
    </div>

    <!-- 导航栏 -->
    <nav class="landing-nav">
      <div class="nav-logo">
        <img :src="logoSrc" alt="EAGENT" class="logo-image" />
        <span class="logo-text">EAGENT</span>
        <span class="logo-badge">Beta</span>
      </div>
      <Button @click="enterApp" class="nav-cta">
        <Rocket class="mr-2 h-4 w-4" />
        进入应用
      </Button>
    </nav>

    <!-- 底部毛玻璃信息条 -->
    <div class="glass-banners">
      <div class="glass-banner banner-bottom">
        <div class="banner-content">
          <span class="banner-text">⚡ 添加剂智能筛选</span>
          <span class="banner-divider">|</span>
          <span class="banner-text">🔋 性能预测</span>
          <span class="banner-divider">|</span>
          <span class="banner-text">✨ 配方优化</span>
          <span class="banner-divider">|</span>
          <span class="banner-text">🧬 分子设计</span>
        </div>
      </div>
    </div>

    <!-- 主内容区 - 单屏 -->
    <main class="main-content">
      <!-- 左侧：标题和行动按钮 -->
      <div class="content-left">
        <div class="hero-badge">
          <div class="badge-pulse"></div>
          <Cpu class="h-4 w-4" />
          <span>电镀添加剂智能筛选 ·  AI 助手</span>
        </div>
        
        <h1 class="hero-title">
          <span class="title-line">智能加速</span>
          <span class="title-line gradient-text">电镀配方研发</span>
        </h1>
        
        <p class="hero-description">
          专为电镀行业打造的 AI 研发助手。预测各类添加剂的表面行以及与性能参数。
          智能筛选高性能分子，加速配方研发与工艺优化。
        </p>
        
        <div class="hero-actions">
          <Button size="lg" @click="enterApp" class="primary-cta">
            <Zap class="mr-2 h-5 w-5" />
            立即体验
            <ArrowRight class="ml-2 h-4 w-4 arrow-icon" />
          </Button>
        </div>

        <!-- 快速统计 -->
        <div class="quick-stats">
          <div class="stat-item">
            <span class="stat-value">海量体系</span>
            <span class="stat-label">电镀类型</span>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <span class="stat-value">SaaS计算</span>
            <span class="stat-label">智算加速平台</span>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <span class="stat-value">近毫秒级</span>
            <span class="stat-label">性质预测</span>
          </div>
        </div>
      </div>

      <!-- 右侧：功能亮点卡片 -->
      <div class="content-right">
        <div class="feature-cards">
          <div class="feature-card" v-for="(feature, index) in quickFeatures" :key="feature.title"
            :style="{ animationDelay: `${index * 0.1}s` }">
            <div class="feature-icon" :style="{ background: feature.iconBg }">
              <component :is="feature.icon" class="h-5 w-5" :style="{ color: feature.iconColor }" />
            </div>
            <div class="feature-info">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 底部信息 -->
    <footer class="landing-footer">
      <p>© 2025 EAGENT · Powered by Vue 3 + Agno</p>
    </footer>

    <!-- 登录/注册模态框 -->
    <Transition name="modal-fade">
      <div v-if="showLoginModal" class="login-modal-overlay" @click.self="!isInviteRequestDrawerOpen && closeLoginModal()">
        <div class="login-modal-card" :class="{ 'drawer-open': isInviteRequestDrawerOpen }">
          <!-- 关闭按钮 -->
          <button class="modal-close-btn" @click="closeLoginModal">
            <X class="h-5 w-5" />
          </button>

          <!-- 装饰背景 -->
          <div class="modal-bg-glow"></div>
          <div class="modal-grid-lines"></div>

          <!-- 登录表单 -->
          <div v-if="!isRegisterMode">
            <div class="login-header">
              <div class="login-logo-wrapper">
                <img :src="logoSrc" alt="EAGENT" class="login-logo-icon" />
              </div>
              <h2 class="login-title">欢迎回来</h2>
              <p class="login-subtitle">登录 EAGENT 开启智能研发之旅</p>
            </div>

            <form @submit.prevent="handleLogin" class="login-form">
              <!-- 用户名或邮箱 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <User class="h-4 w-4" />
                  </div>
                  <Input
                    id="username"
                    v-model="loginForm.username_or_email"
                    type="text"
                    placeholder="用户名 / 邮箱"
                    autocomplete="username"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                </div>
              </div>

              <!-- 密码 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <Lock class="h-4 w-4" />
                  </div>
                  <Input
                    id="password"
                    v-model="loginForm.password"
                    :type="showPassword ? 'text' : 'password'"
                    placeholder="密码"
                    autocomplete="current-password"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                  <button
                    type="button"
                    class="password-toggle-btn"
                    @click="showPassword = !showPassword"
                  >
                    <Eye v-if="!showPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                  </button>
                </div>
              </div>

              <!-- 记住我 & 忘记密码 -->
              <div class="form-options">
                <label class="checkbox-label">
                  <input type="checkbox" class="custom-checkbox" />
                  <span>记住我</span>
                </label>
                <a href="#" class="forgot-password">忘记密码?</a>
              </div>

              <!-- 登录按钮 -->
              <Button
                type="submit"
                class="login-submit-btn"
                :disabled="isLoading"
              >
                <div class="btn-content">
                  <span v-if="isLoading">登录中...</span>
                  <span v-else>立即登录</span>
                  <ArrowRight v-if="!isLoading" class="ml-2 h-4 w-4" />
                </div>
                <div class="btn-glow"></div>
              </Button>

              <!-- 注册链接 -->
              <div class="register-prompt">
                还没有账号？
                <button type="button" @click="showRegisterForm" class="register-link-btn">
                  立即注册
                </button>
              </div>
            </form>
          </div>

          <!-- 注册表单 -->
          <div v-else>
            <div class="login-header">
              <div class="login-logo-wrapper">
                <img :src="logoSrc" alt="EAGENT" class="login-logo-icon" />
              </div>
              <h2 class="login-title">创建账号</h2>
              <p class="login-subtitle">加入 E-Agent 开启智能研发之旅</p>
            </div>

            <form @submit.prevent="handleRegister" class="login-form">
              <!-- 用户名 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <User class="h-4 w-4" />
                  </div>
                  <Input
                    id="reg-username"
                    v-model="registerForm.username"
                    type="text"
                    placeholder="用户名"
                    autocomplete="username"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                </div>
              </div>

              <!-- 邮箱 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <Mail class="h-4 w-4" />
                  </div>
                  <Input
                    id="reg-email"
                    v-model="registerForm.email"
                    type="email"
                    placeholder="邮箱地址"
                    autocomplete="email"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                </div>
              </div>

              <!-- 密码 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <Lock class="h-4 w-4" />
                  </div>
                  <Input
                    id="reg-password"
                    v-model="registerForm.password"
                    :type="showRegisterPassword ? 'text' : 'password'"
                    placeholder="密码（至少6位）"
                    autocomplete="new-password"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                  <button
                    type="button"
                    class="password-toggle-btn"
                    @click="showRegisterPassword = !showRegisterPassword"
                  >
                    <Eye v-if="!showRegisterPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                  </button>
                </div>
              </div>

              <!-- 确认密码 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <Lock class="h-4 w-4" />
                  </div>
                  <Input
                    id="reg-confirm-password"
                    v-model="registerForm.confirmPassword"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    placeholder="确认密码"
                    autocomplete="new-password"
                    :disabled="isLoading"
                    class="custom-input"
                  />
                  <button
                    type="button"
                    class="password-toggle-btn"
                    @click="showConfirmPassword = !showConfirmPassword"
                  >
                    <Eye v-if="!showConfirmPassword" class="h-4 w-4" />
                    <EyeOff v-else class="h-4 w-4" />
                  </button>
                </div>
              </div>

              <!-- 邀请码 -->
              <div class="form-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z"/>
                      <path d="M13 5v2"/>
                      <path d="M13 17v2"/>
                      <path d="M13 11v2"/>
                    </svg>
                  </div>
                  <Input
                    id="reg-invite-code"
                    v-model="registerForm.invite_code"
                    type="text"
                    placeholder="邀请码"
                    :disabled="isLoading"
                    class="custom-input uppercase"
                  />
                  <button
                    type="button"
                    @click="showInviteRequestDrawer"
                    class="question-button"
                    title="联系我们以获得邀请码！"
                  >
                    <HelpCircle class="h-5 w-5" />
                  </button>
                </div>
                <p class="form-hint">请输入邀请码（不区分大小写）</p>
              </div>

              <!-- 注册按钮 -->
              <Button
                type="submit"
                class="login-submit-btn"
                :disabled="isLoading"
              >
                <div class="btn-content">
                  <span v-if="isLoading">注册中...</span>
                  <span v-else>立即注册</span>
                  <ArrowRight v-if="!isLoading" class="ml-2 h-4 w-4" />
                </div>
                <div class="btn-glow"></div>
              </Button>

              <!-- 登录链接 -->
              <div class="register-prompt">
                已有账号？
                <button type="button" @click="showLoginForm" class="register-link-btn">
                  立即登录
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 邀请码申请抽屉 -->
    <Transition name="drawer-fade">
      <div v-if="isInviteRequestDrawerOpen" class="drawer-overlay" @click="closeInviteRequestDrawer"></div>
    </Transition>

    <Transition name="drawer-slide">
      <div v-if="isInviteRequestDrawerOpen" class="invite-request-drawer">
        <div class="drawer-header">
          <h2 class="drawer-title">申请邀请码</h2>
          <button type="button" @click="closeInviteRequestDrawer" class="drawer-close">
            <X class="w-5 h-5" />
          </button>
        </div>

        <div class="drawer-content">
          <p class="drawer-description">
            请填写以下信息，我们会尽快审核并发放邀请码。
          </p>

          <form @submit.prevent="handleInviteRequestSubmit" class="invite-request-form">
            <div class="form-field">
              <label for="request-name">称谓 *</label>
              <Input
                id="request-name"
                v-model="inviteRequestForm.name"
                type="text"
                placeholder="我们该怎么称呼您？"
                required
              />
            </div>

            <div class="form-field">
              <label for="request-email">邮箱 *</label>
              <Input
                id="request-email"
                v-model="inviteRequestForm.email"
                type="email"
                placeholder="邀请码会发送到您的邮箱"
                required
              />
            </div>

            <div class="form-field">
              <label for="request-reason">申请原因</label>
              <textarea
                id="request-reason"
                v-model="inviteRequestForm.reason"
                placeholder="如愿意，简述您的试用需求，例如：研究用途、商业合作等"
                rows="4"
                class="request-textarea"
              ></textarea>
            </div>

            <div class="form-field">
              <label for="request-affiliation">所属机构（可选）</label>
              <Input
                id="request-affiliation"
                v-model="inviteRequestForm.affiliation"
                type="text"
                placeholder="请输入您的所属机构或公司"
              />
            </div>

            <Button type="submit" class="submit-request-btn" :disabled="isSubmittingRequest">
              {{ isSubmittingRequest ? '提交中...' : '提交申请' }}
            </Button>
          </form>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ArrowRight, Zap, Database, Brain, Cpu, Rocket, Eye, EyeOff, X, User, Lock, Mail, FlaskConical as Beaker, HelpCircle
} from 'lucide-vue-next';
import { useAuthStore } from '@/stores/auth';
import { toast } from 'vue-sonner';

const router = useRouter();
const authStore = useAuthStore();

// Logo 路径
const logoSrc = '/apps/eagent/ea-large.png';

// 登录模态框状态
const showLoginModal = ref(false);
const showPassword = ref(false);
const showRegisterPassword = ref(false);
const showConfirmPassword = ref(false);
const isRegisterMode = ref(false);

// 登录表单数据
const loginForm = ref({
  username_or_email: '',
  password: '',
});

// 注册表单数据
const registerForm = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  invite_code: '',
});

const isLoading = ref(false);

// 邀请码申请抽屉状态
const isInviteRequestDrawerOpen = ref(false);
const isSubmittingRequest = ref(false);
const inviteRequestForm = ref({
  name: '',
  email: '',
  reason: '',
  affiliation: '',
});

// 打开登录模态框
const enterApp = () => {
  // 检查登录状态
  if (authStore.isAuthenticated) {
    // 已登录，直接进入聊天页面
    router.push('/chat');
  } else {
    // 未登录，打开登录模态框
    showLoginModal.value = true;
    isRegisterMode.value = false;
  }
};

// 关闭登录模态框
const closeLoginModal = () => {
  showLoginModal.value = false;
  isRegisterMode.value = false;
  loginForm.value = {
    username_or_email: '',
    password: '',
  };
  registerForm.value = {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    invite_code: '',
  };
};

// 显示注册表单
const showRegisterForm = () => {
  isRegisterMode.value = true;
  loginForm.value = {
    username_or_email: '',
    password: '',
  };
};

// 显示登录表单
const showLoginForm = () => {
  isRegisterMode.value = false;
  registerForm.value = {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    invite_code: '',
  };
};

// 提交登录
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.value.username_or_email.trim()) {
    toast.error('请输入用户名或邮箱');
    return;
  }

  if (!loginForm.value.password) {
    toast.error('请输入密码');
    return;
  }

  if (loginForm.value.password.length < 6) {
    toast.error('密码至少需要 6 个字符');
    return;
  }

  try {
    isLoading.value = true;
    await authStore.login({
      username_or_email: loginForm.value.username_or_email.trim(),
      password: loginForm.value.password,
    });

    toast.success('登录成功！');

    // 关闭模态框
    closeLoginModal();

    // 跳转到聊天页面
    router.push('/chat');
  } catch (error: any) {
    console.error('登录失败:', error);
    toast.error(error.message || '登录失败，请检查用户名和密码');
  } finally {
    isLoading.value = false;
  }
};

// 提交注册
const handleRegister = async () => {
  // 表单验证
  if (!registerForm.value.username.trim()) {
    toast.error('请输入用户名');
    return;
  }

  if (!registerForm.value.email.trim()) {
    toast.error('请输入邮箱');
    return;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(registerForm.value.email)) {
    toast.error('请输入有效的邮箱地址');
    return;
  }

  if (!registerForm.value.password) {
    toast.error('请输入密码');
    return;
  }

  if (registerForm.value.password.length < 6) {
    toast.error('密码至少需要 6 个字符');
    return;
  }

  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    toast.error('两次输入的密码不一致');
    return;
  }

  try {
    isLoading.value = true;
    await authStore.register({
      username: registerForm.value.username.trim(),
      email: registerForm.value.email.trim(),
      password: registerForm.value.password,
      invite_code: registerForm.value.invite_code.trim().toUpperCase() || undefined,
    });

    toast.success('注册成功！正在登录...');

    // 关闭模态框
    closeLoginModal();

    // 跳转到聊天页面
    router.push('/chat');
  } catch (error: any) {
    console.error('注册失败:', error);
    toast.error(error.message || '注册失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};

const getMoleculeStyle = (index: number) => {
  const row = Math.floor(index / 10);
  const col = index % 10;
  const baseLeft = col * 10 + Math.random() * 5;
  const baseTop = row * 12.5 + Math.random() * 8;
  const moveX = (Math.random() - 0.5) * 120;
  const moveY = (Math.random() - 0.5) * 120;
  return {
    left: `${baseLeft}%`,
    top: `${baseTop}%`,
    '--move-x': `${moveX}px`,
    '--move-y': `${moveY}px`,
    animationDelay: `${Math.random() * 8}s`,
    animationDuration: `${15 + Math.random() * 20}s`,
  };
};

const getSparkleStyle = (_index: number) => {
  return {
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    animationDelay: `${Math.random() * 5}s`,
    animationDuration: `${2 + Math.random() * 3}s`,
    '--sparkle-scale': 0.5 + Math.random() * 1,
  };
};

const quickFeatures = [
  {
    icon: Brain,
    iconBg: 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.05) 100%)',
    iconColor: '#3b82f6',
    title: '吸附能/扩散系数预测',
    description: '基于深度学习模型预测添加剂分子在金属表面的吸附与扩散行为',
  },
  {
    icon: Database,
    iconBg: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2) 0%, rgba(34, 211, 238, 0.05) 100%)',
    iconColor: '#22d3ee',
    title: '电镀文献分子提取',
    description: '从电镀论文专利中自动提取添加剂分子结构与性能数据',
  },
  {
    icon: Beaker,
    iconBg: 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.05) 100%)',
    iconColor: '#60a5fa',
    title: '添加剂配方设计',
    description: '根据目标性能筛选最优分子组合，辅助电镀添加剂配方设计',
  },
];

// 邀请码申请抽屉方法
const showInviteRequestDrawer = () => {
  isInviteRequestDrawerOpen.value = true;
};

const closeInviteRequestDrawer = () => {
  isInviteRequestDrawerOpen.value = false;
  inviteRequestForm.value = {
    name: '',
    email: '',
    reason: '',
    affiliation: '',
  };
};

const handleInviteRequestSubmit = async () => {
  try {
    isSubmittingRequest.value = true;

    const apiBase = import.meta.env.VITE_API_BASE_URL || '';
      
    const response = await fetch(`${apiBase}/api/invite-codes/applications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: inviteRequestForm.value.name,
        email: inviteRequestForm.value.email,
        reason: inviteRequestForm.value.reason,
        affiliation: inviteRequestForm.value.affiliation,
      }),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '提交申请失败');
    }

    toast.success('申请已提交！我们会尽快审核并联系您。');
    closeInviteRequestDrawer();
  } catch (error: any) {
    console.error('提交邀请码申请失败:', error);
    toast.error(error.message || '提交失败，请稍后重试');
  } finally {
    isSubmittingRequest.value = false;
  }
};
</script>

<style scoped>
/* ===== 变量定义 ===== */
.landing-page {
  /* 主色：科技蓝 */
  --color-primary: #3b82f6;
  --color-primary-light: #60a5fa;
  --color-primary-dark: #2563eb;
  /* 辅色：青色 */
  --color-accent: #06b6d4;
  --color-accent-light: #22d3ee;
  --color-accent-dark: #0891b2;
  /* 第三色：浅蓝 */
  --color-tertiary: #3b82f6;
  --color-tertiary-light: #60a5fa;

  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  color: #e2e8f0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* ===== 背景图片 ===== */
.landing-page::before {
  content: '';
  position: fixed;
  inset: 0;
  background-image: url('/air.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.7;
  z-index: 0;
}

/* 左侧渐变遮罩层 */
.landing-page::after {
  content: '';
  position: fixed;
  inset: 0;
  background: linear-gradient(to right,
    rgba(15, 23, 42, 0.85) 0%,
    rgba(15, 23, 42, 0.75) 25%,
    rgba(15, 23, 42, 0.5) 45%,
    rgba(15, 23, 42, 0.2) 65%,
    rgba(15, 23, 42, 0.05) 100%
  );
  z-index: 1;
  pointer-events: none;
}

/* ===== 背景动画 ===== */
.bg-animation {
  position: fixed;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 0;
  background:
    radial-gradient(ellipse at 20% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(6, 182, 212, 0.06) 0%, transparent 50%),
    radial-gradient(ellipse at 50% 50%, rgba(59, 130, 246, 0.04) 0%, transparent 60%);
}

.grid-lines {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.06) 1px, transparent 1px);
  background-size: 50px 50px;
  mask-image: radial-gradient(ellipse at center, black 0%, transparent 70%);
  animation: gridPulse 10s ease-in-out infinite;
}

@keyframes gridPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 极光层 */
.aurora-layer {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.aurora {
  position: absolute;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0%,
    rgba(59, 130, 246, 0.12) 10%,
    transparent 20%,
    rgba(6, 182, 212, 0.1) 30%,
    transparent 40%,
    rgba(59, 130, 246, 0.08) 50%,
    transparent 60%,
    rgba(6, 182, 212, 0.08) 70%,
    transparent 80%,
    rgba(59, 130, 246, 0.1) 90%,
    transparent 100%
  );
  filter: blur(80px);
  animation: auroraRotate 60s linear infinite;
}

.aurora-1 {
  top: -50%;
  left: -50%;
  animation-duration: 45s;
}

.aurora-2 {
  top: -50%;
  left: -50%;
  animation-duration: 60s;
  animation-direction: reverse;
  opacity: 0.7;
}

.aurora-3 {
  top: -25%;
  left: -25%;
  width: 150%;
  height: 150%;
  animation-duration: 75s;
  opacity: 0.5;
}

@keyframes auroraRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 分子点阵 */
.molecule-grid {
  position: absolute;
  inset: 0;
}

.molecule-dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary-light);
  border-radius: 50%;
  animation: floatDot linear infinite;
  opacity: 0.3;
  filter: blur(1px);
  box-shadow: 
    0 0 20px 8px rgba(167, 139, 250, 0.3),
    0 0 40px 16px rgba(59, 130, 246, 0.15);
}

.molecule-dot:nth-child(even) {
  background: var(--color-accent);
  box-shadow: 
    0 0 20px 8px rgba(103, 232, 249, 0.3),
    0 0 40px 16px rgba(34, 211, 238, 0.15);
}

.molecule-dot:nth-child(3n) {
  width: 6px;
  height: 6px;
  opacity: 0.15;
  filter: blur(2px);
}

.molecule-dot:nth-child(5n) {
  width: 3px;
  height: 3px;
  opacity: 0.25;
}

@keyframes floatDot {
  0%, 100% { transform: translate(0, 0); opacity: 0.2; }
  25% { opacity: 0.12; }
  50% { transform: translate(var(--move-x), var(--move-y)); opacity: 0.15; }
  75% { opacity: 0.1; }
}

/* 光线条 */
.light-beams {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.beam {
  position: absolute;
  width: 2px;
  height: 300px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(59, 130, 246, 0.5),
    rgba(34, 211, 238, 0.3),
    transparent
  );
  filter: blur(3px);
  animation: beamMove 8s ease-in-out infinite;
}

.beam-1 {
  left: 20%;
  top: -300px;
  animation-delay: 0s;
  transform: rotate(15deg);
}

.beam-2 {
  left: 60%;
  top: -300px;
  animation-delay: 3s;
  transform: rotate(-10deg);
}

.beam-3 {
  left: 85%;
  top: -300px;
  animation-delay: 5s;
  transform: rotate(5deg);
  opacity: 0.6;
}

@keyframes beamMove {
  0%, 100% { transform: translateY(0) rotate(var(--rotation, 15deg)); opacity: 0; }
  10% { opacity: 0.8; }
  50% { transform: translateY(calc(100vh + 300px)) rotate(var(--rotation, 15deg)); opacity: 0.5; }
  90% { opacity: 0; }
}

/* 渐变光球 */
.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(120px);
  opacity: 0.15;
  mix-blend-mode: screen;
}

.orb-1 {
  width: 700px;
  height: 700px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(96, 165, 250, 0.3) 30%, transparent 70%);
  top: -250px;
  right: -150px;
  animation: orbFloat 25s ease-in-out infinite;
}

.orb-2 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.5) 0%, rgba(34, 211, 238, 0.2) 30%, transparent 70%);
  bottom: -200px;
  left: -150px;
  animation: orbFloat 20s ease-in-out infinite reverse;
}

.orb-3 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.6) 0%, rgba(59, 130, 246, 0.3) 50%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: orbPulse 15s ease-in-out infinite;
  opacity: 0.12;
}

@keyframes orbFloat {
  0%, 100% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(20px, -20px) scale(1.05); }
  50% { transform: translate(40px, 40px) scale(1.1); }
  75% { transform: translate(-20px, 20px) scale(1.02); }
}

@keyframes orbPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.08; }
  50% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.12; }
}

/* 六边形网格 */
.hex-pattern {
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='28' height='49' viewBox='0 0 28 49'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.06'%3E%3Cpath d='M13.99 9.25l13 7.5v15l-13 7.5L1 31.75v-15l12.99-7.5zM3 17.9v12.7l10.99 6.34 11-6.35V17.9l-11-6.34L3 17.9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.6;
  animation: hexFade 8s ease-in-out infinite;
}

@keyframes hexFade {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* 闪烁光点 */
.sparkle-container {
  position: absolute;
  inset: 0;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  animation: sparkle ease-in-out infinite;
  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.5);
}

.sparkle:nth-child(odd) {
  background: var(--color-primary-light);
  box-shadow: 0 0 10px 2px var(--color-primary);
}

.sparkle:nth-child(even) {
  background: var(--color-accent-light);
  box-shadow: 0 0 10px 2px var(--color-accent);
}

@keyframes sparkle {
  0%, 100% { 
    transform: scale(0); 
    opacity: 0; 
  }
  50% { 
    transform: scale(var(--sparkle-scale, 1)); 
    opacity: 1; 
  }
}

/* ===== 毛玻璃 Banner ===== */
.glass-banners {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 5;
}

.glass-banner {
  position: absolute;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.02) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 24px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.glass-banner::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(34, 211, 238, 0.05) 100%
  );
}

.banner-bottom {
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  padding: 14px 40px;
  height: auto;
  border-radius: 50px;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.banner-text {
  font-size: 13px;
  color: rgba(226, 232, 240, 0.7);
  white-space: nowrap;
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='4' fill='%2360a5fa'/%3E%3Ccircle cx='6' cy='8' r='2' fill='%2322d3ee'/%3E%3Ccircle cx='18' cy='8' r='2' fill='%2322d3ee'/%3E%3Ccircle cx='6' cy='16' r='2' fill='%2322d3ee'/%3E%3Ccircle cx='18' cy='16' r='2' fill='%2322d3ee'/%3E%3Cline x1='12' y1='12' x2='6' y2='8' stroke='%2360a5fa' stroke-width='1.5'/%3E%3Cline x1='12' y1='12' x2='18' y2='8' stroke='%2360a5fa' stroke-width='1.5'/%3E%3Cline x1='12' y1='12' x2='6' y2='16' stroke='%2360a5fa' stroke-width='1.5'/%3E%3Cline x1='12' y1='12' x2='18' y2='16' stroke='%2360a5fa' stroke-width='1.5'/%3E%3C/svg%3E") 12 12, pointer;
  transition: all 0.3s;
  padding: 4px 8px;
  border-radius: 6px;
  position: relative;
}

.banner-text:hover {
  color: var(--color-primary-light);
  background: rgba(59, 130, 246, 0.1);
}

.banner-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.3s;
  pointer-events: none;
}

.banner-text:hover::before {
  width: 40px;
  height: 40px;
}

.banner-divider {
  color: rgba(59, 130, 246, 0.3);
}

/* ===== 导航栏 ===== */
.landing-nav {
  position: relative;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 48px;
  background: rgba(15, 15, 26, 0.2);
  backdrop-filter: blur(4px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.15);
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 700;
  font-size: 20px;
}

.logo-icon-wrapper {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  border-radius: 10px;
  color: white;
}

.logo-image {
  width: 36px;
  height: 36px;
  object-fit: contain;
}

.logo-icon {
  width: 20px;
  height: 20px;
}

.logo-badge {
  font-size: 10px;
  padding: 2px 8px;
  background: transparent;
  border: 1px solid rgba(59, 130, 246, 0.4);
  color: var(--color-primary-light);
  border-radius: 20px;
  font-weight: 600;
}

.nav-cta {
  background: transparent;
  border: 1px solid rgba(59, 130, 246, 0.4);
  color: var(--color-primary-light);
  font-weight: 600;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
}

.nav-cta:hover {
  border-color: rgba(59, 130, 246, 0.7);
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.25);
}

/* ===== 主内容区 ===== */
.main-content {
  flex: 1;
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  gap: 80px;
}

.content-left {
  flex: 1;
  max-width: 560px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(6, 182, 212, 0.08) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 999px;
  font-size: 13px;
  color: var(--color-primary-light);
  margin-bottom: 24px;
  position: relative;
}

.badge-pulse {
  position: absolute;
  left: 10px;
  width: 6px;
  height: 6px;
  background: var(--color-accent);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.5); }
}

.hero-title {
  font-size: 52px;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.title-line {
  display: block;
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 16px;
  line-height: 1.7;
  color: rgba(226, 232, 240, 0.7);
  margin-bottom: 32px;
}

.hero-actions {
  margin-bottom: 40px;
}

.primary-cta {
  background: transparent;
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: var(--color-primary-light);
  padding: 14px 32px;
  font-size: 15px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.4s;
}

.primary-cta::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(34, 211, 238, 0.08) 100%);
}

.primary-cta::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s;
}

.primary-cta:hover {
  border-color: rgba(59, 130, 246, 0.8);
  box-shadow: 0 0 40px rgba(59, 130, 246, 0.3);
}

.primary-cta:hover::after {
  left: 100%;
}

.primary-cta .arrow-icon {
  transition: transform 0.2s;
}

.primary-cta:hover .arrow-icon {
  transform: translateX(4px);
}

/* 快速统计 */
.quick-stats {
  display: flex;
  align-items: center;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 12px;
  color: rgba(226, 232, 240, 0.6);
}

.stat-divider {
  width: 1px;
  height: 36px;
  background: linear-gradient(180deg, transparent, rgba(59, 130, 246, 0.3), transparent);
}

/* ===== 右侧功能卡片 ===== */
.content-right {
  flex: 1;
  max-width: 400px;
}

.feature-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
  animation: fadeInUp 0.5s ease-out backwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card:hover {
  border-color: rgba(59, 130, 246, 0.35);
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.feature-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-info h3 {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #e2e8f0;
}

.feature-info p {
  font-size: 13px;
  color: rgba(226, 232, 240, 0.6);
  line-height: 1.5;
}

/* ===== 页脚 ===== */
.landing-footer {
  position: relative;
  z-index: 1;
  padding: 16px 48px;
  border-top: 1px solid rgba(59, 130, 246, 0.15);
  text-align: center;
}

.landing-footer p {
  font-size: 12px;
  color: rgba(226, 232, 240, 0.5);
}

/* ===== 响应式 ===== */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    padding: 40px 24px;
    gap: 40px;
    overflow-y: auto;
  }
  
  .content-left {
    max-width: 100%;
    text-align: center;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-actions {
    justify-content: center;
    display: flex;
  }
  
  .quick-stats {
    justify-content: center;
  }
  
  .content-right {
    max-width: 100%;
    width: 100%;
  }
  
  .landing-nav {
    padding: 12px 24px;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 28px;
  }
  
  .quick-stats {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .stat-divider {
    display: none;
  }
}

/* ===== 登录抽屉样式覆盖 ===== */
/* 强制覆盖 shadcn-vue 的默认样式 */
.login-drawer-content {
  width: 420px !important;
  max-width: 90vw !important;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(15, 23, 42, 0.95) 100%) !important;
  --background: 15 23 42 !important;
  backdrop-filter: blur(40px) !important;
  border-left: 1px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow:
    -20px 0 80px rgba(0, 0, 0, 0.6),
    -10px 0 40px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  color: #e2e8f0 !important;
}

/* 确保抽屉内的所有元素都是暗色主题 */
.login-drawer-content *,
.login-drawer-content *::before,
.login-drawer-content *::after {
  color-scheme: dark !important;
}

/* 强制覆盖 bg-background 类 */
.login-drawer-content.bg-background {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(15, 23, 42, 0.95) 100%) !important;
}

.login-drawer-header {
  text-align: center;
  padding: 32px 40px 24px;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
}

.login-drawer-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
}

.login-logo {
  width: 56px;
  height: 56px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 14px;
  color: var(--color-primary-light);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.15),
    inset 0 0 20px rgba(59, 130, 246, 0.05);
  animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.15),
      inset 0 0 20px rgba(59, 130, 246, 0.05);
    border-color: rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.25),
      inset 0 0 30px rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.5);
  }
}

.login-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
  letter-spacing: -0.02em;
}

.login-subtitle {
  font-size: 13px;
  color: rgba(226, 232, 240, 0.5);
  font-weight: 400;
}

.login-content {
  padding: 24px 40px 40px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 13px;
  font-weight: 500;
  color: rgba(226, 232, 240, 0.7);
  letter-spacing: 0.01em;
}

.login-input {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.15);
  color: #e2e8f0;
  transition: all 0.25s ease;
  font-size: 14px;
}

.login-input::placeholder {
  color: rgba(226, 232, 240, 0.25);
}

.login-input:focus {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.08),
    0 0 20px rgba(59, 130, 246, 0.1);
  outline: none;
}

.login-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-input-wrapper {
  position: relative;
}

.password-input-wrapper .login-input {
  padding-right: 40px;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 6px;
  color: rgba(226, 232, 240, 0.4);
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s ease;
}

.password-toggle:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  color: rgba(226, 232, 240, 0.7);
}

.login-button {
  width: 100%;
  padding: 13px;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  border: none;
  color: white;
  font-weight: 600;
  font-size: 14px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.02em;
}

.login-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  transition: left 0.5s ease;
  left: -100%;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.35),
    0 0 0 2px rgba(59, 130, 246, 0.15);
}

.login-button:hover:not(:disabled)::before {
  left: 100%;
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.register-link {
  text-align: center;
  margin-top: 8px;
  font-size: 13px;
  color: rgba(226, 232, 240, 0.5);
}

.register-link .link-button {
  background: none;
  border: none;
  color: var(--color-primary-light);
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
  transition: color 0.25s ease;
}

.register-link .link-button:hover {
  color: var(--color-accent-light);
  text-decoration: underline;
}

/* 抽屉响应式 */
@media (max-width: 480px) {
  .login-drawer-content {
    width: 100%;
    max-width: 100%;
  }

  .login-content {
    padding: 0 20px 20px;
  }

  .login-title {
    font-size: 24px;
  }

  /* 小屏幕上，模态框移动距离调整为抽屉宽度的一半（90vw / 2 = 45vw） */
  .login-modal-card.drawer-open {
    transform: translateX(-45vw);
  }
}

/* 当抽屉宽度为 90vw 时（屏幕宽度 <= 500px） */
@media (max-width: 500px) {
  .login-modal-card.drawer-open {
    transform: translateX(-45vw);
  }
}

/* ===== 登录模态框样式 ===== */
.login-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 100;
  background: rgba(10, 15, 30, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-modal-card {
  position: relative;
  width: 100%;
  max-width: 440px;
  background: rgba(20, 25, 45, 0.75);
  border: 1px solid rgba(100, 116, 139, 0.2);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.login-modal-card.drawer-open {
  transform: translateX(-225px);
}

/* Background effects inside modal */
.modal-bg-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.08) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.modal-grid-lines {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.5;
  pointer-events: none;
  z-index: 0;
}

.modal-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  color: rgba(148, 163, 184, 0.6);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  z-index: 10;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.login-header {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 32px;
}

.login-logo-wrapper {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.15);
  position: relative;
}

.login-logo-wrapper::before {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.5), rgba(168, 85, 247, 0.5)); 
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.5;
}

.login-logo-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.login-title {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
  letter-spacing: -0.5px;
}

.login-subtitle {
  font-size: 14px;
  color: rgba(148, 163, 184, 0.8);
}

.login-form {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(148, 163, 184, 0.6);
  pointer-events: none;
  z-index: 10;
}

.custom-input {
  height: 48px;
  padding-left: 40px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: white;
  font-family: inherit;
  transition: all 0.2s;
}

.custom-input:focus {
  background: rgba(15, 23, 42, 0.8);
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

.password-toggle-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: rgba(148, 163, 184, 0.6);
  cursor: pointer;
  padding: 4px;
}

.password-toggle-btn:hover {
  color: white;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: rgba(148, 163, 184, 0.8);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  position: relative;
}

.custom-checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  background: rgba(15, 23, 42, 0.6);
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.custom-checkbox:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(15, 23, 42, 0.8);
}

.custom-checkbox:checked {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
}

.custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  animation: checkmark 0.2s ease-in-out;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: rotate(45deg) scale(0.5);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: rotate(45deg) scale(1);
  }
}

.custom-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.forgot-password {
  color: #60a5fa;
  text-decoration: none;
  transition: color 0.2s;
}

.forgot-password:hover {
  color: #93c5fd;
}

.login-submit-btn {
  position: relative;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s;
}

.login-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.login-submit-btn:hover .btn-glow {
  transform: translateX(100%);
}

.register-prompt {
  text-align: center;
  font-size: 14px;
  color: rgba(148, 163, 184, 0.8);
}

.register-link-btn {
  background: transparent;
  border: none;
  color: #60a5fa;
  font-weight: 600;
  cursor: pointer;
  padding: 0 4px;
  margin-left: 4px;
  transition: all 0.2s;
}

.register-link-btn:hover {
  color: #93c5fd;
  text-decoration: underline;
}

.form-hint {
  font-size: 12px;
  color: rgba(148, 163, 184, 0.6);
  margin-top: 4px;
  padding-left: 4px;
}

.uppercase {
  text-transform: uppercase;
}

/* Modal Transition */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-fade-enter-active .login-modal-card {
  animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.modal-fade-leave-active .login-modal-card {
  animation: modalSlideIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) reverse;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.95) translateY(10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* ===== 邀请码申请抽屉样式 ===== */
.question-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(96, 165, 250, 0.7);
  cursor: pointer;
  transition: all 0.2s;
}

.question-button:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

.question-button:active {
  transform: translateY(-50%) scale(0.95);
}

.drawer-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  backdrop-filter: blur(4px);
}

.invite-request-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 450px;
  max-width: 90vw;
  background: rgba(30, 41, 59, 0.98);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.drawer-title {
  font-size: 20px;
  font-weight: 600;
  color: #f1f5f9;
  margin: 0;
}

.drawer-close {
  background: rgba(59, 130, 246, 0.1);
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  cursor: pointer;
  transition: all 0.3s;
}

.drawer-close:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.drawer-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.drawer-description {
  color: rgba(148, 163, 184, 0.8);
  font-size: 14px;
  margin-bottom: 24px;
  line-height: 1.6;
}

.invite-request-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field label {
  font-size: 14px;
  font-weight: 500;
  color: #e2e8f0;
}

.form-field input {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  color: #f1f5f9;
  font-size: 14px;
  transition: all 0.3s;
}

.form-field input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-field input::placeholder {
  color: rgba(148, 163, 184, 0.6);
}

.request-textarea {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  color: #f1f5f9;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: all 0.3s;
}

.request-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.request-textarea::placeholder {
  color: rgba(148, 163, 184, 0.6);
}

.submit-request-btn {
  margin-top: 12px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 10px;
  color: white;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.submit-request-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.submit-request-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Drawer Transition */
.drawer-fade-enter-active,
.drawer-fade-leave-active {
  transition: opacity 0.3s ease;
}

.drawer-fade-enter-from,
.drawer-fade-leave-to {
  opacity: 0;
}

.drawer-slide-enter-active,
.drawer-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.drawer-slide-enter-from,
.drawer-slide-leave-to {
  transform: translateX(100%);
}
</style>
