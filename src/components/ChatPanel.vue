<template>
  <div class="chat-panel">
    <div class="chat-header">
      <span class="title">{{ selectedAgentName }}</span>
      <div class="header-actions">
        <Button
          variant="ghost"
          size="sm"
          title="对话信息"
          @click="sessionInfoDialogOpen = true"
        >
          <Info class="h-4 w-4" />
        </Button>
        <Button
          v-if="messages.length > 0"
          variant="ghost"
          size="sm"
          @click="startNewChat"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M12 5v14M5 12h14"/></svg>
          新对话
        </Button>
      </div>
    </div>

    <div class="chat-messages" ref="messagesContainer">
      <!-- 加载历史记录骨架屏 -->
      <div v-if="isLoadingSession" class="loading-skeleton">
        <div v-for="i in 3" :key="i" class="skeleton-message">
          <div class="skeleton-avatar"></div>
          <div class="skeleton-bubble">
            <div class="skeleton-line"></div>
            <div class="skeleton-line short"></div>
          </div>
        </div>
      </div>
      
      <!-- 欢迎页面 - 当没有消息时显示 -->
      <div v-else-if="messages.length === 0" class="welcome-screen">
        <div class="welcome-header">
          <div class="welcome-logo">
            <img :src="logoSrc" alt="电镀智能体" class="logo-image" />
          </div>
          <p class="welcome-description">
            <span class="text-primary font-semibold">AI-Powered Electroplating Intelligence</span>
            <span class="text-muted-foreground mx-2">•</span>
            电镀添加剂设计助手
          </p>
        </div>
        
        <div class="welcome-cards">
          <Card class="welcome-card card-analyze" @click="handleQuickAction('analyze')">
            <CardContent class="p-6">
              <div class="card-icon">
                <Search class="h-8 w-8 text-blue-500" />
              </div>
              <h3 class="card-title">性质预测</h3>
              <p class="card-description">
                预测分子吸附能、扩散系数、可合成性等关键性质
              </p>
            </CardContent>
          </Card>
          
          <Card class="welcome-card card-visualize" @click="handleQuickAction('visualize')">
            <CardContent class="p-6">
              <div class="card-icon">
                <Eye class="h-8 w-8 text-green-500" />
              </div>
              <h3 class="card-title">分子设计</h3>
              <p class="card-description">
                设计整平剂、加速剂、抑制剂等电镀添加剂分子
              </p>
            </CardContent>
          </Card>
          
          <Card class="welcome-card card-optimize" @click="handleQuickAction('optimize')">
            <CardContent class="p-6">
              <div class="card-icon">
                <Zap class="h-8 w-8 text-purple-500" />
              </div>
              <h3 class="card-title">配方优化</h3>
              <p class="card-description">
                分析配方协同效应，改进电镀配方，解决电镀问题
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <!-- 聊天消息列表 -->
      <div v-else class="message-list">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="['message-container', message.role]"
          v-show="message.role !== 'tool'"
        >
          <div class="message-avatar">
            <!-- 用户头像：使用真实头像或首字母 -->
            <Avatar v-if="message.role === 'user'" class="user-avatar">
              <AvatarImage :src="userProfile.avatar" :alt="userProfile.name || 'User'" />
              <AvatarFallback class="user-avatar-fallback">
                {{ getUserInitials(userProfile.name) }}
              </AvatarFallback>
            </Avatar>
            <!-- AI 头像：根据 Agent 名称显示首字母 -->
            <div v-else :class="['avatar', 'assistant']">
              {{ getAgentInitials(selectedAgentName) }}
            </div>
          </div>
          <div class="message-wrapper">
            <!-- 工具调用提示卡片 (AI 消息且有工具调用时显示) -->
            <div v-if="message.role === 'assistant' && getDisplayToolCalls(message).length > 0" class="tool-calls-card mb-1.5">
              <Collapsible
                :default-open="false"
                class="tool-calls-collapsible"
              >
                <CollapsibleTrigger class="tool-calls-trigger">
                  <div class="flex items-center gap-1.5 flex-wrap px-2 py-1.5 rounded-md border border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/20">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500 dark:text-blue-400 flex-shrink-0">
                      <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                    </svg>
                    <div
                      v-for="(tool, index) in getDisplayToolCalls(message).slice(0, 3)"
                      :key="index"
                      class="inline-flex items-center gap-1 text-xs"
                    >
                      <span class="font-mono bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 px-1.5 py-0.5 rounded">{{ tool.name }}</span>
                      <span v-if="tool.duration" class="text-blue-500 dark:text-blue-400 text-[10px]">{{ tool.duration }}</span>
                    </div>
                    <span v-if="getDisplayToolCalls(message).length > 3" class="text-blue-500 dark:text-blue-400 text-xs">
                      +{{ getDisplayToolCalls(message).length - 3 }} 更多
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tool-calls-chevron text-blue-500 dark:text-blue-400">
                      <path d="m6 9 6 6 6-6"/>
                    </svg>
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent class="tool-calls-content">
                  <div class="flex items-center gap-1.5 flex-wrap px-2 py-1.5 rounded-md border border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/20 mt-1">
                    <div
                      v-for="(tool, index) in getDisplayToolCalls(message)"
                      :key="index"
                      class="inline-flex items-center gap-1 text-xs"
                    >
                      <span class="font-mono bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 px-1.5 py-0.5 rounded">{{ tool.name }}</span>
                      <span v-if="tool.duration" class="text-blue-500 dark:text-blue-400 text-[10px]">{{ tool.duration }}</span>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>

            <Card :class="['message-bubble', message.role]">
              <CardContent class="message-content">
                <!-- 用户消息：纯文本 -->
                <div v-if="message.role === 'user'" class="message-text user-message-text">
                  {{ message.content }}
                  <div class="message-time">
                    {{ formatTime(message.timestamp) }}
                  </div>
                </div>

                <!-- AI 消消息：清理后的内容 + Markdown 渲染 -->
                <div v-if="message.role === 'assistant'">
                  <!-- 推理步骤（可折叠） -->
                  <Collapsible 
                    v-if="message.reasoningSteps && message.reasoningSteps.length > 0"
                    class="reasoning-steps-container mb-3"
                  >
                    <CollapsibleTrigger class="reasoning-trigger">
                      <span class="reasoning-icon">🧠</span>
                      <span class="reasoning-text">推理过程 ({{ message.reasoningSteps.length }} 步)</span>
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="reasoning-arrow">
                        <path d="m6 9 6 6 6-6"/>
                      </svg>
                    </CollapsibleTrigger>
                    <CollapsibleContent class="reasoning-content">
                      <div 
                        v-for="(step, stepIndex) in message.reasoningSteps" 
                        :key="stepIndex"
                        class="reasoning-step"
                      >
                        <div class="reasoning-step-header">
                          <span class="reasoning-step-title">{{ step.title }}</span>
                          <span v-if="step.confidence" class="reasoning-confidence">
                            置信度: {{ (step.confidence * 100).toFixed(0) }}%
                          </span>
                        </div>
                        <div v-if="step.reasoning" class="reasoning-step-reasoning">
                          <MarkdownRenderer :content="step.reasoning" />
                        </div>
                        <div v-if="step.action" class="reasoning-step-action">
                          <strong>行动:</strong> {{ step.action }}
                        </div>
                        <div v-if="step.result" class="reasoning-step-result">
                          <strong>结果:</strong> {{ step.result }}
                        </div>
                        <div v-if="step.reasoning_content" class="reasoning-step-content">
                          <MarkdownRenderer :content="step.reasoning_content" />
                        </div>
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                  
                  <!-- 主要消息内容 -->
                  <div class="message-text">
                    <MarkdownRenderer :content="getMessageContentWithCursor(message)" />
                  </div>

                  <div class="message-time">
                    {{ formatTime(message.timestamp) }}
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 用户消息操作按钮（气泡外部） -->
            <div v-if="message.role === 'user'" class="message-actions user-message-actions">
              <Button
                variant="ghost"
                size="icon"
                class="action-btn"
                @click="copyMessage(message.content)"
                title="复制"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
              </Button>
            </div>

            <!-- AI 消息操作按钮（气泡外部） -->
            <div v-if="message.role === 'assistant'" class="message-actions">
              <Button 
                variant="ghost" 
                size="icon" 
                class="action-btn"
                @click="copyMessage(message.content)"
                title="复制"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="action-btn"
                @click="openShareDialog"
                title="分享"
              >
                <Share2 :size="14" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                class="action-btn"
                @click="regenerateMessage(message.id)"
                title="重新生成"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/></svg>
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                class="action-btn"
                @click="likeMessage(message.id)"
                title="赞"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 10v12"/><path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"/></svg>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="action-btn"
                @click="dislikeMessage(message.id)"
                title="踩"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 14V2"/><path d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z"/></svg>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="action-btn"
                @click="showEventStream(message.id)"
                title="查看工具调用"
              >
                <ListTree :size="14" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-controls">
      <TooltipProvider>
        <div class="control-buttons">
          <!-- 模型选择下拉菜单（图标按钮） -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="icon" :disabled="isLoadingAgents" title="选择模型">
                <Bot class="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" class="min-w-[200px]">
              <DropdownMenuItem
                v-for="agent in agents"
                :key="agent.id"
                @click="handleAgentSwitch(agent.id)"
                :class="{ 'bg-accent': selectedAgentId === agent.id }"
                class="flex items-center justify-between pr-2"
              >
                <div class="flex items-center">
                  <Bot class="h-4 w-4 mr-2" />
                  {{ agent.name }}
                </div>
                <button
                  class="ml-2 p-1 rounded-full hover:bg-muted-foreground/20 transition-colors"
                  @click="openAgentDetail(agent, $event)"
                  title="查看详情"
                >
                  <Info class="h-3.5 w-3.5 text-muted-foreground" />
                </button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <!-- 上传文件按钮（暂时禁用点击） -->
          <Button
            variant="ghost"
            size="icon"
            type="button"
            title="文件上传功能暂时不可用"
            disabled
            class="opacity-50 cursor-not-allowed"
          >
            <Upload class="h-4 w-4" />
          </Button>

          <!-- 联网搜索按钮（图标） -->
          <Tooltip>
            <TooltipTrigger as-child>
              <Button 
                variant="ghost" 
                size="icon"
                :class="{ 'bg-blue-500/20 text-blue-600 dark:text-blue-400 hover:bg-blue-500/30': isWebSearchEnabled }"
                @click="isWebSearchEnabled = !isWebSearchEnabled"
              >
                <Globe class="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{{ isWebSearchEnabled ? '关闭联网搜索' : '开启联网搜索' }}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
      
      <!-- 已选择文件显示 -->
      <div v-if="pendingFiles.length" class="pending-file">
        <div v-for="(file, idx) in pendingFiles" :key="file.name + file.size" class="file-item">
          <File class="h-4 w-4" />
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">({{ formatFileSize(file.size) }})</span>
          <Button
            variant="ghost"
            size="icon"
            class="remove-file-btn"
            @click="removePendingFile(idx)"
            type="button"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <form class="chat-actions" @submit.prevent="handleSend">
        <Input
          v-model="prompt"
          :disabled="isGenerating"
        />
        <Button
          v-if="isGenerating"
          type="button"
          size="icon"
          variant="ghost"
          @click="handleStop"
          title="停止生成"
          class="text-muted-foreground hover:text-destructive relative overflow-hidden"
        >
          <span class="absolute inset-0 animate-ping opacity-10 bg-muted-foreground rounded-full"></span>
          <Square class="h-4 w-4 fill-current relative z-10" />
        </Button>
        <Button 
          v-else
          type="submit" 
          size="icon" 
          title="发送"
        >
          <Send class="h-4 w-4" />
        </Button>
      </form>
      <p class="text-xs text-muted-foreground text-center mt-2">
        内容由AI生成
      </p>
    </div>

    <!-- 分享对话框 -->
    <Dialog v-model:open="shareDialogOpen">
      <DialogContent class="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>分享对话</DialogTitle>
          <DialogDescription>
            选择要分享的消息，然后导出为文本或图片
          </DialogDescription>
        </DialogHeader>

        <!-- 全选按钮 -->
        <div class="flex items-center justify-between py-2 border-b">
          <div class="text-sm text-muted-foreground">
            已选择 {{ selectedMessageIds.size }} / {{ messages.length }} 条消息
          </div>
          <Button
            variant="ghost"
            size="sm"
            @click="toggleSelectAll"
            class="h-8"
          >
            <Checkbox
              :model-value="selectAllMessages"
              @update:model-value="toggleSelectAll"
              class="mr-2"
            >
              <CheckboxIndicator />
            </Checkbox>
            {{ selectAllMessages ? '取消全选' : '全选' }}
          </Button>
        </div>

        <!-- 消息列表 -->
        <div class="flex-1 overflow-y-auto space-y-2 py-2">
          <div
            v-for="message in messages.filter(m => m.role !== 'tool')"
            :key="message.id"
            :class="[
              'flex items-start gap-3 p-3 rounded-lg border transition-colors cursor-pointer',
              selectedMessageIds.has(message.id) ? 'bg-primary/5 border-primary' : 'hover:bg-muted/50'
            ]"
            @click="toggleMessageSelection(message.id)"
          >
            <Checkbox
              :model-value="selectedMessageIds.has(message.id)"
              @update:model-value="() => toggleMessageSelection(message.id)"
              class="mt-1"
              @click.stop
            >
              <CheckboxIndicator />
            </Checkbox>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span
                  :class="[
                    'text-xs font-medium px-2 py-0.5 rounded',
                    message.role === 'user'
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                  ]"
                >
                  {{ message.role === 'user' ? '用户' : '助手' }}
                </span>
                <span class="text-xs text-muted-foreground">
                  {{ formatTime(message.timestamp) }}
                </span>
              </div>
              <p class="text-sm line-clamp-2">
                {{ message.content }}
              </p>
            </div>
          </div>
        </div>

        <!-- 导出按钮 -->
        <div class="flex gap-2 pt-4 border-t">
          <Button
            variant="default"
            class="flex-1 gap-2 h-10"
            @click="exportAsText"
            type="button"
          >
            <Download :size="16" />
            <span class="text-sm font-medium">导出文本</span>
          </Button>

          <Button
            variant="secondary"
            class="flex-1 gap-2 h-10"
            @click="exportAsImage"
            type="button"
          >
            <ImageIcon :size="16" />
            <span class="text-sm font-medium">导出图片</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    <!-- 对话信息对话框 -->
    <AlertDialog v-model:open="sessionInfoDialogOpen">
      <AlertDialogContent class="max-w-2xl">
        <AlertDialogHeader>
          <AlertDialogTitle class="flex items-center gap-2">
            <MessageSquare class="h-5 w-5" />
            对话与智能体信息
          </AlertDialogTitle>
          <AlertDialogDescription>
            查看当前对话的详细信息和智能体配置
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div class="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
          <!-- 对话基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 对话信息 -->
            <div class="space-y-2">
              <h4 class="text-sm font-medium text-muted-foreground">对话信息</h4>
              <div class="rounded-md border p-3 space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">消息数量</span>
                  <span>{{ messages.length }} 条</span>
                </div>
                <div v-if="currentSessionData" class="flex justify-between text-sm">
                  <span class="text-muted-foreground">创建时间</span>
                  <span>{{ formatSessionTime(currentSessionData.created_at) }}</span>
                </div>
                <div v-if="currentSessionData" class="flex justify-between text-sm">
                  <span class="text-muted-foreground">更新时间</span>
                  <span>{{ formatSessionTime(currentSessionData.updated_at) }}</span>
                </div>
              </div>
            </div>

            <!-- 智能体信息 -->
            <div v-if="selectedAgentData" class="space-y-2">
              <h4 class="text-sm font-medium text-muted-foreground">智能体配置</h4>
              <div class="rounded-md border p-3 space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">智能体 ID</span>
                  <span class="font-mono text-xs select-all">{{ selectedAgentData.id }}</span>
                </div>
                <div v-if="selectedAgentData.model" class="flex justify-between text-sm">
                  <span class="text-muted-foreground">模型</span>
                  <span>{{ selectedAgentData.model.name || selectedAgentData.model.model }}</span>
                </div>
                <div v-if="selectedAgentData.model?.provider" class="flex justify-between text-sm">
                  <span class="text-muted-foreground">提供商</span>
                  <span>{{ selectedAgentData.model.provider }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 工具列表 -->
          <div v-if="selectedAgentData?.tools?.tools && selectedAgentData.tools.tools.length > 0" class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">可用工具 ({{ selectedAgentData.tools.tools.length }})</h4>
            <div class="rounded-md border p-3">
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="tool in selectedAgentData.tools.tools"
                  :key="tool.name"
                  class="font-mono text-xs bg-primary/10 text-primary px-2 py-1 rounded"
                >
                  {{ tool.name }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel @click="sessionInfoDialogOpen = false">关闭</AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 智能体详情对话框 -->
    <AlertDialog v-model:open="agentDetailDialogOpen">
      <AlertDialogContent class="max-w-lg">
        <AlertDialogHeader>
          <AlertDialogTitle class="flex items-center gap-2">
            <Bot class="h-5 w-5" />
            {{ selectedAgentForDetail?.name || '智能体详情' }}
          </AlertDialogTitle>
          <AlertDialogDescription>
            查看智能体的详细信息
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div v-if="selectedAgentForDetail" class="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
          <!-- 基本信息 -->
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">基本信息</h4>
            <div class="rounded-md border p-3 space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">ID</span>
                <span class="font-mono text-xs">{{ selectedAgentForDetail.id }}</span>
              </div>
              <div v-if="selectedAgentForDetail.db_id" class="flex justify-between text-sm">
                <span class="text-muted-foreground">数据库 ID</span>
                <span class="font-mono text-xs">{{ selectedAgentForDetail.db_id }}</span>
              </div>
            </div>
          </div>

          <!-- 模型信息 -->
          <div v-if="selectedAgentForDetail.model" class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">模型配置</h4>
            <div class="rounded-md border p-3 space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">模型名称</span>
                <span>{{ selectedAgentForDetail.model.name || selectedAgentForDetail.model.model }}</span>
              </div>
              <div v-if="selectedAgentForDetail.model.provider" class="flex justify-between text-sm">
                <span class="text-muted-foreground">提供商</span>
                <span>{{ selectedAgentForDetail.model.provider }}</span>
              </div>
            </div>
          </div>

          <!-- 描述 -->
          <div v-if="selectedAgentForDetail.description" class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">描述</h4>
            <div class="rounded-md border p-3">
              <p class="text-sm">{{ selectedAgentForDetail.description }}</p>
            </div>
          </div>

          <!-- 系统指令 -->
          <div v-if="selectedAgentForDetail.instructions" class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">系统指令</h4>
            <div class="rounded-md border p-3 max-h-32 overflow-y-auto">
              <p class="text-sm whitespace-pre-wrap">{{ selectedAgentForDetail.instructions }}</p>
            </div>
          </div>

          <!-- 工具列表 -->
          <div v-if="selectedAgentForDetail.tools && selectedAgentForDetail.tools.length > 0" class="space-y-2">
            <h4 class="text-sm font-medium text-muted-foreground">可用工具 ({{ selectedAgentForDetail.tools.length }})</h4>
            <div class="rounded-md border p-3 space-y-2 max-h-40 overflow-y-auto">
              <div
                v-for="tool in selectedAgentForDetail.tools"
                :key="tool.name"
                class="flex items-start gap-2 text-sm"
              >
                <span class="font-mono text-xs bg-muted px-1.5 py-0.5 rounded">{{ tool.name }}</span>
                <span v-if="tool.description" class="text-muted-foreground text-xs">{{ tool.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel>关闭</AlertDialogCancel>
          <Button @click="selectedAgentId = selectedAgentForDetail?.id || ''; agentDetailDialogOpen = false">
            选择此智能体
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 产品引导教程 -->
    <ProductTour
      v-model:show="showTour"
      :steps="tourSteps"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Search, Eye, Zap, Bot, Upload, Globe, X, File, Send, Share2, Download, Image as ImageIcon, Info, Sidebar, MessageSquare, Beaker, Clock, ListTree, Square } from 'lucide-vue-next';
import { toast } from 'vue-sonner';
import MarkdownRenderer from '@/components/MarkdownRenderer.vue';
import ProductTour from '@/components/ProductTourSimple.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox, CheckboxIndicator } from '@/components/ui/checkbox';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { useColorMode } from '@vueuse/core';
import html2canvas from 'html2canvas';
import { API_BASE_URL } from '@/config/api.config';
import { onBeforeRouteLeave } from 'vue-router';
// import { uploadChatFile, type FileReference } from '@/services/chatFileService';  // 暂时禁用，恢复原始 Agno 格式

// 定义 emit 事件
const emit = defineEmits<{
  sessionCreated: [sessionId: string];
  messageCompleted: [];
  chatPanelMounted: [instance: any];
}>();

// 接收 props
const props = defineProps<{
  selectedSessionId?: string;
}>();

type ChatRole = 'user' | 'assistant' | 'tool';

interface ToolCall {
  id: string;
  name?: string;
  args?: string;
  result?: string;
  status: 'running' | 'completed' | 'error';
  duration?: string;
}

interface ReasoningStep {
  title: string;
  action?: string;
  result?: string;
  reasoning?: string;
  next_action?: string;
  confidence?: number;
  reasoning_content?: string;
  timestamp: number;
}

interface ChatMessage {
  id: number;
  role: ChatRole;
  content: string;
  timestamp: number;
  toolCalls?: ToolCall[];
  toolCallId?: string; // 用于工具结果消息，关联到工具调用 ID
  isStreaming?: boolean;
  runId?: string;
  reasoningSteps?: ReasoningStep[];
}

interface Agent {
  id: string;
  name: string;
  db_id?: string;
  description?: string;
  instructions?: string;
  tools?: Array<{ name: string; description?: string }>;
  model?: {
    name: string;
    model: string;
    provider: string;
  };
}

// API 返回的消息格式
interface ApiMessage {
  content: string;
  role: 'user' | 'assistant' | 'tool';
  created_at: number;
  from_history?: boolean;
  tool_calls?: any[];
  tool_call_id?: string;
  run_id?: string;
  metadata?: Record<string, any>;
  reasoning_steps?: any[];
  thinking?: any[];
}

interface SessionDetail {
  session_id: string;
  session_name: string;
  chat_history: ApiMessage[];
  agent_id: string;
  created_at: string;
  updated_at: string;
}

interface UserProfile {
  name: string;
  email: string;
  avatar: string;
}

type ReasoningCache = Record<string, Record<string, ReasoningStep[]>>;

const REASONING_CACHE_KEY = 'eagent_reasoning_cache';

const loadReasoningCache = (): ReasoningCache => {
  if (typeof window === 'undefined') {
    return {};
  }
  try {
    const raw = localStorage.getItem(REASONING_CACHE_KEY);
    if (!raw) return {};
    const parsed = JSON.parse(raw) as ReasoningCache;
    return parsed || {};
  } catch (error) {
    console.warn('[ChatPanel] Failed to load reasoning cache:', error);
    return {};
  }
};

const saveReasoningCache = (cache: ReasoningCache) => {
  if (typeof window === 'undefined') {
    return;
  }
  try {
    localStorage.setItem(REASONING_CACHE_KEY, JSON.stringify(cache));
  } catch (error) {
    console.warn('[ChatPanel] Failed to persist reasoning cache:', error);
  }
};

const reasoningCache = ref<ReasoningCache>(loadReasoningCache());

const REASONING_EVENT_TYPES = new Set([
  'ReasoningStarted',
  'ReasoningStep',
  'ReasoningCompleted',
  'ReasoningContent',
  'Reasoning',
  'ThinkingStarted',
  'ThinkingStep',
  'ThinkingCompleted',
  'ThinkingContent',
]);

const persistReasoningSteps = (sessionId: string, runId: string, steps: ReasoningStep[], messageIndex?: number) => {
  if (!sessionId || steps.length === 0) return;
  if (!reasoningCache.value[sessionId]) {
    reasoningCache.value[sessionId] = {};
  }
  
  // 使用 runId 作为主键，如果没有 runId，使用消息索引作为备选
  const cacheKey = runId || (messageIndex !== undefined ? `msg_${messageIndex}` : null);
  if (!cacheKey) return;
  
  reasoningCache.value[sessionId][cacheKey] = steps.map(step => ({ ...step }));
  saveReasoningCache(reasoningCache.value);
  console.log('[ChatPanel] Persisted reasoning steps:', { sessionId, cacheKey, stepCount: steps.length });
};

const getPersistedReasoningSteps = (sessionId: string, runId?: string, messageIndex?: number): ReasoningStep[] => {
  if (!sessionId) return [];
  
  const sessionCache = reasoningCache.value[sessionId];
  if (!sessionCache) return [];
  
  // 优先使用 runId 查找
  if (runId && sessionCache[runId]) {
    console.log('[ChatPanel] Found reasoning steps by runId:', { sessionId, runId });
    return sessionCache[runId].map(step => ({ ...step }));
  }
  
  // 备选：使用消息索引查找
  if (messageIndex !== undefined) {
    const indexKey = `msg_${messageIndex}`;
    if (sessionCache[indexKey]) {
      console.log('[ChatPanel] Found reasoning steps by messageIndex:', { sessionId, messageIndex });
      return sessionCache[indexKey].map(step => ({ ...step }));
    }
  }
  
  return [];
};

const normalizeReasoningStep = (rawStep: any, index: number, fallbackTimestamp: number): ReasoningStep | null => {
  if (!rawStep) return null;

  const source = rawStep.content && typeof rawStep.content === 'object'
    ? rawStep.content
    : rawStep;

  const reasoningContent =
    source.reasoning_content ??
    source.reasoning ??
    source.content ??
    (typeof rawStep.content === 'string' ? rawStep.content : undefined);

  return {
    title: source.title || rawStep.title || `推理步骤 ${index + 1}`,
    action: source.action ?? rawStep.action,
    result: source.result ?? rawStep.result,
    reasoning: source.reasoning ?? rawStep.reasoning,
    next_action: source.next_action ?? rawStep.next_action,
    confidence: source.confidence ?? rawStep.confidence,
    reasoning_content: reasoningContent,
    timestamp: source.timestamp
      ? Number(source.timestamp)
      : rawStep.timestamp
        ? Number(rawStep.timestamp)
        : fallbackTimestamp,
  };
};

const extractReasoningStepsFromApiMessage = (apiMsg: ApiMessage): ReasoningStep[] => {
  const candidateSources = [
    Array.isArray(apiMsg.reasoning_steps) ? apiMsg.reasoning_steps : null,
    Array.isArray(apiMsg.metadata?.reasoning_steps) ? apiMsg.metadata?.reasoning_steps : null,
    Array.isArray(apiMsg.metadata?.thinking) ? apiMsg.metadata?.thinking : null,
    Array.isArray(apiMsg.thinking) ? apiMsg.thinking : null,
  ].filter((source): source is any[] => !!source);

  if (candidateSources.length === 0) {
    return [];
  }

  const fallbackTimestamp = apiMsg.created_at ? apiMsg.created_at * 1000 : Date.now();
  const firstSource = candidateSources[0];
  
  if (!firstSource) {
    return [];
  }

  return firstSource
    .map((step: any, index: number) => normalizeReasoningStep(step, index, fallbackTimestamp))
    .filter((step: ReasoningStep | null): step is ReasoningStep => !!step);
};

const getApiMessageEventType = (apiMsg: ApiMessage): string | undefined => {
  return (
    apiMsg.metadata?.event ||
    apiMsg.metadata?.event_type ||
    apiMsg.metadata?.type ||
    (apiMsg as any).event
  );
};

const isReasoningEventMessage = (apiMsg: ApiMessage): boolean => {
  const eventType = getApiMessageEventType(apiMsg);
  return !!eventType && REASONING_EVENT_TYPES.has(eventType);
};

const appendReasoningStepsToLatestAssistant = (sessionId: string, runId: string | undefined, steps: ReasoningStep[]) => {
  if (steps.length === 0) return false;
  
  // 找到目标消息及其索引
  let targetMessageIndex = -1;
  const targetMessage = [...messages]
    .reverse()
    .find((msg, idx) => {
      if (msg.role === 'assistant' && (!runId || (msg.runId && msg.runId === runId))) {
        // 计算实际索引（因为是反向查找）
        targetMessageIndex = messages.length - 1 - idx;
        return true;
      }
      return false;
    });
  
  if (!targetMessage) {
    return false;
  }

  if (!targetMessage.reasoningSteps) {
    targetMessage.reasoningSteps = [];
  }

  targetMessage.reasoningSteps.push(...steps);

  if (sessionId) {
    // 计算 assistant 消息的索引（只计算 assistant 消息）
    const assistantIndex = messages.filter((m, idx) => m.role === 'assistant' && idx <= targetMessageIndex).length - 1;
    persistReasoningSteps(sessionId, targetMessage.runId || '', targetMessage.reasoningSteps, assistantIndex);
  }
  return true;
};

const prompt = ref('');
const messages = reactive<ChatMessage[]>([]);
const agents = ref<Agent[]>([]);
const selectedAgentId = ref<string>('');
const isLoadingAgents = ref(false);
const isLoadingSession = ref(false);
const currentSessionId = ref<string>('');
const dbId = ref<string>(''); // 数据库 ID

// 智能体详情对话框状态
const agentDetailDialogOpen = ref(false);
const selectedAgentForDetail = ref<Agent | null>(null);

// 对话信息对话框状态
const sessionInfoDialogOpen = ref(false);
const currentSessionData = ref<SessionDetail | null>(null);

// 当前选中的智能体数据（包含完整的工具等信息）
const selectedAgentData = computed(() => {
  return agents.value.find(a => a.id === selectedAgentId.value) || null;
});

// 打开智能体详情对话框
const openAgentDetail = (agent: Agent, event: Event) => {
  event.stopPropagation(); // 阻止触发选择智能体
  selectedAgentForDetail.value = agent;
  agentDetailDialogOpen.value = true;
};

// 联网搜索状态
const isWebSearchEnabled = ref(false);

const authStore = useAuthStore();

// AI 正在生成回复的状态
const isGenerating = ref(false);
const currentRunId = ref<string>('');

// 暂存的文件列表
const pendingFiles = ref<File[]>([]);

// 分享对话框状态
const shareDialogOpen = ref(false);
const selectedMessageIds = ref<Set<string>>(new Set());
const selectAllMessages = ref(false);

// 轮询状态 - 指数退避策略
const pollingInterval = ref<number | null>(null);
const MIN_POLLING_INTERVAL = 2000; // 最快 2 秒（有新消息时）
const MAX_POLLING_INTERVAL = 15000; // 最慢 15 秒（无新消息时）
const POLLING_INCREASE_FACTOR = 1.5; // 每次增加 50%
let currentPollingInterval = MIN_POLLING_INTERVAL; // 当前轮询间隔
const lastMessageCount = ref<number>(0);
const consecutiveEmptyPolls = ref<number>(0); // 连续空轮询次数

// 主题模式
const mode = useColorMode();

// 根据主题选择 logo
const logoSrc = computed(() => {
  const baseUrl = import.meta.env.BASE_URL;
  return mode.value === 'dark' ? `${baseUrl}white.png` : `${baseUrl}black.png`;
});

// 当前选择的 Agent 名称
const selectedAgentName = computed(() => {
  const agent = agents.value.find(a => a.id === selectedAgentId.value);
  return agent?.name || '电镀智能体';
});

// 用户配置
const userProfile = ref<UserProfile>({
  name: '',
  email: '',
  avatar: '',
});

// 加载用户配置
const loadUserProfile = () => {
  const savedProfile = localStorage.getItem('userProfile');
  if (savedProfile) {
    try {
      userProfile.value = JSON.parse(savedProfile);
    } catch (error) {
      console.error('Failed to load user profile:', error);
    }
  }
};

// 获取用户名首字母
const getUserInitials = (name: string) => {
  if (!name) return 'U';

  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// 获取 Agent 名首字母
const getAgentInitials = (name: string) => {
  if (!name) return 'K';

  // 提取中文名称的首字或英文单词的首字母
  const matches = name.match(/[\u4e00-\u9fa5]|[A-Za-z]/g);
  if (matches && matches.length > 0) {
    return matches[0].toUpperCase();
  }

  return name[0].toUpperCase();
};

// 解析消息中的工具调用信息
interface ParsedToolCall {
  name: string;
  args?: string;
  duration?: string;
}

const getToolCallsFromMessage = (message: ChatMessage): ParsedToolCall[] => {
  if (message.role !== 'assistant') return [];
  
  const toolCalls: ParsedToolCall[] = [];
  // 匹配模式: tool_name(args=value) completed in 0.0020s
  // 或者: tool_name() completed in 0.0020s
  const toolCallPattern = /(\w+)\(([^)]*)\)\s+completed\s+in\s+([\d.]+)s/g;
  
  let match;
  while ((match = toolCallPattern.exec(message.content)) !== null) {
    if (match[1]) {
      toolCalls.push({
        name: match[1],
        args: match[2] || undefined,
        duration: match[3] ? `${match[3]}s` : undefined,
      });
    }
  }
  
  return toolCalls;
};

// 获取用于显示的工具调用列表
// 策略：优先使用事件数据（实时），然后用文本解析的数据补充 duration
const getDisplayToolCalls = (message: ChatMessage) => {
  const fromContent = getToolCallsFromMessage(message);
  const fromEvents = message.toolCalls || [];
  
  // 如果没有事件数据，直接使用文本解析的结果（这是之前的逻辑）
  if (fromEvents.length === 0) {
    return fromContent;
  }
  
  // 有事件数据时，使用事件数据
  return fromEvents.map(eventTool => {
    // 尝试通过名称匹配找到对应的文本记录
    const contentTool = fromContent.find(t => t.name === eventTool.name);
    
    // 确定显示的 duration
    let displayDuration: string | undefined;
    if (eventTool.duration) {
      // 优先使用事件中的 duration（从 ToolCallCompleted 获取）
      displayDuration = eventTool.duration;
    } else if (contentTool?.duration) {
      // 其次使用从文本解析到的 duration
      displayDuration = contentTool.duration;
    } else if (eventTool.status === 'running') {
      // 工具还在运行中
      displayDuration = 'Running...';
    }
    
    return {
      name: eventTool.name,
      args: eventTool.args,
      duration: displayDuration
    };
  });
};

// 清理消息内容，移除工具调用信息
const cleanMessageContent = (content: string): string => {
  if (!content) return content;
  
  // 如果没有检测到工具调用，直接返回原内容
  const hasToolCalls = /\w+\([^)]*\)\s+completed\s+in\s+[\d.]+s/g.test(content);
  if (!hasToolCalls) {
    return content;
  }
  
  console.log('🔍 原始内容:', content);
  
  // 移除工具调用文本（更精确的匹配）
  let cleaned = content.replace(/\w+\([^)]*\)\s+completed\s+in\s+[\d.]+s\.?\s*/g, '');
  
  // 清理连续多个空格，但保留换行符
  cleaned = cleaned.replace(/ {2,}/g, ' ');
  
  // 清理开头和结尾的空格
  cleaned = cleaned.trim();
  
  // 清理开头可能残留的标点符号
  cleaned = cleaned.replace(/^[.,;:\s]+/, '');
  
  console.log('✅ 清理后内容:', cleaned);
  
  return cleaned;
};

// 消息容器的引用
const messagesContainer = ref<HTMLElement | null>(null);

// 引导教程
const showTour = ref(false);

const tourSteps = [
  {
    target: '.chat-input-area',
    title: '输入问题',
    description: '在这里输入你的问题，可以询问电镀知识、请求预测分子性质，或者要求设计新的添加剂分子。',
  },
  {
    target: '.welcome-cards',
    title: '快捷功能',
    description: '点击这些卡片可以快速开始常见的任务：性质预测、分子设计、配方优化。',
  },
  {
    target: '.chat-header .header-actions',
    title: '更多操作',
    description: '点击信息图标可以重新查看引导教程，点击新对话按钮可以开始一个新的对话。',
  },
];

const startTour = () => {
  showTour.value = true;
};

const onTourFinish = () => {
  console.log('引导教程完成');
};

// 滚动到底部（仅在用户接近底部时自动滚动）
const SCROLL_THRESHOLD = 120; // 距离底部小于该值视为“接近底部”
const isUserNearBottom = ref(true);
let scrollTimer: number | null = null;

const updateScrollState = () => {
  if (!messagesContainer.value) return;
  const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value;
  const distanceToBottom = scrollHeight - (scrollTop + clientHeight);
  isUserNearBottom.value = distanceToBottom <= SCROLL_THRESHOLD;
};

const handleScroll = () => {
  updateScrollState();
};

const scrollToBottom = (smooth: boolean = false, debounce: boolean = false, force: boolean = false) => {
  if (!force && !isUserNearBottom.value) {
    return;
  }

  const doScroll = () => {
    nextTick(() => {
      if (messagesContainer.value) {
        if (smooth) {
          messagesContainer.value.scrollTo({
            top: messagesContainer.value.scrollHeight,
            behavior: 'smooth'
          });
        } else {
          // 立即跳转，不使用动画
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
      }
    });
  };

  if (debounce) {
    // 使用防抖，避免频繁滚动
    if (scrollTimer !== null) {
      clearTimeout(scrollTimer);
    }
    scrollTimer = window.setTimeout(() => {
      doScroll();
      scrollTimer = null;
    }, 50); // 50ms 防抖延迟
  } else {
    // 不使用防抖，立即滚动
    doScroll();
  }
};

// 获取 agents 列表
const fetchAgents = async () => {
  isLoadingAgents.value = true;
  try {
    const response = await fetch('/api/agents');
    if (response.ok) {
      const data = await response.json();
      agents.value = data;
      if (agents.value.length > 0 && agents.value[0]) {
        selectedAgentId.value = agents.value[0].id;
      }
    } else {
      console.error('Failed to fetch agents:', response.statusText);
      // 使用默认值
      agents.value = [{ id: 'default', name: '默认模型' }];
    }
  } catch (error) {
    console.error('Error fetching agents:', error);
    // 使用默认值
    agents.value = [{ id: 'default', name: '默认模型' }];
  } finally {
    isLoadingAgents.value = false;
  }
};

// 获取 session 详情并加载对话历史
const fetchSessionDetail = async (sessionId: string, silent: boolean = false) => {
  if (!sessionId) return;

  // 如果不是静默更新，显示加载状态
  if (!silent) {
    isLoadingSession.value = true;
  }

  try {
    // 如果还没有 dbId，先获取配置
    if (!dbId.value) {
      const configResponse = await fetch('/api/config');
      if (configResponse.ok) {
        const config = await configResponse.json();
        dbId.value = config.databases?.[0];
      }
    }
    
    if (!dbId.value) {
      console.error('[ChatPanel] No database ID found');
      if (!silent) {
        isLoadingSession.value = false;
      }
      return;
    }
    
    const params = new URLSearchParams({
      type: 'agent',
      db_id: dbId.value,
    });
    const response = await fetch(`${API_BASE_URL}/api/custom/sessions/${sessionId}?${params.toString()}`);
    if (response.ok) {
      const data: SessionDetail = await response.json();

      // 保存会话数据
      currentSessionData.value = data;

      console.log('[DEBUG] Fetched session data:', data);
      console.log('[DEBUG] Chat history length:', data.chat_history?.length);
      if (data.chat_history && data.chat_history.length > 0) {
        console.log('[DEBUG] First few messages:', data.chat_history.slice(0, 5).map(m => ({
          role: m.role,
          run_id: m.run_id,
          content: m.content?.substring(0, 50)
        })));
      }

      // 记录清空前的消息数量（用于判断是否有新消息）
      const apiMessageCount = data.chat_history?.length || 0;
      const previousMessageCount = silent ? lastMessageCount.value : 0;


      // 静默模式下，如果消息数量没变，直接返回（避免不必要的 DOM 更新）
      if (silent && apiMessageCount === previousMessageCount) {
        console.log('[ChatPanel] No new messages in silent mode, skipping update');
        return;
      }

      // 清空当前消息
      messages.length = 0;

      // 转换 chat_history 为 ChatMessage 格式
      // 使用 Map 来按 run_id 合并同一轮对话的 assistant 消息
      let messageId = 1;
      let assistantMessageIndex = 0;

      // 用于追踪每个 run_id 对应的消息（user、assistant 和 tool results）
      const runIdToMessagesMap = new Map<string, {user?: ChatMessage, assistant?: ChatMessage, toolResults?: ChatMessage[]}>();

      data.chat_history.forEach((apiMsg: ApiMessage) => {
        const apiReasoningSteps = extractReasoningStepsFromApiMessage(apiMsg);

        if (isReasoningEventMessage(apiMsg)) {
          appendReasoningStepsToLatestAssistant(sessionId, apiMsg.run_id, apiReasoningSteps);
          return;
        }

        // Check for RunCompleted
        if (getApiMessageEventType(apiMsg) === 'RunCompleted') {
          return;
        }

        // 处理 tool 消息（工具结果）
        if (apiMsg.role === 'tool') {
          const runId = apiMsg.run_id || `no-run-id-${messageId}`;
          const toolMsg: ChatMessage = {
            id: messageId++,
            role: 'tool',
            content: apiMsg.content || '',
            timestamp: apiMsg.created_at * 1000,
            runId: runId,
            toolCallId: apiMsg.tool_call_id,
            toolCalls: [{
              id: apiMsg.tool_call_id || '',
              name: apiMsg.tool_name,
              args: apiMsg.tool_args,
              result: apiMsg.content,
              status: apiMsg.tool_call_error ? 'error' : 'completed',
            }]
          };

          const existing = runIdToMessagesMap.get(runId);
          if (existing) {
            // 将工具结果添加到已存在的记录中
            // 我们需要一个新的字段来存储工具结果
            if (!existing.toolResults) {
              existing.toolResults = [];
            }
            existing.toolResults.push(toolMsg);
          } else {
            runIdToMessagesMap.set(runId, { toolResults: [toolMsg] });
          }
          return;
        }

        // 处理 user 消息
        if (apiMsg.role === 'user') {
          const runId = apiMsg.run_id || `no-run-id-${messageId}`;
          const userMsg: ChatMessage = {
            id: messageId++,
            role: 'user',
            content: apiMsg.content,
            timestamp: apiMsg.created_at * 1000,
            runId: runId,
          };

          const existing = runIdToMessagesMap.get(runId);
          if (existing) {
            existing.user = userMsg;
          } else {
            runIdToMessagesMap.set(runId, { user: userMsg });
          }
          return;
        }

        // 处理 assistant 消息 - 按 run_id 合并
        if (apiMsg.role === 'assistant') {
          const runId = apiMsg.run_id || `no-run-id-${messageId}`;

          const existing = runIdToMessagesMap.get(runId);

          if (existing?.assistant) {
            // 合并到已存在的 assistant 消息
            const existingAssistant = existing.assistant;

            // 合并内容：追加新内容（如果内容不同）
            if (apiMsg.content && !existingAssistant.content.includes(apiMsg.content)) {
              existingAssistant.content += apiMsg.content;
            }

            // 合并工具调用
            if (apiMsg.tool_calls && apiMsg.tool_calls.length > 0) {
              if (!existingAssistant.toolCalls) {
                existingAssistant.toolCalls = [];
              }
              apiMsg.tool_calls.forEach((tc: any) => {
                const toolId = tc.id || tc.tool_call_id;
                // 避免重复添加
                if (!existingAssistant.toolCalls!.find(t => t.id === toolId)) {
                  existingAssistant.toolCalls!.push({
                    id: toolId,
                    name: tc.function?.name || tc.name || tc.tool_name,
                    args: tc.function?.arguments || tc.args || tc.tool_args,
                    status: 'completed',
                  });
                }
              });
            }

            // 合并推理步骤
            const newReasoningSteps = apiReasoningSteps.length > 0 ? apiReasoningSteps : [];
            if (newReasoningSteps.length > 0) {
              if (!existingAssistant.reasoningSteps) {
                existingAssistant.reasoningSteps = [];
              }
              existingAssistant.reasoningSteps.push(...newReasoningSteps);
            }
          } else {
            // 创建新的 assistant 消息
            const cachedReasoning = getPersistedReasoningSteps(sessionId, runId, assistantMessageIndex);
            const reasoningSteps = apiReasoningSteps.length > 0 ? apiReasoningSteps : cachedReasoning;
            assistantMessageIndex++;

            const assistantMsg: ChatMessage = {
              id: messageId++,
              role: 'assistant',
              content: apiMsg.content || '',
              timestamp: apiMsg.created_at * 1000,
              runId: runId,
              reasoningSteps: reasoningSteps.length > 0 ? reasoningSteps : undefined,
              toolCalls: apiMsg.tool_calls?.map((tc: any) => ({
                id: tc.id || tc.tool_call_id,
                name: tc.function?.name || tc.name || tc.tool_name,
                args: tc.function?.arguments || tc.args || tc.tool_args,
                status: 'completed',
              })),
            };

            if (existing) {
              existing.assistant = assistantMsg;
            } else {
              runIdToMessagesMap.set(runId, { assistant: assistantMsg });
            }
          }
        }
      });

      // 按原始顺序添加消息到 messages 数组
      // 由于 Map 不能保证顺序，我们按 run_id 的出现顺序来添加
      // 同时确保每个 run_id 的 user 消息在 assistant 消息之前
      runIdToMessagesMap.forEach((msgPair) => {
        if (msgPair.user) {
          messages.push(msgPair.user);
        }
        if (msgPair.assistant) {
          messages.push(msgPair.assistant);
        }
        if (msgPair.toolResults) {
          msgPair.toolResults.forEach(toolMsg => messages.push(toolMsg));
        }
      });
      
      // 更新当前 session ID
      currentSessionId.value = sessionId;
      
      // 更新选中的 agent
      if (data.agent_id) {
        selectedAgentId.value = data.agent_id;
      }

      console.log(`Loaded session ${sessionId} with ${messages.length} messages`);

      // 检测是否有新消息（用于静默更新）
      const newMessageCount = messages.length;
      const hasNewMessages = silent && newMessageCount > previousMessageCount;

      if (hasNewMessages) {
        console.log(`[ChatPanel] ${newMessageCount - previousMessageCount} new messages detected`);
      }

      // 更新消息数量（用于轮询检测）
      lastMessageCount.value = newMessageCount;

      // 结束加载状态
      if (!silent) {
        isLoadingSession.value = false;
      }

      // 如果有新消息或非静默模式，滚动到底部
      if (hasNewMessages || !silent) {
        // 使用 nextTick 等待 DOM 更新完成后再滚动
        await nextTick();
        // 非静默加载强制滚动；静默更新则仅在用户接近底部时滚动
        scrollToBottom(false, false, !silent);
      }
    } else {
      console.error('Failed to fetch session detail:', response.statusText);
      if (!silent) {
        isLoadingSession.value = false;
      }
    }
  } catch (error) {
    console.error('Error fetching session detail:', error);
    if (!silent) {
      isLoadingSession.value = false;
    }
  }
};

// 监听 selectedSessionId 变化
watch(() => props.selectedSessionId, (newSessionId, oldSessionId) => {
  console.log('[ChatPanel] selectedSessionId changed:', { oldSessionId, newSessionId });

  // 停止旧会话的轮询
  if (oldSessionId) {
    stopPolling();
  }

  if (newSessionId && newSessionId !== oldSessionId) {
    console.log('[ChatPanel] Loading session:', newSessionId);
    fetchSessionDetail(newSessionId).then(() => {
      // 加载完成后启动轮询
      startPolling();
    });
  } else if (!newSessionId) {
    // 如果没有新会话，清空消息
    messages.length = 0;
    lastMessageCount.value = 0;
  }
}, { immediate: true });

// 开始新对话
const startNewChat = () => {
  // 清空当前会话
  currentSessionId.value = '';
  messages.length = 0;

  toast.success('新对话', { description: '已开始新的对话会话', duration: 2000 });
  closeEventStream();
};

// 切换智能体
const handleAgentSwitch = (newAgentId: string) => {
  // 如果点击的是当前智能体，不做任何操作
  if (newAgentId === selectedAgentId.value) {
    return;
  }

  // 如果当前有对话内容，需要先保存或提示用户
  const hasMessages = messages.length > 0;

  // 切换智能体
  selectedAgentId.value = newAgentId;

  // 清空当前对话和session，开始新对话
  currentSessionId.value = '';
  messages.length = 0;

  // 提示用户
  if (hasMessages) {
    toast.success('智能体已切换', { description: '已开始新的对话会话', duration: 2000 });
  }
};

// 轮询函数 - 检查会话是否有新消息（指数退避策略）
const startPolling = () => {
  // 清除已有的轮询
  stopPolling();

  // 重置轮询间隔
  currentPollingInterval = MIN_POLLING_INTERVAL;
  consecutiveEmptyPolls.value = 0;

  // 只有在有当前会话时才启动轮询
  if (currentSessionId.value) {
    console.log('[ChatPanel] Starting exponential backoff polling for session:', currentSessionId.value);

    const poll = async () => {
      if (!currentSessionId.value) {
        stopPolling();
        return;
      }

      // 如果正在生成回答，跳过此次轮询（session 可能还未建立）
      if (isGenerating.value) {
        console.log('[ChatPanel] Response is generating, skipping polling');
        pollingInterval.value = window.setTimeout(poll, currentPollingInterval);
        return;
      }

      try {
        // 先检查会话的 updated_at 时间戳
        if (!dbId.value) return;

        const params = new URLSearchParams({
          type: 'agent',
          db_id: dbId.value,
        });
        const response = await fetch(`${API_BASE_URL}/api/custom/sessions/${currentSessionId.value}?${params.toString()}`);

        if (response.ok) {
          const data: SessionDetail = await response.json();

          // 只有当消息数量变化时才重新加载
          const currentMessageCount = data.chat_history?.length || 0;

          if (currentMessageCount > lastMessageCount.value) {
            console.log(`[ChatPanel] New messages detected! ${lastMessageCount.value} -> ${currentMessageCount}`);

            // 有新消息！重置间隔为最快速度
            currentPollingInterval = MIN_POLLING_INTERVAL;
            consecutiveEmptyPolls.value = 0;

            // 先更新 lastMessageCount，避免重复触发
            const previousCount = lastMessageCount.value;

            // 重新加载会话详情（静默模式）
            await fetchSessionDetail(currentSessionId.value, true);
            lastMessageCount.value = currentMessageCount;

            // 显示提示
            toast.success('新消息', {
              description: `收到 ${currentMessageCount - previousCount} 条新消息`,
              duration: 3000,
            });

            console.log(`[ChatPanel] New messages! Reset polling interval to ${currentPollingInterval}ms`);
          } else {
            // 没有新消息，增加轮询间隔（指数退避）
            consecutiveEmptyPolls.value++;

            if (consecutiveEmptyPolls.value >= 2) {
              // 连续 2 次没有新消息，开始增加间隔
              currentPollingInterval = Math.min(
                Math.floor(currentPollingInterval * POLLING_INCREASE_FACTOR),
                MAX_POLLING_INTERVAL
              );

              console.log(`[ChatPanel] No new messages (${consecutiveEmptyPolls.value}x). Increasing polling interval to ${currentPollingInterval}ms`);
            }
          }
        } else if (response.status === 404) {
          // Session 不存在（可能已被删除），停止轮询
          console.warn('[ChatPanel] Session not found (404), stopping polling:', currentSessionId.value);
          stopPolling();
          currentSessionId.value = '';
          lastMessageCount.value = 0;
          return;
        }
      } catch (error) {
        console.error('[ChatPanel] Polling error:', error);
        // 出错时也增加间隔，避免频繁重试
        currentPollingInterval = Math.min(
          Math.floor(currentPollingInterval * POLLING_INCREASE_FACTOR),
          MAX_POLLING_INTERVAL
        );
      }

      // 重新调度下一次轮询（使用动态间隔）
      pollingInterval.value = window.setTimeout(poll, currentPollingInterval);
    };

    // 启动第一次轮询
    pollingInterval.value = window.setTimeout(poll, currentPollingInterval);
  }
};

// 停止轮询
const stopPolling = () => {
  if (pollingInterval.value) {
    console.log('[ChatPanel] Stopping polling');
    clearTimeout(pollingInterval.value); // 使用 clearTimeout 而不是 clearInterval
    pollingInterval.value = null;
  }
};

// 组件挂载时获取 agents
onMounted(() => {
  fetchAgents();
  loadUserProfile();

  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.addEventListener('scroll', handleScroll, { passive: true });
      updateScrollState();
    }
  });

  // 检查是否需要显示引导教程（首次访问）
  const tourCompleted = localStorage.getItem('tour-completed');
  if (!tourCompleted) {
    // 延迟一点启动，确保页面渲染完成
    setTimeout(() => {
      startTour();
    }, 1000);
  }

  // 如果有初始的 selectedSessionId，加载它
  if (props.selectedSessionId) {
    console.log('[ChatPanel] Initial session load:', props.selectedSessionId);
    fetchSessionDetail(props.selectedSessionId).then(() => {
      // 加载完成后启动轮询
      startPolling();
    });
  }

  // 监听 localStorage 变化（跨标签页同步）
  window.addEventListener('storage', (e) => {
    if (e.key === 'userProfile' && e.newValue) {
      try {
        userProfile.value = JSON.parse(e.newValue);
      } catch (error) {
        console.error('Failed to parse user profile:', error);
      }
    }
  });
});

// 组件卸载时清理轮询
onUnmounted(() => {
  stopPolling();
  if (messagesContainer.value) {
    messagesContainer.value.removeEventListener('scroll', handleScroll);
  }
});

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化会话时间
const formatSessionTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 复制消息内容
const copyMessage = async (content: string) => {
  // 备用方案：使用传统的 textarea 方法
  const fallbackCopy = async (text: string): Promise<boolean> => {
    try {
      // 创建一个临时 textarea
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';
      document.body.appendChild(textarea);

      // 选择并复制
      textarea.select();
      textarea.setSelectionRange(0, text.length);
      const successful = document.execCommand('copy');

      // 清理
      document.body.removeChild(textarea);

      return successful;
    } catch (error) {
      console.error('备用复制方法失败:', error);
      return false;
    }
  };

  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(content);
      toast.success('复制成功', { description: '消息已复制到剪贴板', duration: 2000 });
    } else {
      // 如果不支持或不安全，使用备用方案
      const success = await fallbackCopy(content);
      if (success) {
        toast.success('复制成功', { description: '消息已复制到剪贴板', duration: 2000 });
      } else {
        throw new Error('复制失败');
      }
    }
  } catch (error) {
    console.error('复制失败:', error);
    toast.error('复制失败', { description: '无法访问剪贴板，请手动选择文本复制', duration: 3000 });
  }
};

// 打开分享对话框
const openShareDialog = () => {
  // 默认选中所有消息
  selectedMessageIds.value = new Set(messages.map(m => m.id));
  selectAllMessages.value = true;
  shareDialogOpen.value = true;
};

// 切换消息选中状态
const toggleMessageSelection = (messageId: string) => {
  if (selectedMessageIds.value.has(messageId)) {
    selectedMessageIds.value.delete(messageId);
  } else {
    selectedMessageIds.value.add(messageId);
  }
  // 更新全选状态
  selectAllMessages.value = selectedMessageIds.value.size === messages.length;
};

// 全选/取消全选
const toggleSelectAll = () => {
  if (selectAllMessages.value) {
    // 取消全选
    selectedMessageIds.value.clear();
    selectAllMessages.value = false;
  } else {
    // 全选
    selectedMessageIds.value = new Set(messages.map(m => m.id));
    selectAllMessages.value = true;
  }
};

// 获取选中的消息
const getSelectedMessages = (): ChatMessage[] => {
  return messages.filter(m => selectedMessageIds.value.has(m.id));
};

// 导出为文本
const exportAsText = () => {
  const selectedMessages = getSelectedMessages();

  if (selectedMessages.length === 0) {
    toast.error('请选择消息', { description: '请至少选择一条消息进行导出', duration: 2000 });
    return;
  }

  try {
    // 构建对话内容
    let content = `AI4EC eagent\n`;
    content += `╔═══════════════════════════════════════════════════════════╗\n`;
    content += `║  ${selectedAgentName.value} - 对话记录\n`;
    content += `║  导出时间: ${new Date().toLocaleString('zh-CN')}\n`;
    content += `║  共 ${selectedMessages.length} 条消息\n`;
    content += `╚═══════════════════════════════════════════════════════════╝\n\n`;

    for (let i = 0; i < selectedMessages.length; i++) {
      const message = selectedMessages[i];
      const role = message.role === 'user' ? '👤 用户' : '🤖 助手';

      content += `┌───────────────────────────────────────────────────────────┐\n`;
      content += `│ ${role}  |  ${formatTime(message.timestamp).padEnd(25)} │\n`;
      content += `└───────────────────────────────────────────────────────────┘\n`;
      content += `${message.content}\n\n`;

      if (i < selectedMessages.length - 1) {
        content += `─────────────────────────────────────────────────────────────\n\n`;
      }
    }

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('导出成功', { description: `已导出 ${selectedMessages.length} 条消息`, duration: 2000 });
    shareDialogOpen.value = false;
  } catch (error) {
    console.error('导出文本失败:', error);
    toast.error('导出失败', { description: '导出文本文件时出错', duration: 2000 });
  }
};

// 导出为图片
const exportAsImage = async () => {
  const selectedMessages = getSelectedMessages();

  if (selectedMessages.length === 0) {
    toast.error('请选择消息', { description: '请至少选择一条消息进行导出', duration: 2000 });
    return;
  }

  // 显示正在生成提示
  toast('正在生成图片', { description: '请稍候...', duration: 2000 });

  try {
    // 动态导入 html2canvas - 已改为静态导入以避免 Nginx 代理下的 404 问题
    // const html2canvas = (await import('html2canvas')).default;

    // 创建隔离的 iframe 来避免 Tailwind 样式干扰
    const iframe = document.createElement('iframe');
    iframe.style.cssText = `
      position: fixed;
      top: -9999px;
      left: -9999px;
      width: 800px;
      height: 800px;
      border: none;
    `;
    document.body.appendChild(iframe);

    const iframeDoc = iframe.contentDocument || iframe.contentWindow!.document;
    iframeDoc.open();
    iframeDoc.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Microsoft YaHei", sans-serif;
            width: 800px;
            padding: 36px 40px 44px;
            background: #f7f8fb;
            color: #0f172a;
          }
          .header {
            text-align: left;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e6e9f2;
          }
          .brand {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 600;
            color: #2563eb;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            margin-bottom: 10px;
          }
          .brand-dot {
            width: 8px;
            height: 8px;
            border-radius: 999px;
            background: #2563eb;
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.12);
          }
          .header h1 {
            font-size: 22px;
            font-weight: 600;
            margin: 0 0 6px 0;
            color: #0f172a;
            letter-spacing: 0.2px;
          }
          .header p {
            font-size: 12px;
            color: #64748b;
            margin: 0;
          }
          .message {
            background: #ffffff;
            border-radius: 14px;
            padding: 18px 20px;
            margin-bottom: 16px;
            border: 1px solid #edf0f7;
            box-shadow: 0 6px 18px rgba(15, 23, 42, 0.06);
          }
          .message.assistant {
            background: #ffffff;
            border-color: #e8f0ff;
          }
          .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eef2f7;
          }
          .role {
            font-weight: 600;
            font-size: 13px;
            color: #0f172a;
          }
          .role.user {
            color: #1d4ed8;
          }
          .role.assistant {
            color: #0f766e;
          }
          .timestamp {
            color: #94a3b8;
            font-size: 11px;
          }
          .message-content {
            color: #1f2937;
            font-size: 14px;
            line-height: 1.7;
            word-wrap: break-word;
            word-break: break-word;
            overflow-wrap: anywhere;
            max-width: 100%;
          }
          pre {
            background: #f8fafc;
            padding: 12px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 10px 0;
            max-width: 100%;
            border: 1px solid #eef2f7;
          }
          code {
            font-family: monospace;
          }
          :not(pre) > code {
            background: #eef2ff;
            padding: 2px 6px;
            border-radius: 6px;
            font-size: 13px;
            color: #3730a3;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 13px;
            table-layout: fixed;
            max-width: 100%;
            overflow-wrap: anywhere;
            border: 1px solid #eef2f7;
            border-radius: 10px;
            overflow: hidden;
          }
          th, td {
            border-bottom: 1px solid #eef2f7;
            padding: 8px 10px;
            text-align: left;
            word-break: break-word;
          }
          th {
            background: #f8fafc;
            font-weight: 600;
            color: #334155;
          }
          tr:nth-child(even) td {
            background: #fafbff;
          }
          img, svg, canvas {
            max-width: 100%;
            height: auto;
          }
        </style>
      </head>
      <body>
    `);

    // 构建HTML内容
    let html = `
      <div class="header">
        <div class="brand"><span class="brand-dot"></span>AI4EC eagent</div>
        <h1>${selectedAgentName.value}</h1>
        <p>${new Date().toLocaleString('zh-CN')}  ·  共 ${selectedMessages.length} 条消息</p>
      </div>
    `;

    for (const message of selectedMessages) {
      const role = message.role === 'user' ? '👤 用户' : '🤖 助手';
      const roleClass = message.role === 'user' ? 'user' : 'assistant';

      html += `
        <div class="message ${roleClass}">
          <div class="message-header">
            <span class="role ${roleClass}">${role}</span>
            <span class="timestamp">·</span>
            <span class="timestamp">${formatTime(message.timestamp)}</span>
          </div>
          <div class="message-content">
            ${renderMarkdown(message.content)}
          </div>
        </div>
      `;
    }

    // 写入内容并关闭文档
    iframeDoc.write(html);
    iframeDoc.write('</body></html>');
    iframeDoc.close();

    // 等待 iframe 加载完成
    await new Promise(resolve => setTimeout(resolve, 200));

    // 使用 iframe 的 body 作为 canvas 目标
    const canvas = await html2canvas(iframeDoc.body, {
      scale: 2, // 提高清晰度
      useCORS: true,
      backgroundColor: '#667eea',
      logging: false,
    });

    // 移除 iframe
    document.body.removeChild(iframe);

    // 导出为图片
    canvas.toBlob((blob) => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('导出成功', { description: `已导出 ${selectedMessages.length} 条消息`, duration: 2000 });
      shareDialogOpen.value = false;
    }, 'image/png');
  } catch (error) {
    console.error('导出图片失败:', error);
    toast.error('导出失败', { description: '生成图片时出错', duration: 2000 });
  }
};

// 简单的 Markdown 渲染函数
const renderMarkdown = (text: string): string => {
  // 基本的 Markdown 转换
  let html = text
    // 转义 HTML
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    // 表格（必须先处理）
    .replace(/\|(.+)\|\n\|[-|\s]+\|\n((?:\|.+\|\n?)+)/g, (match, header, body) => {
      const headers = header.split('|').filter(h => h.trim()).map(h => `<th>${h.trim()}</th>`).join('');
      const rows = body.trim().split('\n').map(row => {
        const cells = row.split('|').filter(c => c.trim()).map(c => `<td>${c.trim()}</td>`).join('');
        return `<tr>${cells}</tr>`;
      }).join('');
      return `<table><thead><tr>${headers}</tr></thead><tbody>${rows}</tbody></table>`;
    })
    // 代码块（必须先处理，避免内部内容被替换）
    .replace(/```(\w*)\n([\s\S]*?)```/g, (match, lang, code) => {
      return `<pre><code>${code}</code></pre>`;
    })
    // 行内代码
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    // 粗体
    .replace(/\*\*\*([^*]+)\*\*\*/g, '<strong><em>$1</em></strong>')
    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
    // 斜体（注意避免匹配到粗体的一部分）
    .replace(/\*([^*\n]+)\*/g, '<em>$1</em>')
    // 标题
    .replace(/^### (.+)$/gm, '<h3>$1</h3>')
    .replace(/^## (.+)$/gm, '<h2>$1</h2>')
    .replace(/^# (.+)$/gm, '<h1>$1</h1>')
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // 无序列表
    .replace(/^\* (.+)$/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>\n?)+/g, '<ul>$&</ul>')
    // 有序列表
    .replace(/^\d+\. (.+)$/gm, '<li>$1</li>')
    // 换行（但不要在标签内换行）
    .replace(/\n(?![^<]*>)/g, '<br>');

  return html;
};

// 点赞消息
const likeMessage = (messageId: number) => {
  console.log('点赞消息:', messageId);
  toast.success('感谢反馈', { description: '👍 已标记为有帮助', duration: 2000 });
  // TODO: 发送到后端 API
};

// 踩消息
const dislikeMessage = (messageId: number) => {
  console.log('踩消息:', messageId);
  toast.success('感谢反馈', { description: '👎 已标记为无帮助', duration: 2000 });
  // TODO: 发送到后端 API
};

// 显示工具调用
const showEventStream = (messageId: number) => {
  // 找到该消息及之前的所有消息
  const messageIndex = messages.findIndex(m => m.id === messageId);
  if (messageIndex === -1) {
    toast.error('错误', { description: '无法找到该消息', duration: 2000 });
    return;
  }

  // 获取当前消息及其相关的工具调用和结果
  const targetMessage = messages[messageIndex];

  // 转换 ChatMessage 为 SessionMessage 格式
  const convertToSessionMessage = (msg: ChatMessage): any => {
    // 工具结果消息
    if (msg.role === 'tool') {
      return {
        content: msg.content,
        from_history: false,
        stop_after_tool_call: false,
        role: 'tool',
        tool_calls: undefined,
        tool_call_id: msg.toolCallId, // 工具调用 ID
        tool_name: msg.toolCalls?.[0]?.name,
        tool_args: undefined,
        tool_call_error: msg.toolCalls?.[0]?.status === 'error',
        created_at: msg.timestamp,
        run_id: msg.runId || '',
        metrics: undefined
      };
    }

    // 普通消息（user/assistant）
    return {
      content: msg.content,
      from_history: false,
      stop_after_tool_call: false,
      role: msg.role,
      tool_calls: msg.toolCalls?.map(tc => ({
        id: tc.id,
        type: 'function',
        function: {
          name: tc.name,
          arguments: tc.args || JSON.stringify({})
        }
      })),
      tool_call_id: undefined,
      tool_name: undefined,
      tool_args: undefined,
      tool_call_error: false,
      created_at: msg.timestamp,
      run_id: msg.runId || '',
      metrics: undefined
    };
  };

  const eventMessages = [convertToSessionMessage(targetMessage)];

  // 如果当前消息是 assistant 消息，查找它的所有工具调用和结果
  if (targetMessage.role === 'assistant' && targetMessage.runId) {
    // 查找同一 run_id 下的所有后续工具消息（tool calls 和 tool results）
    for (let i = messageIndex + 1; i < messages.length; i++) {
      const msg = messages[i];
      // 只收集同一 run_id 的工具调用和结果
      if ((msg.role === 'tool' || msg.role === 'assistant') && msg.runId === targetMessage.runId) {
        eventMessages.push(convertToSessionMessage(msg));
      } else if (msg.role === 'user') {
        // 遇到新的用户消息就停止
        break;
      }
    }
  }

  // 通过自定义事件通知 RightBar
  window.dispatchEvent(new CustomEvent('open-event-stream', {
    detail: { messages: eventMessages }
  }));

  toast.success('工具调用', { description: '已在右侧边栏打开工具调用查看器', duration: 2000 });
};

const closeEventStream = () => {
  window.dispatchEvent(new CustomEvent('close-event-stream'));
}

// 重新生成消息
const regenerateMessage = async (messageId: number) => {
  // 找到当前 AI 消息的索引
  const currentIndex = messages.findIndex(m => m.id === messageId);
  if (currentIndex === -1) {
    toast.error('错误', { description: '无法找到该消息', duration: 2000 });
    return;
  }

  // 找到上一条用户消息
  let userMessageContent = '';
  for (let i = currentIndex - 1; i >= 0; i--) {
    const msg = messages[i];
    if (msg && msg.role === 'user') {
      userMessageContent = msg.content;
      break;
    }
  }

  if (!userMessageContent) {
    toast.error('错误', { description: '找不到对应的用户消息', duration: 2000 });
    return;
  }

  if (!selectedAgentId.value) {
    toast.error('错误', { description: '请先选择一个模型', duration: 2000 });
    return;
  }

  // 删除当前的 AI 消息
  messages.splice(currentIndex, 1);

  // 添加加载占位消息
  const loadingId = Date.now();
  messages.push({
    id: loadingId,
    role: 'assistant',
    content: '正在重新生成...',
    timestamp: Date.now(),
  });

  // 滚动到底部（用户发起的操作，强制）
  scrollToBottom(false, false, true);

  try {
    // 使用 FormData 发送请求
    const formData = new FormData();
    formData.append('message', userMessageContent);
    formData.append('stream', 'false');
    
    // 添加当前用户ID
    if (authStore.user?.user_id) {
      formData.append('user_id', authStore.user.user_id);
    }
    
    // 如果有 session ID,添加到 FormData
    if (currentSessionId.value) {
      console.log('[ChatPanel] [Regen] Sending message with session_id:', currentSessionId.value);
      formData.append('session_id', currentSessionId.value);
    } else {
      console.log('[ChatPanel] [Regen] Sending message without session_id (new session will be created)');
    }

    const response = await fetch(`${API_BASE_URL}/api/agents/${encodeURIComponent(selectedAgentId.value)}/runs`, {
      method: 'POST',
      body: formData,
    });

    if (response.ok) {
      const data = await response.json();
      
      // 更新 session ID（如果是新会话）
      if (data.session_id && !currentSessionId.value) {
        currentSessionId.value = data.session_id;
        console.log('[ChatPanel] New session created:', data.session_id);
        emit('sessionCreated', data.session_id);
      }

      // 移除加载消息
      const loadingIndex = messages.findIndex(m => m.id === loadingId);
      if (loadingIndex !== -1) {
        messages.splice(loadingIndex, 1);
      }

      // 添加新的 AI 回复
      messages.push({
        id: Date.now(),
        role: 'assistant',
        content: data.content || '抱歉，我无法回答这个问题。',
        timestamp: Date.now(),
      });

      // 滚动到底部（用户发起的操作，强制）
      scrollToBottom(false, false, true);

      toast.success('重新生成成功', { description: '已生成新的回复', duration: 2000 });
    } else {
      // 错误处理
      const loadingIndex = messages.findIndex(m => m.id === loadingId);
      if (loadingIndex !== -1 && messages[loadingIndex]) {
        messages[loadingIndex].content = '抱歉，重新生成失败，请稍后再试。';
      }
      console.error('Failed to regenerate message:', response.statusText);

      toast.error('重新生成失败', { description: '请稍后再试', duration: 2000 });
    }
  } catch (error) {
    // 网络错误处理
    const loadingIndex = messages.findIndex(m => m.id === loadingId);
    if (loadingIndex !== -1 && messages[loadingIndex]) {
      messages[loadingIndex].content = '网络错误，请检查连接后重试。';
    }
    console.error('Error regenerating message:', error);

    toast.error('网络错误', { description: '请检查网络连接', duration: 2000 });
  }
};

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    for (let i = 0; i < target.files.length; i++) {
      const file = target.files[i];
      if (file) {
        // 防止重复添加
        if (!pendingFiles.value.some(f => f.name === file.name && f.size === file.size)) {
          pendingFiles.value.push(file);

          // 暂时不自动上传到 MinIO，等发送消息时一起处理
          toast.success('文件已选择', { description: `${file.name} 已添加到发送队列` });
        }
      }
    }
    console.log('Files selected:', pendingFiles.value.map(f => f.name));
  }
  // 清空文件输入框，允许重复选择同一个文件
  target.value = '';
};

// 移除暂存的某个文件
const removePendingFile = (index: number) => {
  pendingFiles.value.splice(index, 1);
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + ' ' + sizes[i];
};

const handleQuickAction = (action: string) => {
  let message = '';
  switch (action) {
    case 'analyze':
      message = '请告诉我 SPS（磺丙基磺酸钠）C(CS(=O)(=O)O)S(=O)(=O)[O-].[Na+]，在铜表面的吸附能。';
      break;
    case 'visualize':
      message = '使用分子生成工具generate_molecules_stream，寻找SC1NCCCN1的替代分子';
      break;
    case 'optimize':
      message = '请指导我进行分子结构优化';
      break;
    default:
      message = '你好！';
  }

  prompt.value = message;
  handleSend();
};

const handleStop = async () => {
  if (!currentRunId.value || !selectedAgentId.value) return;

  try {
    const response = await fetch(`${API_BASE_URL}/api/agents/${encodeURIComponent(selectedAgentId.value)}/runs/${encodeURIComponent(currentRunId.value)}/cancel`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      console.error('Failed to cancel run:', response.statusText);
      toast.error('停止生成失败');
    } else {
      toast.info('生成已停止');
    }
  } catch (error) {
    console.error('Error canceling run:', error);
    toast.error('停止生成出错');
  } finally {
    isGenerating.value = false;
    currentRunId.value = '';
  }
};

const handleSend = async () => {
  if (!prompt.value.trim() || !selectedAgentId.value || isGenerating.value) {
    return;
  }

  // 设置正在生成状态
  isGenerating.value = true;
  currentRunId.value = '';

  const userMessage = prompt.value.trim();
  const timestamp = Date.now();
  const attachedFiles = pendingFiles.value.slice(); // 保存附件文件

  // 添加用户消息
  messages.push({
    id: timestamp,
    role: 'user',
    content: userMessage,
    timestamp,
  });

  // 滚动到底部（用户发起的操作，强制）
  scrollToBottom(false, false, true);

  // 清空输入和文件
  prompt.value = '';
  pendingFiles.value = [];
  // 清空文件输入框
  const fileInput = document.getElementById('chat-file-upload') as HTMLInputElement;
  if (fileInput) {
    fileInput.value = '';
  }

  // 添加加载占位消息
  const loadingId = timestamp + 1;
  messages.push({
    id: loadingId,
    role: 'assistant',
    content: '正在思考...',
    timestamp: Date.now(),
  });

  // 滚动到底部显示加载消息（用户发起的操作，强制）
  scrollToBottom(false, false, true);

  try {
    // 使用 FormData 发送请求（恢复原始 Agno 格式）
    const formData = new FormData();
    formData.append('message', userMessage);
    formData.append('stream', 'true');
    
    // 添加当前用户ID，确保会话归属正确
    if (authStore.user?.user_id) {
      formData.append('user_id', authStore.user.user_id);
    }

    // 如果有 session ID,添加到请求体
    if (currentSessionId.value) {
      console.log('[ChatPanel] [Stream] Sending message with session_id:', currentSessionId.value);
      formData.append('session_id', currentSessionId.value);
    } else {
      console.log('[ChatPanel] [Stream] Sending message without session_id (new session will be created)');
    }

    // 添加文件到 FormData（如果有）
    if (attachedFiles.length > 0) {
      for (const file of attachedFiles) {
        formData.append('files', file);
      }
      console.log('[ChatPanel] Attaching files:', attachedFiles.map(f => f.name));
    }

    const response = await fetch(`${API_BASE_URL}/api/agents/${encodeURIComponent(selectedAgentId.value)}/runs`, {
      method: 'POST',
      body: formData,
    });

    // 提前退出：响应错误或没有响应体
    if (!response.ok || !response.body) {
      const loadingIndex = messages.findIndex(m => m.id === loadingId);
      if (loadingIndex !== -1 && messages[loadingIndex]) {
        messages[loadingIndex].content = '抱歉，发生了错误，请稍后再试。';
      }
      isGenerating.value = false;
      console.error('Failed to send message:', response.statusText);
      emit('messageCompleted');
      return;
    }

    // 移除加载消息
    const loadingIndex = messages.findIndex(m => m.id === loadingId);
    if (loadingIndex !== -1) {
      messages.splice(loadingIndex, 1);
    }

    // 添加空的 AI 回复消息，用于流式更新
    const aiMessageId = Date.now();
    messages.push({
      id: aiMessageId,
      role: 'assistant',
      content: '',
      timestamp: aiMessageId,
      isStreaming: true,
    });

    // 滚动到底部（用户发起的操作，强制）
    scrollToBottom(false, false, true);

    // 读取流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let currentEvent: string | null = null;

    const processSseEvent = (eventType: string | null, payload: string) => {
      const data = payload.trim();
      if (!data) {
        return;
      }
      
      try {
        const jsonData = JSON.parse(data);
        const msgIndex = messages.findIndex(m => m.id === aiMessageId);
        if (msgIndex === -1) {
          return;
        }

        const currentMessage = messages[msgIndex];
        if (!currentMessage) {
          return;
        }

        if (jsonData.run_id && !currentMessage.runId) {
          currentMessage.runId = jsonData.run_id;
          currentRunId.value = jsonData.run_id;
        }

        const eventTypeResolved = eventType || jsonData.event;

        if (eventTypeResolved === 'RunStarted') {
          if (jsonData.session_id && !currentSessionId.value) {
            currentSessionId.value = jsonData.session_id;
            console.log('[ChatPanel] New session created:', jsonData.session_id);
            emit('sessionCreated', jsonData.session_id);
          }
          return;
        }

        if (eventTypeResolved === 'ReasoningStarted') {
          if (!currentMessage.reasoningSteps) {
            currentMessage.reasoningSteps = [];
          }
          console.log('[ChatPanel] Reasoning started');
          return;
        }

        if (eventTypeResolved === 'ReasoningStep') {
          if (!currentMessage.reasoningSteps) {
            currentMessage.reasoningSteps = [];
          }

          const step: ReasoningStep = {
            title: jsonData.content?.title || '推理步骤',
            action: jsonData.content?.action,
            result: jsonData.content?.result,
            reasoning: jsonData.content?.reasoning,
            next_action: jsonData.content?.next_action,
            confidence: jsonData.content?.confidence,
            reasoning_content: jsonData.reasoning_content || jsonData.content?.reasoning_content || '',
            timestamp: jsonData.created_at ? jsonData.created_at * 1000 : Date.now(),
          };

          currentMessage.reasoningSteps.push(step);
          console.log('[ChatPanel] Reasoning step added:', step.title);
          scrollToBottom(false, true); // 使用防抖
          if (currentSessionId.value) {
            // 计算当前 assistant 消息的索引
            const assistantIndex = messages.filter(m => m.role === 'assistant').length - 1;
            persistReasoningSteps(currentSessionId.value, currentMessage.runId || '', currentMessage.reasoningSteps, assistantIndex);
          }
          return;
        }

        if (eventTypeResolved === 'ToolCallStarted') {
          if (!currentMessage.toolCalls) {
            currentMessage.toolCalls = [];
          }
          // 从 jsonData.tool 中提取工具信息
          const toolInfo = jsonData.tool || {};
          const toolCallId = toolInfo.tool_call_id || jsonData.tool_call_id || Date.now().toString();
          const toolName = toolInfo.tool_name || jsonData.tool_name;
          const toolArgs = toolInfo.tool_args || jsonData.tool_args;

          // Check if already exists (deduplication)
          const existing = currentMessage.toolCalls.find(t => t.id === toolCallId);
          if (!existing) {
            currentMessage.toolCalls.push({
              id: toolCallId,
              name: toolName,
              args: typeof toolArgs === 'object' ? JSON.stringify(toolArgs) : toolArgs,
              status: 'running',
            });
          }
          console.log('[ChatPanel] Tool call started:', toolName);
          scrollToBottom(false, true); // 使用防抖

          // 自动打开工具调用查看器
          showEventStream(aiMessageId);

          return;
        }

        if (eventTypeResolved === 'ToolCallCompleted') {
          if (currentMessage.toolCalls) {
            // 从 jsonData.tool 中提取工具信息
            const toolInfo = jsonData.tool || {};
            const toolCallId = toolInfo.tool_call_id || jsonData.tool_call_id;
            const toolName = toolInfo.tool_name || jsonData.tool_name;
            const duration = toolInfo.metrics?.duration;
            
            // 尝试通过 tool_call_id 匹配
            let tool = currentMessage.toolCalls.find(t => t.id === toolCallId);
            // 如果没找到，尝试通过 tool_name 匹配最后一个 running 状态的工具
            if (!tool && toolName) {
              tool = [...currentMessage.toolCalls].reverse().find(t => t.name === toolName && t.status === 'running');
            }
            // 如果还没找到，直接标记最后一个 running 状态的工具
            if (!tool) {
              tool = [...currentMessage.toolCalls].reverse().find(t => t.status === 'running');
            }
            if (tool) {
              tool.status = 'completed';
              tool.result = toolInfo.result || jsonData.result || jsonData.content;
              // 设置执行时间
              if (duration !== undefined) {
                tool.duration = `${duration.toFixed(4)}s`;
              }
              console.log('[ChatPanel] Tool call completed:', tool.name, tool.duration);
            } else {
              console.warn('[ChatPanel] ToolCallCompleted but no matching tool found:', jsonData);
            }
          }
          scrollToBottom(false, true); // 使用防抖
          return;
        }

        const isContentEvent = eventTypeResolved === 'RunContent' ||
                               eventTypeResolved === 'TextMessageChunk' ||
                               (jsonData.content && typeof jsonData.content === 'string' && eventTypeResolved !== 'ReasoningStep' && eventTypeResolved !== 'RunCompleted' && eventTypeResolved !== 'ToolCallStarted' && eventTypeResolved !== 'ToolCallCompleted');

        if (isContentEvent) {
          const content = typeof jsonData.content === 'string'
            ? jsonData.content
            : jsonData.content?.text || '';

          if (content) {
            currentMessage.content += content;
            scrollToBottom(false, true); // 使用防抖
          }
          return;
        }

        if (eventTypeResolved) {
          console.log('[ChatPanel] Unhandled event:', eventTypeResolved, jsonData);
        }
      } catch (e) {
        console.error('[ChatPanel] Failed to parse SSE data:', e, 'Raw data:', payload);
      }
    };

    while (true) {
      if (!isGenerating.value) {
        await reader.cancel();
        break;
      }
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      buffer += decoder.decode(value, { stream: true });

      while (true) {
        const newlineIndex = buffer.indexOf('\n');
        if (newlineIndex === -1) {
          break;
        }

        let line = buffer.slice(0, newlineIndex);
        buffer = buffer.slice(newlineIndex + 1);

        if (line.endsWith('\r')) {
          line = line.slice(0, -1);
        }

        const trimmedLine = line.trimEnd();

        if (trimmedLine === '') {
          currentEvent = null;
          continue;
        }

        if (trimmedLine.startsWith('event:')) {
          currentEvent = trimmedLine.slice(6).trim();
          continue;
        }

        if (trimmedLine.startsWith('data:')) {
          const dataPart = trimmedLine.slice(5).trimStart();
          processSseEvent(currentEvent, dataPart);
          continue;
        }
      }
    }

    // 标记流式传输结束
    const msgIndex = messages.findIndex(m => m.id === aiMessageId);
    if (msgIndex !== -1 && messages[msgIndex]) {
      messages[msgIndex].isStreaming = false;
      
      const aiMessage = messages[msgIndex];
      
      // 流结束时，尝试从文本内容中提取工具调用信息并更新 toolCalls
      const parsedToolCalls = getToolCallsFromMessage(aiMessage);
      console.log('[ChatPanel] Stream ended. Parsed tool calls from content:', parsedToolCalls);
      console.log('[ChatPanel] Current toolCalls from events:', aiMessage.toolCalls);
      
      if (aiMessage.toolCalls && aiMessage.toolCalls.length > 0) {
        // 更新事件工具调用的状态和 duration
        aiMessage.toolCalls.forEach(tool => {
          // 尝试从文本解析结果中找到对应的工具调用
          const parsed = parsedToolCalls.find(p => p.name === tool.name);
          if (parsed?.duration) {
            tool.duration = parsed.duration;
            tool.status = 'completed';
            console.log('[ChatPanel] Updated tool with duration:', tool.name, tool.duration);
          } else if (tool.status === 'running') {
            tool.status = 'completed';
            console.log('[ChatPanel] Marking tool as completed (no duration):', tool.name);
          }
        });
      } else if (parsedToolCalls.length > 0) {
        // 如果没有事件数据但有文本解析数据，使用解析的数据
        aiMessage.toolCalls = parsedToolCalls.map(p => ({
          id: `parsed-${Date.now()}-${p.name}`,
          name: p.name,
          args: p.args,
          status: 'completed' as const,
          duration: p.duration,
        }));
        console.log('[ChatPanel] Created toolCalls from parsed content:', aiMessage.toolCalls);
      }
      
      // 流结束时确保推理步骤被保存到缓存
      if (currentSessionId.value && aiMessage.reasoningSteps && aiMessage.reasoningSteps.length > 0) {
        const assistantIndex = messages.filter(m => m.role === 'assistant').indexOf(aiMessage);
        persistReasoningSteps(currentSessionId.value, aiMessage.runId || '', aiMessage.reasoningSteps, assistantIndex);
        console.log('[ChatPanel] Final reasoning steps persisted:', { 
          sessionId: currentSessionId.value, 
          runId: aiMessage.runId,
          assistantIndex,
          stepCount: aiMessage.reasoningSteps.length 
        });
      }
    }

    // 重置生成状态
    isGenerating.value = false;

    // 最后滚动到底部
    scrollToBottom();
  } catch (error) {
    // 网络错误处理
    const loadingIndex = messages.findIndex(m => m.id === loadingId);
    if (loadingIndex !== -1 && messages[loadingIndex]) {
      messages[loadingIndex].content = '网络错误，请检查连接后重试。';
    }
    
    // 重置生成状态
    isGenerating.value = false;
    
    console.error('Error sending message:', error);
  } finally {
    // 无论成功还是失败，都发出消息完成事件，刷新文件列表
    emit('messageCompleted');
    currentRunId.value = '';
  }
};

// 获取带有光标的消息内容
const getMessageContentWithCursor = (message: ChatMessage) => {
  const content = cleanMessageContent(message.content);
  if (message.isStreaming) {
    return content + '<span class="streaming-cursor"></span>';
  }
  return content;
};

// 暴露方法给父组件
defineExpose({
  fetchSessionDetail,
  refreshMessages: () => {
    if (currentSessionId.value) {
      fetchSessionDetail(currentSessionId.value);
    }
  }
});

// 通知父组件 ChatPanel 已挂载
onMounted(() => {
  emit('chatPanelMounted', {
    fetchSessionDetail,
    refreshMessages: () => {
      if (currentSessionId.value) {
        fetchSessionDetail(currentSessionId.value);
      }
    }
  });
});

onBeforeRouteLeave(() => {
  closeEventStream();
})
</script>

<style scoped>
.chat-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid hsl(var(--border));
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.title {
  font-weight: 600;
  font-size: 18px;
  flex-shrink: 0;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.new-chat-btn svg {
  width: 16px;
  height: 16px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
  align-items: center;
  scroll-behavior: smooth; /* 平滑滚动 */
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

/* 欢迎页面样式 */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.welcome-header {
  margin-bottom: 3rem;
}

/* Logo 样式 */
.welcome-logo {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.logo-image {
  max-width: 400px;
  width: 100%;
  height: auto;
  filter: drop-shadow(0 8px 32px hsl(var(--primary) / 0.3));
  animation: logoFloat 3s ease-in-out infinite, logoGlow 2s ease-in-out infinite;
  transition: transform 0.3s ease, opacity 0.5s ease, filter 0.5s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 8px 32px hsl(var(--primary) / 0.3))
            brightness(1);
  }
  50% {
    filter: drop-shadow(0 12px 48px hsl(var(--primary) / 0.5))
            drop-shadow(0 0 24px hsl(var(--primary) / 0.4))
            brightness(1.1);
  }
}

.welcome-icon {
  margin-bottom: 1rem;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.welcome-description {
  font-size: 1rem;
  color: hsl(var(--muted-foreground));
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.welcome-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 900px;
  width: 100%;
}

.welcome-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid hsl(var(--border));
  position: relative;
  overflow: hidden;
}

/* 分子分析卡片 - 蓝色主题 */
.welcome-card.card-analyze {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.08) 100%);
}

.dark .welcome-card.card-analyze {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(30, 64, 175, 0.12) 100%);
}

/* 可视化卡片 - 绿色主题 */
.welcome-card.card-visualize {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(134, 239, 172, 0.08) 100%);
}

.dark .welcome-card.card-visualize {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(21, 128, 61, 0.12) 100%);
}

/* 结构优化卡片 - 紫色主题 */
.welcome-card.card-optimize {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.05) 0%, rgba(216, 180, 254, 0.08) 100%);
}

.dark .welcome-card.card-optimize {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.08) 0%, rgba(107, 33, 168, 0.12) 100%);
}

.welcome-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px hsl(var(--border));
  border-color: hsl(var(--ring));
}

/* Hover 状态增强 */
.welcome-card.card-analyze:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.12) 100%);
  border-color: rgba(59, 130, 246, 0.3);
}

.dark .welcome-card.card-analyze:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(30, 64, 175, 0.18) 100%);
  border-color: rgba(59, 130, 246, 0.4);
}

.welcome-card.card-visualize:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(134, 239, 172, 0.12) 100%);
  border-color: rgba(34, 197, 94, 0.3);
}

.dark .welcome-card.card-visualize:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.12) 0%, rgba(21, 128, 61, 0.18) 100%);
  border-color: rgba(34, 197, 94, 0.4);
}

.welcome-card.card-optimize:hover {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.08) 0%, rgba(216, 180, 254, 0.12) 100%);
  border-color: rgba(168, 85, 247, 0.3);
}

.dark .welcome-card.card-optimize:hover {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.12) 0%, rgba(107, 33, 168, 0.18) 100%);
  border-color: rgba(168, 85, 247, 0.4);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  background: hsl(var(--accent));
  border-radius: 12px;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.card-description {
  font-size: 0.9rem;
  color: hsl(var(--muted-foreground));
  line-height: 1.5;
}

/* 聊天气泡容器样式 */
.message-container {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 85%;
}

.message-container.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-container.assistant {
  align-self: flex-start;
  flex-direction: row;
}

.message-avatar {
  flex-shrink: 0;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.avatar.user {
  background: hsl(var(--primary));
}

.avatar.assistant {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-size: 16px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: -1px;
}

/* 深色主题下反转 AI 头像颜色 */
.dark .avatar.assistant {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

/* 用户头像样式 - 与 AI 头像保持一致 */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  flex-shrink: 0;
}

.user-avatar-fallback {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  /* 黄绿渐变色 - 深浅模式都清晰 */
  background: linear-gradient(135deg, #84cc16 0%, #22c55e 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.message-bubble {
  position: relative;
  border: 1px solid hsl(var(--border));
  transition: all 0.2s ease;
  height: auto;
  min-height: auto;
}

/* 重置 Card 组件的默认 padding 和 gap */
.message-bubble[data-slot="card"] {
  padding: 0 !important;
  gap: 0 !important;
}

.message-bubble.user {
  background: #f1f1f1;
  color: #2c2c2c;
  border-radius: 8px 8px 2px 8px;
  border: 1px solid #e0e0e0;
}

.message-bubble.assistant {
  background: var(--chat-bubble-bg, #f1f1f1); /* 默认灰色，与用户消息相同，可通过设置修改 */
  color: #2c2c2c;
  border-radius: 8px 8px 8px 2px;
  border: 1px solid #e0e0e0; /* 与用户消息相同的柔和边框 */
}

/* 深色主题下的气泡样式 */
.dark .message-bubble.user {
  background: #3a3a3a;
  color: #e5e5e5;
  border-color: #4a4a4a;
}

.dark .message-bubble.assistant {
  background: #3a3a3a; /* 与用户消息相同的深灰色 */
  color: #e5e5e5;
  border-color: #4a4a4a; /* 与用户消息相同的边框 */
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--border) / 0.3);
}

/* 消息包装器 */
.message-wrapper {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

/* AI 消息操作按钮 */
.message-actions {
  display: flex;
  gap: 4px;
  padding-left: 12px;
}

/* 用户消息操作按钮 - hover 时显示 */
.user-message-actions {
  padding-right: 12px;
  justify-content: flex-end;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-container.user:hover .user-message-actions {
  opacity: 1;
}

.message-actions .action-btn {
  height: 28px;
  width: 28px;
  padding: 0;
  color: hsl(var(--muted-foreground));
  transition: all 0.2s ease;
}

.message-actions .action-btn:hover {
  color: hsl(var(--foreground));
  background: hsl(var(--accent));
}

.message-actions .action-btn svg {
  width: 14px;
  height: 14px;
}

/* 用户消息文本样式 */
.user-message-text {
  line-height: 1.3;
}

.message-content {
  padding: 8px 12px !important;
  height: auto !important;
  min-height: auto !important;
}

/* 重置 CardContent 的默认样式 */
.message-bubble .message-content[data-slot="card-content"] {
  padding: 8px 12px !important;
  margin: 0 !important;
  height: auto !important;
  min-height: 0 !important;
  box-sizing: border-box !important;
}

.message-text {
  margin: 0 0 2px 0;
  padding: 0;
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;
}

/* 用户消息的纯文本样式 */
.message-bubble.user .message-text {
  line-height: 1.3;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  text-align: right;
  margin: 0;
  padding: 0;
  line-height: 1;
}

.message-bubble.user .message-time {
  color: #666;
}

.message-bubble.assistant .message-time {
  color: #666;
}

.dark .message-bubble.user .message-time,
.dark .message-bubble.assistant .message-time {
  color: #aaa;
}

.placeholder {
  margin: auto;
  color: hsl(var(--muted-foreground));
  text-align: center;
}

.chat-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px 16px;
  border-top: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  flex-shrink: 0;
  align-items: center;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 900px;
}

.file-upload-label {
  cursor: pointer;
  margin: 0;
}

.pending-file {
  width: 100%;
  max-width: 900px;
  padding: 8px 12px;
  background-color: hsl(var(--accent));
  border-radius: 8px;
  border: 1px solid hsl(var(--border));
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: hsl(var(--foreground));
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.file-size {
  color: hsl(var(--muted-foreground));
  font-size: 12px;
  flex-shrink: 0;
}

.file-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  flex-shrink: 0;
}

.file-status.uploaded {
  color: #22c55e;
  background-color: rgba(34, 197, 94, 0.1);
}

.file-status.uploading {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.remove-file-btn {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}

.remove-file-btn:hover {
  color: hsl(var(--destructive));
}

.chat-actions {
  display: flex;
  gap: 8px;
  width: 100%;
  max-width: 900px;
}

/* 加载骨架屏样式 */
.loading-skeleton {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.skeleton-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.skeleton-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  flex-shrink: 0;
}

.skeleton-bubble {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 70%;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-line.short {
  width: 60%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 为加载过程添加平滑过渡 */
.chat-messages {
  transition: opacity 0.2s ease-in-out;
}

/* 推理步骤样式 */
.reasoning-steps-container {
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  background: hsl(var(--accent) / 0.3);
  overflow: hidden;
}

.reasoning-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.reasoning-trigger:hover {
  background: hsl(var(--accent));
}

.reasoning-icon {
  font-size: 14px;
}

.reasoning-text {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.reasoning-arrow {
  transition: transform 0.2s;
}

.reasoning-steps-container[data-state="open"] .reasoning-arrow {
  transform: rotate(180deg);
}

.reasoning-content {
  padding: 12px;
  border-top: 1px solid hsl(var(--border));
  background: hsl(var(--background));
}

.reasoning-step {
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid hsl(var(--primary));
  background: hsl(var(--accent) / 0.2);
  border-radius: 4px;
}

.reasoning-step:last-child {
  margin-bottom: 0;
}

.reasoning-step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reasoning-step-title {
  font-weight: 600;
  font-size: 13px;
  color: hsl(var(--foreground));
}

.reasoning-confidence {
  font-size: 11px;
  color: hsl(var(--muted-foreground));
  padding: 2px 6px;
  background: hsl(var(--accent));
  border-radius: 4px;
}

.reasoning-step-reasoning,
.reasoning-step-action,
.reasoning-step-result,
.reasoning-step-content {
  font-size: 12px;
  line-height: 1.5;
  color: hsl(var(--foreground));
  margin-top: 6px;
}

.reasoning-step-reasoning strong,
.reasoning-step-action strong,
.reasoning-step-result strong {
  color: hsl(var(--primary));
  margin-right: 4px;
}

.reasoning-step-content {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid hsl(var(--border));
}

:deep(.streaming-cursor) {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: currentColor;
  animation: blink 1s step-end infinite;
  vertical-align: text-bottom;
  margin-left: 2px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 工具调用折叠样式 */
.tool-calls-collapsible {
  width: fit-content;
  max-width: 100%;
}

.tool-calls-trigger {
  cursor: pointer;
  user-select: none;
  display: block;
  width: fit-content;
  max-width: 100%;
}

.tool-calls-trigger:hover {
  opacity: 0.9;
}

.tool-calls-chevron {
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.tool-calls-collapsible[data-state="open"] .tool-calls-chevron {
  transform: rotate(180deg);
}

.tool-calls-content {
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
