from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
import sys
from pathlib import Path

# Add model path to sys.path
model_path = Path(__file__).parent.parent
if str(model_path) not in sys.path:
    sys.path.insert(0, str(model_path))

from store.storage import sesssion_database
from src.api.auth import get_current_user

router = APIRouter(prefix="/custom/sessions", tags=["custom_sessions"])


class SessionInfo(BaseModel):
    session_id: str
    session_name: str
    session_type: str
    session_state: Dict[str, Any]
    created_at: str
    updated_at: str
    agent_id: Optional[str] = None  # 添加 agent_id 字段


class SessionResponse(BaseModel):
    data: List[SessionInfo]
    meta: Dict[str, Any]


@router.get("", response_model=SessionResponse)
async def list_sessions(
    type: str = Query(..., description="Session type (e.g., 'agent')"),
    db_id: str = Query(..., description="Database ID"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    limit: int = Query(100, description="Number of results to return"),
    page: int = Query(1, description="Page number"),
    current_user: dict = Depends(get_current_user),
):
    """List sessions from the agno_sessions table.

    Args:
        type: Filter by session type
        db_id: Database ID (for compatibility, not used directly)
        sort_by: Field to sort by
        sort_order: Sort order (asc or desc)
        limit: Maximum number of results
        page: Page number for pagination
        current_user: Authenticated user

    Returns:
        SessionResponse with data list and metadata
    """
    try:
        user_id = current_user.get("user_id")
        
        # Query the database using get_sessions
        # Use deserialize=False to get raw dicts and total count
        # 将排序参数传递给数据库层面，避免API层面重复排序
        result = sesssion_database.get_sessions(
            session_type=type,
            user_id=user_id,
            limit=limit,
            page=page,
            sort_by=sort_by,
            sort_order=sort_order,
            deserialize=False
        )

        # Handle result based on updated agno signature (List[Dict], int)
        if isinstance(result, tuple) and len(result) == 2:
            sessions_data, total_count = result
        else:
             # Fallback/Safety in case return type varies or is empty
             sessions_data = []
             total_count = 0

        # 注意：不需要在API层面重新排序，因为数据库层面已经通过 apply_sorting()
        # 进行了排序和分页。重新排序会破坏全局排序的正确性。
        #
        # 之前的排序代码已移除：
        # - 数据库返回的是"全局排序后的分页数据"
        # - API层面重新排序会打乱这个全局顺序
        # - 特别是在数据量超过分页限制时会导致新session不在正确位置

        # Transform database results to API format
        sessions = []
        for session in sessions_data:
            # Convert timestamp to ISO format (handle both seconds and milliseconds)
            created_at_ts = session.get("created_at", 0)
            updated_at_ts = session.get("updated_at", session.get("created_at", 0))

            # Convert milliseconds to seconds if needed
            if created_at_ts and created_at_ts > 10000000000:
                created_at_ts = created_at_ts / 1000
            if updated_at_ts and updated_at_ts > 10000000000:
                updated_at_ts = updated_at_ts / 1000

            created_at = datetime.fromtimestamp(created_at_ts).isoformat() if created_at_ts else None
            updated_at = datetime.fromtimestamp(updated_at_ts).isoformat() if updated_at_ts else None

            # Extract session name from metadata or use default
            # session_data is a dict that may contain leader_name, session_name, etc.
            session_data_dict = session.get("session_data", {}) if isinstance(session.get("session_data"), dict) else {}
            metadata = session.get("metadata") or {}
            
            session_name = (
                session_data_dict.get("session_name") or
                metadata.get("session_name") or
                session_data_dict.get("leader_name")
            )

            # Fallback: Try to get the first user message from runs
            if not session_name:
                runs = session.get("runs", [])
                if runs and isinstance(runs, list):
                    # Sort runs by created_at to find the first one
                    sorted_runs = sorted(runs, key=lambda x: x.get("created_at", 0))
                    for run in sorted_runs:
                        messages = run.get("messages", [])
                        if messages and isinstance(messages, list):
                            # Find first user message
                            for msg in messages:
                                if msg.get("role") == "user" and msg.get("content"):
                                    content = msg.get("content")
                                    # Truncate if too long
                                    session_name = content[:47] + "..." if len(content) > 50 else content
                                    break
                        if session_name:
                            break
            
            # Final fallback
            if not session_name:
                 session_name = f"Session {session.get('session_id', '')[:8]}"

            # 获取 agent_id（如果有的话）
            agent_id = session.get("agent_id")

            sessions.append(
                SessionInfo(
                    session_id=session.get("session_id", ""),
                    session_name=session_name,
                    session_type=session.get("session_type", ""),
                    session_state=session_data_dict,
                    created_at=created_at,
                    updated_at=updated_at,
                    agent_id=agent_id,  # 添加 agent_id
                )
            )

        return SessionResponse(
            data=sessions,
            meta={
                "page": page,
                "limit": limit,
                "total_pages": (total_count + limit - 1) // limit if limit > 0 else 1,
                "total_count": total_count,
                "search_time_ms": 0,
            },
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}")
async def get_session(session_id: str, type: str = Query(...), db_id: str = Query(...)):
    """Get a specific session by ID with chat_history.

    !!! THIS IS OUR CUSTOM ENDPOINT !!!
    """
    print(f"[CUSTOM ENDPOINT] get_session called: session_id={session_id}, type={type}, db_id={db_id}")

    """Get a specific session by ID with chat_history.

    Args:
        session_id: The session ID
        type: Session type
        db_id: Database ID

    Returns:
        SessionInfo with chat_history including run_id for each message
    """
    try:
        session_data = sesssion_database.get_session(session_id=session_id, session_type=type)

        # Fallback: if get_session returns None, try to query directly from database
        if not session_data:
            print(f"[WARNING] get_session returned None for {session_id}, trying fallback...")

            # Query sessions list to find the session
            result = sesssion_database.get_sessions(
                session_type=type,
                limit=1000,
                page=1,
                deserialize=False
            )

            if isinstance(result, tuple):
                sessions_list, _ = result
                # Find the session in the list
                for session_dict in sessions_list:
                    if session_dict.get("session_id") == session_id:
                        print(f"[FALLBACK] Found session {session_id[:8]}... in list")

                        # Create a simple mock object with the necessary attributes
                        class MockSession:
                            def __init__(self, data):
                                self.session_id = data.get("session_id")
                                self.session_type = data.get("session_type")
                                self.agent_id = data.get("agent_id")
                                self.user_id = data.get("user_id")
                                self.created_at = data.get("created_at")
                                self.updated_at = data.get("updated_at")
                                self.session_data = data.get("session_data")
                                self.metadata = data.get("metadata")
                                self.runs = data.get("runs")

                        session_data = MockSession(session_dict)
                        break

            if not session_data:
                raise HTTPException(status_code=404, detail="Session not found")

        # Convert timestamp to ISO format (handle both seconds and milliseconds)
        created_at_ts = session_data.created_at
        updated_at_ts = session_data.updated_at

        # Convert milliseconds to seconds if needed
        if created_at_ts and created_at_ts > 10000000000:
            created_at_ts = created_at_ts / 1000
        if updated_at_ts and updated_at_ts > 10000000000:
            updated_at_ts = updated_at_ts / 1000

        created_at = datetime.fromtimestamp(created_at_ts).isoformat() if created_at_ts else None
        updated_at = datetime.fromtimestamp(updated_at_ts).isoformat() if updated_at_ts else None

        # Extract session name from various possible locations
        session_data_dict = session_data.session_data if isinstance(session_data.session_data, dict) else {}
        metadata = session_data.metadata or {}
        session_name = (
            session_data_dict.get("session_name") or
            (metadata.get("session_name") if metadata else None) or
            session_data_dict.get("leader_name") or
            f"Session {session_data.session_id[:8]}"
        )

        # Build chat_history with run_id for each message
        chat_history = []
        if hasattr(session_data, 'runs') and session_data.runs:
            print(f"[DEBUG] Session has {len(session_data.runs)} runs")
            for run in session_data.runs:
                run_id = run.run_id
                print(f"[DEBUG] Processing run {run_id} with {len(run.messages) if run.messages else 0} messages")
                if run.messages:
                    for msg in run.messages:
                        # Skip messages that are from history (duplicates)
                        if msg.from_history:
                            continue

                        msg_dict = msg.to_dict()
                        # Add run_id to each message so frontend can group them
                        msg_dict["run_id"] = run_id
                        chat_history.append(msg_dict)
                        print(f"[DEBUG] Added message: role={msg.role}, run_id={run_id}, has_run_id={msg_dict.get('run_id') is not None}")

        return {
            "session_id": session_data.session_id,
            "session_name": session_name,
            "session_type": "agent",  # AgentSession doesn't have session_type field
            "session_state": session_data_dict,
            "created_at": created_at,
            "updated_at": updated_at,
            "chat_history": chat_history,
            "agent_id": session_data.agent_id,
        }
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


class RenameRequest(BaseModel):
    session_name: str


@router.post("/{session_id}/rename")
async def rename_session(
    session_id: str,
    request: RenameRequest,
    type: str = Query(...),
    db_id: str = Query(...),
):
    """Rename a session.

    Args:
        session_id: The session ID
        request: Rename request with new session_name
        type: Session type
        db_id: Database ID

    Returns:
        Success message
    """
    try:
        # Use rename_session method from PostgresDb
        success = sesssion_database.rename_session(
            session_id=session_id,
            new_name=request.session_name,
        )

        if not success:
            raise HTTPException(status_code=404, detail="Session not found or update failed")

        return {"success": True, "message": "Session renamed successfully"}
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{session_id}")
async def delete_session(
    session_id: str,
    type: str = Query(..., description="Session type"),
    db_id: str = Query(..., description="Database ID"),
    current_user: dict = Depends(get_current_user),
):
    """Delete a session.

    Args:
        session_id: The session ID
        type: Session type
        db_id: Database ID
        current_user: Authenticated user

    Returns:
        Success message
    """
    try:
        # Check if delete_session exists on the database object
        if hasattr(sesssion_database, "delete_session"):
            sesssion_database.delete_session(session_id=session_id)
        else:
            # Fallback for when delete_session is not available
            # Check for generic execute or run_sql method, or use engine directly
            if hasattr(sesssion_database, "engine"):
                from sqlalchemy import text
                with sesssion_database.engine.begin() as conn:
                    # session_table is usually passed to PostgresDb constructor. 
                    # We can access it via sesssion_database.session_table or hardcode if we knew.
                    # storage.py initializes it with session_table="agno_sessions"
                    table_name = getattr(sesssion_database, "session_table", "agno_sessions")
                    conn.execute(
                        text(f"DELETE FROM {table_name} WHERE session_id = :session_id"),
                        {"session_id": session_id}
                    )
            else:
                raise HTTPException(status_code=500, detail="Database adapter does not support session deletion")

        return {"success": True, "message": "Session deleted successfully"}

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

