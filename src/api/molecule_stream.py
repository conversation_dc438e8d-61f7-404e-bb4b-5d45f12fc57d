"""
分子生成 SSE 流式进度 API

通过 Server-Sent Events (SSE) 实时推送分子生成任务进度
"""
import os
import json
import time
import asyncio
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from minio import Minio
from minio.error import S3Error

router = APIRouter(prefix="/molecule-stream", tags=["molecule_stream"])


def _get_minio_client() -> Minio:
    """获取 MinIO 客户端"""
    endpoint = os.getenv("MINIO_ENDPOINT", "localhost:9000")
    access_key = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    secret_key = os.getenv("MINIO_SECRET_KEY", "minioadmin123")
    return Minio(endpoint, access_key, secret_key, secure=False)


def _get_bucket_name() -> str:
    """获取存储桶名称"""
    return os.getenv("MOLECULE_GEN_BUCKET", "molecule-gen")


async def _stream_minio_logs(
    run_id: str,
    user_id: Optional[str],
    session_id: Optional[str],
    initial_only: bool = False
):
    """
    从 MinIO 流式读取日志并通过 SSE 推送

    Args:
        run_id: 运行 ID
        user_id: 用户 ID (已弃用，保留兼容性)
        session_id: 会话 ID (已弃用，保留兼容性)
        initial_only: 是否仅发送初始状态（不持续监听）
    """
    import sys
    print(f"[SSE Generator] Started for run_id: {run_id}", file=sys.stderr)

    client = _get_minio_client()
    bucket = _get_bucket_name()

    # 尝试两种路径格式：
    # 1. 新格式: runs/{run_id}/
    # 2. 旧格式: users/{user_id}/sessions/{session_id}/runs/{run_id}/
    new_prefix = f"runs/{run_id}/"
    old_prefix = f"users/{user_id or 'anonymous'}/sessions/{session_id or 'default'}/runs/{run_id}/"

    # 检测哪个路径存在
    prefix = new_prefix  # 默认使用新格式
    try:
        # 尝试读取新格式的 status.json
        test_path = f"{new_prefix}status.json"
        resp = client.get_object(bucket, test_path)
        resp.close()
        resp.release_conn()
        print(f"[SSE Generator] Using new path format: {new_prefix}", file=sys.stderr)
    except S3Error:
        # 新格式不存在，尝试旧格式
        try:
            test_path = f"{old_prefix}status.json"
            resp = client.get_object(bucket, test_path)
            resp.close()
            resp.release_conn()
            prefix = old_prefix
            print(f"[SSE Generator] Using old path format: {old_prefix}", file=sys.stderr)
        except S3Error:
            # 两种格式都不存在，使用新格式（任务可能还没开始）
            print(f"[SSE Generator] No existing files found, using new path: {new_prefix}", file=sys.stderr)
            prefix = new_prefix

    print(f"[SSE Generator] Starting to stream from prefix: {prefix}", file=sys.stderr)

    # 已发送的消息索引（用于去重）
    sent_message_count = 0
    last_status = None

    def send_sse(data: dict, event_type: str = "message"):
        """发送 SSE 格式数据"""
        line = f"event: {event_type}\n"
        line += f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
        return line.encode('utf-8')

    try:
        # 检查存储桶是否存在
        print(f"[SSE Generator] Checking bucket existence...", file=sys.stderr)
        if not client.bucket_exists(bucket):
            print(f"[SSE Generator] Bucket '{bucket}' not found!", file=sys.stderr)
            yield send_sse({
                "type": "error",
                "message": f"存储桶 '{bucket}' 不存在"
            }, "error")
            return

        print(f"[SSE Generator] Bucket exists, preparing to send connected event...", file=sys.stderr)

        # ✅ 立即发送连接成功消息（避免 ERR_INCOMPLETE_CHUNKED_ENCODING）
        yield send_sse({
            "type": "connected",
            "run_id": run_id,
            "message": "SSE connection established",
            "timestamp": time.time()
        }, "connected")

        print(f"[SSE Generator] Connected event sent, starting polling...", file=sys.stderr)

        # 持续轮询 MinIO (模拟实时流)
        max_iterations = 1200 if not initial_only else 1  # 最多轮询 10 分钟 (600 * 0.5 = 300秒)
        poll_interval = 0.5  # 每 0.5 秒轮询一次（降低延迟）
        heartbeat_interval = 30  # 每 30 秒发送一次心跳
        last_heartbeat = time.time()

        for iteration in range(max_iterations):
            # 发送心跳（保持连接活跃）
            if time.time() - last_heartbeat > heartbeat_interval:
                yield send_sse({
                    "type": "heartbeat",
                    "timestamp": time.time()
                }, "heartbeat")
                last_heartbeat = time.time()
                print(f"[SSE Generator] Heartbeat sent at iteration {iteration}", file=sys.stderr)
            # 尝试读取 status.json
            status_path = f"{prefix}status.json"
            current_status = None

            try:
                resp = client.get_object(bucket, status_path)
                status_data = json.loads(resp.read().decode('utf-8'))
                resp.close()
                resp.release_conn()

                current_status = status_data.get("status", "running")

                # 发送状态更新（仅当状态变化时）
                if current_status != last_status:
                    yield send_sse({
                        "type": "status",
                        "status": current_status,
                        "run_id": run_id,
                        "timestamp": status_data.get("timestamp"),
                    }, "status")
                    last_status = current_status

            except S3Error as e:
                if e.code == "NoSuchKey":
                    # status.json 还不存在，任务可能刚开始
                    pass
                else:
                    raise

            # 尝试读取 logs.jsonl
            logs_path = f"{prefix}logs.jsonl"
            new_messages = []

            try:
                resp = client.get_object(bucket, logs_path)
                logs_content = resp.read().decode('utf-8')
                resp.close()
                resp.release_conn()

                # 解析所有日志行
                all_messages = []
                for line in logs_content.splitlines():
                    if line.strip():
                        try:
                            msg = json.loads(line)
                            all_messages.append(msg)
                        except json.JSONDecodeError:
                            continue

                # 只发送新消息
                if len(all_messages) > sent_message_count:
                    new_messages = all_messages[sent_message_count:]
                    sent_message_count = len(all_messages)

                    # 发送新消息
                    for msg in new_messages:
                        # 如果没有 type 字段但有 level 字段，进行映射（与 polling endpoint 保持一致）
                        if 'type' not in msg and 'level' in msg:
                            level_to_type = {
                                'info': 'log',
                                'warning': 'log',
                                'error': 'log',
                                'success': 'log',
                            }
                            msg['type'] = level_to_type.get(msg['level'], 'log')
                        yield send_sse(msg, "log")

            except S3Error as e:
                if e.code == "NoSuchKey":
                    # logs.jsonl 还不存在
                    pass
                else:
                    raise

            # 检查 complete_log.json 是否存在（如果存在，说明任务已完成）
            complete_log_path = f"{prefix}complete_log.json"
            complete_log_exists = False
            try:
                resp = client.get_object(bucket, complete_log_path)
                resp.close()
                resp.release_conn()
                complete_log_exists = True
            except S3Error as e:
                if e.code == "NoSuchKey":
                    pass
                else:
                    raise

            # 检查是否已完成（状态为 completed 或 complete_log 存在）
            if current_status == "completed" or complete_log_exists:
                # 如果状态不是 completed 但 complete_log 存在，先更新状态
                if current_status != "completed" and complete_log_exists:
                    yield send_sse({
                        "type": "status",
                        "status": "completed",
                        "run_id": run_id,
                    }, "status")

                # 尝试读取完整日志
                try:
                    resp = client.get_object(bucket, complete_log_path)
                    complete_data = json.loads(resp.read().decode('utf-8'))
                    resp.close()
                    resp.release_conn()

                    # 映射 final_results 到 results（前端期望的字段名）
                    results_data = complete_data.get("final_results", complete_data.get("results", {}))

                    # 发送完成事件和完整结果
                    yield send_sse({
                        "type": "complete",
                        "run_id": run_id,
                        "summary": complete_data.get("metadata", {}),
                        "results": results_data,
                    }, "complete")
                    return  # 结束流

                except S3Error:
                    # 即使没有 complete_log，也发送完成通知
                    yield send_sse({
                        "type": "complete",
                        "run_id": run_id,
                        "message": "任务已完成"
                    }, "complete")
                    return

            elif current_status == "error":
                yield send_sse({
                    "type": "serverError",
                    "run_id": run_id,
                    "message": "任务执行出错"
                }, "serverError")
                return

            # 如果只是读取初始状态，退出循环
            if initial_only:
                break

            # 等待下一次轮询
            await asyncio.sleep(poll_interval)

        # 如果达到最大迭代次数，发送超时通知
        if not initial_only:
            yield send_sse({
                "type": "timeout",
                "message": "流式读取超时，请使用轮询方式继续查询"
            }, "timeout")

    except Exception as e:
        yield send_sse({
            "type": "serverError",
            "message": f"读取 MinIO 失败: {str(e)}"
        }, "serverError")


@router.get("/{run_id}")
async def stream_molecule_progress(
    run_id: str,
    user_id: Optional[str] = Query(None, description="用户 ID"),
    session_id: Optional[str] = Query(None, description="会话 ID"),
    initial_only: bool = Query(False, description="仅发送初始状态，不持续监听"),
):
    """
    SSE 流式推送分子生成进度

    Args:
        run_id: 运行 ID (从任务提交时返回)
        user_id: 用户 ID (可选，用于构造 MinIO 路径)
        session_id: 会话 ID (可选，用于构造 MinIO 路径)
        initial_only: 是否仅发送初始状态（用于获取当前快照）

    Returns:
        StreamingResponse: SSE 流

    SSE 事件类型:
    - status: 状态更新 (running, completed, error)
    - log: 日志消息
    - complete: 任务完成
    - error: 错误消息
    - timeout: 轮询超时
    """
    # ✅ 添加连接日志
    import sys
    print(f"[SSE] ========== NEW SSE CONNECTION ==========", file=sys.stderr)
    print(f"[SSE] run_id: {run_id}", file=sys.stderr)
    print(f"[SSE] user_id: {user_id}", file=sys.stderr)
    print(f"[SSE] session_id: {session_id}", file=sys.stderr)
    print(f"[SSE] initial_only: {initial_only}", file=sys.stderr)
    print(f"[SSE] =========================================", file=sys.stderr)

    return StreamingResponse(
        _stream_minio_logs(run_id, user_id, session_id, initial_only),
        media_type="text/event-stream; charset=utf-8",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用 Nginx 缓冲
        }
    )


@router.get("/{run_id}/files")
async def list_run_files(
    run_id: str,
    user_id: Optional[str] = Query(None),
    session_id: Optional[str] = Query(None),
):
    """
    列出指定运行的所有 MinIO 文件

    用于调试和验证文件是否正确写入
    """
    client = _get_minio_client()
    bucket = _get_bucket_name()

    # 构造路径前缀
    prefix = f"runs/{run_id}/"

    try:
        objects = client.list_objects(bucket, prefix=prefix, recursive=True)

        files = []
        for obj in objects:
            if not obj.is_dir:
                files.append({
                    "name": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified.isoformat() if obj.last_modified else None,
                    "etag": obj.etag,
                })

        return {
            "run_id": run_id,
            "bucket": bucket,
            "prefix": prefix,
            "files": files,
            "count": len(files),
        }

    except S3Error as e:
        raise HTTPException(
            status_code=500,
            detail=f"MinIO error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error: {str(e)}"
        )
