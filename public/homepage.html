<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EAGENT - 电镀添加剂智能研发平台</title>
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        :root {
            --bg-primary: #0E0E0E;
            --bg-secondary: #1A1A1A;
            --bg-tertiary: #252525;
            --text-primary: #FFFFFF;
            --text-secondary: #A0A0A0;
            --text-muted: #6B6B6B;
            --accent-blue: #3B82F6;
            --accent-cyan: #06B6D4;
            --border-color: rgba(255, 255, 255, 0.08);
            --glow-blue: rgba(59, 130, 246, 0.3);
            --glow-cyan: rgba(6, 182, 212, 0.3);
        }

        /* Light Theme */
        [data-theme="light"] {
            --bg-primary: #FFFFFF;
            --bg-secondary: #F5F5F7;
            --bg-tertiary: #E8E8EA;
            --text-primary: #1D1D1F;
            --text-secondary: #6E6E73;
            --text-muted: #A1A1A6;
            --accent-blue: #3B82F6;
            --accent-cyan: #06B6D4;
            --border-color: rgba(0, 0, 0, 0.1);
            --glow-blue: rgba(59, 130, 246, 0.15);
            --glow-cyan: rgba(6, 182, 212, 0.15);
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: relative;
            width: 44px;
            height: 44px;
            border: none;
            background: var(--bg-secondary);
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .theme-toggle:hover {
            background: var(--bg-tertiary);
            transform: scale(1.05);
        }

        .theme-toggle .icon-sun,
        .theme-toggle .icon-moon {
            position: absolute;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .theme-toggle .icon-moon {
            opacity: 1;
            transform: rotate(0deg);
        }

        .theme-toggle .icon-sun {
            opacity: 0;
            transform: rotate(90deg);
        }

        [data-theme="light"] .theme-toggle .icon-moon {
            opacity: 0;
            transform: rotate(-90deg);
        }

        [data-theme="light"] .theme-toggle .icon-sun {
            opacity: 1;
            transform: rotate(0deg);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Hero Section with Background Image */
        .hero-section {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* Background image layer */
        .hero-bg {
            position: absolute;
            inset: 0;
            background-image: url('/apps/eagent/air_compressed.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.75;
            z-index: 0;
        }

        /* Gradient overlay */
        .hero-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(180deg,
                rgba(14, 14, 14, 0.5) 0%,
                rgba(14, 14, 14, 0.3) 50%,
                rgba(14, 14, 14, 0.7) 100%
            );
            z-index: 1;
        }

        [data-theme="light"] .hero-overlay {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(2px) saturate(105%);
            -webkit-backdrop-filter: blur(2px) saturate(105%);
        }

        .container {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 40px;
            background: rgba(14, 14, 14, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s;
        }

        [data-theme="light"] header {
            background: rgba(255, 255, 255, 0.8);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
            font-size: 20px;
            letter-spacing: -0.5px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        nav {
            display: flex;
            gap: 32px;
            align-items: center;
        }

        nav a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s;
        }

        nav a:hover {
            color: var(--text-primary);
        }

        nav > * {
            margin-left: 16px;
        }

        nav > *:first-child {
            margin-left: 0;
        }

        .btn {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 600;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--text-primary);
            color: var(--bg-primary);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-tertiary);
            border-color: var(--text-secondary);
        }

        /* Hero Content */
        .hero-content {
            text-align: center;
            padding: 120px 0 80px;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 20px;
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 100px;
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 32px;
            backdrop-filter: blur(10px);
        }

        .hero-badge-dot {
            width: 6px;
            height: 6px;
            background: #22C55E;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.4; }
        }

        .hero h1 {
            font-size: 72px;
            font-weight: 800;
            line-height: 1.1;
            letter-spacing: -2.5px;
            margin-bottom: 24px;
            background: linear-gradient(180deg, var(--text-primary) 0%, rgba(255, 255, 255, 0.7) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 700px;
            margin: 0 auto 40px;
            font-weight: 400;
            line-height: 1.7;
        }

        .hero-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 80px;
        }

        .hero-actions .btn {
            padding: 16px 32px;
            font-size: 16px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            background: transparent;
            border: none;
            border-radius: 16px;
            overflow: visible;
            backdrop-filter: blur(10px);
        }

        [data-theme="light"] .stats-grid {
            gap: 10px;
        }

        .stat-item {
            background: var(--bg-secondary);
            padding: 40px 32px;
            text-align: center;
            transition: transform 0.1s ease-out, opacity 0.1s ease-out, border-radius 0.1s ease-out, background 0.3s;
            backdrop-filter: blur(10px);
            border-radius: 12px;
            will-change: transform, opacity, border-radius;
        }

        .stat-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-4px);
        }

        [data-theme="light"] .stat-item {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        [data-theme="light"] .stat-item:hover {
            background: rgba(255, 255, 255, 0.85);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .stat-item {
            background: rgba(26, 26, 26, 0.8);
        }

        [data-theme="dark"] .stat-item:hover {
            background: rgba(37, 37, 37, 0.9);
        }

        [data-theme="light"] .stat-item {
            background: rgba(255, 255, 255, 0.65);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        [data-theme="light"] .stat-item:hover {
            background: rgba(255, 255, 255, 0.75);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .stat-number {
            font-size: 52px;
            font-weight: 800;
            letter-spacing: -2px;
            margin-bottom: 8px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Invitation Section */
        .invitation-section {
            padding: 120px 0;
            background: var(--bg-primary);
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-label {
            font-size: 13px;
            font-weight: 600;
            color: var(--accent-blue);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 48px;
            font-weight: 700;
            letter-spacing: -1.5px;
            line-height: 1.2;
            margin-bottom: 20px;
        }

        .section-description {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Invitation Cards */
        .invitation-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 80px;
        }

        .invitation-card {
            position: relative;
            border-radius: 24px;
            overflow: hidden;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            flex: 1;
            height: 100%;
            min-height: 600px;
            display: flex;
            flex-direction: column;
        }

        .invitation-card:hover {
            flex: 1.5;
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(94, 106, 210, 0.3);
            border-color: rgba(94, 106, 210, 0.5);
            z-index: 10;
        }

        .invitation-grid:hover .invitation-card:not(:hover) {
            flex: 0.7;
            opacity: 0.6;
        }

        .invitation-grid:hover .invitation-card:not(:hover) .invitation-image {
            filter: blur(2px);
        }

        .invitation-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            flex-shrink: 0;
        }

        .invitation-content {
            padding: 32px;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .invitation-grid:hover .invitation-card:not(:hover) {
            flex: 0.7;
            opacity: 0.6;
        }

        .invitation-grid:hover .invitation-card:not(:hover) .invitation-image {
            filter: blur(2px);
        }

        .invitation-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .invitation-content {
            padding: 32px;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .invitation-content h3 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .invitation-content p {
            font-size: 15px;
            color: var(--text-secondary);
            line-height: 1.7;
        }

        /* Features Section */
        .features {
            padding: 120px 0;
            background: var(--bg-primary);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .feature-card-image {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            overflow: hidden;
            transition: transform 0.1s ease-out, all 0.3s;
            position: relative;
            display: flex;
            flex-direction: column;
            will-change: transform;
        }

        .feature-card-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--glow-blue), var(--glow-cyan));
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 0;
            pointer-events: none;
        }

        .feature-card-image:hover::before {
            opacity: 0.08;
        }

        .feature-card-image:hover {
            border-color: rgba(94, 106, 210, 0.4);
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(94, 106, 210, 0.15);
        }

        .model-image {
            width: 100%;
            height: 280px;
            object-fit: cover;
            display: block;
        }

        .model-content {
            padding: 32px;
            position: relative;
            z-index: 1;
        }

        .model-content h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            letter-spacing: -0.3px;
        }

        .model-content p {
            font-size: 15px;
            color: var(--text-secondary);
            line-height: 1.7;
        }

        /* Testimonials Section */
        .testimonials {
            display: none;
        }

        /*
        .testimonials {
            padding: 120px 0;
            background: var(--bg-primary);
            overflow: hidden;
        }

        .testimonials-scroll-track {
            margin-top: 60px;
            margin-bottom: 24px;
            overflow: hidden;
            position: relative;
        }

        .testimonials-scroll-track::before,
        .testimonials-scroll-track::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 120px;
            z-index: 2;
            pointer-events: none;
        }

        .testimonials-scroll-track::before {
            left: 0;
            background: linear-gradient(to right, var(--bg-primary) 0%, transparent 100%);
        }

        .testimonials-scroll-track::after {
            right: 0;
            background: linear-gradient(to left, var(--bg-primary) 0%, transparent 100%);
        }

        .testimonials-scroll {
            display: flex;
            gap: 24px;
            width: max-content;
            animation: scroll 30s linear infinite;
        }

        .testimonials-scroll-left {
            animation-direction: normal;
        }

        .testimonials-scroll-right {
            animation-direction: reverse;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        .testimonials-scroll:hover {
            animation-play-state: paused;
        }

        .testimonial-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 40px 32px;
            transition: all 0.3s;
            position: relative;
            width: 380px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            min-height: 280px;
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 32px;
            font-size: 80px;
            font-weight: 700;
            color: var(--accent-blue);
            opacity: 0.15;
            line-height: 1;
            font-family: Georgia, serif;
        }

        .testimonial-card:hover {
            border-color: rgba(94, 106, 210, 0.3);
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(94, 106, 210, 0.12);
        }

        .testimonial-content {
            position: relative;
            z-index: 1;
            margin-bottom: 24px;
        }

        .testimonial-content p {
            font-size: 16px;
            line-height: 1.7;
            color: var(--text-primary);
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
            z-index: 1;
            margin-top: auto;
        }

        .author-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 18px;
            color: white;
            overflow: hidden;
            flex-shrink: 0;
        }

        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center top;
        }

        .author-info {
            flex: 1;
        }

        .author-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
            color: var(--text-primary);
        }

        .author-role {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .author-organization {
            font-size: 13px;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .author-organization::before {
            content: '';
            width: 4px;
            height: 4px;
            background: var(--text-muted);
            border-radius: 50%;
        }
        */

        /* Electric Current Effect */
        .electric-current-container {
            position: relative;
            width: 100%;
            height: 100px;
            overflow: hidden;
            background: transparent;
        }

        .electric-current-svg {
            width: 100%;
            height: 100%;
            display: block;
        }

        .lightning-path {
            stroke-dasharray: 2000;
            stroke-dashoffset: 2000;
        }

        .lightning-path-2 {
            stroke-dasharray: 2000;
            stroke-dashoffset: 2000;
        }

        @keyframes lightningStrike {
            0% {
                stroke-dashoffset: 2000;
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            30% {
                stroke-dashoffset: 0;
                opacity: 1;
            }
            60% {
                opacity: 0.8;
            }
            100% {
                stroke-dashoffset: -2000;
                opacity: 0;
            }
        }

        /* Sparks */
        .electric-sparks {
            position: absolute;
            inset: 0;
            pointer-events: none;
        }

        .spark {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #22C55E;
            border-radius: 50%;
            box-shadow: 0 0 10px #22C55E, 0 0 20px #22C55E, 0 0 30px #22C55E;
            opacity: 0;
        }

        .spark-1 {
            left: 20%;
            top: 40%;
        }

        .spark-2 {
            left: 40%;
            top: 30%;
        }

        .spark-3 {
            left: 60%;
            top: 50%;
        }

        .spark-4 {
            left: 80%;
            top: 35%;
        }

        .spark-5 {
            left: 50%;
            top: 45%;
        }

        @keyframes sparkBurst {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            10% {
                opacity: 1;
                transform: scale(2);
            }
            50% {
                opacity: 1;
                transform: scale(1.5);
            }
            100% {
                opacity: 0;
                transform: scale(0.5) translateY(-30px);
            }
        }

        /* Hover to replay animation */
        .electric-current-container:hover .lightning-path,
        .electric-current-container:hover .lightning-path-2 {
            animation: none;
        }

        /* Animation when triggered */
        .electric-current-container.active .lightning-path {
            animation: lightningStrike 2s ease-out forwards;
        }

        .electric-current-container.active .lightning-path-2 {
            animation: lightningStrike 2.3s ease-out forwards;
        }

        .electric-current-container.active .spark-1 {
            animation: sparkBurst 2s ease-out 0.3s forwards;
        }

        .electric-current-container.active .spark-2 {
            animation: sparkBurst 2s ease-out 0.6s forwards;
        }

        .electric-current-container.active .spark-3 {
            animation: sparkBurst 2s ease-out 0.9s forwards;
        }

        .electric-current-container.active .spark-4 {
            animation: sparkBurst 2s ease-out 1.2s forwards;
        }

        .electric-current-container.active .spark-5 {
            animation: sparkBurst 2s ease-out 1.5s forwards;
        }

        /* Workflow Section */
        .workflow {
            padding: 120px 0;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .workflow::before {
            content: '';
            position: absolute;
            inset: 0;
            background-image: url('/apps/eagent/image.png');
            background-size: cover;
            background-position: center;
            opacity: 0.15;
            filter: blur(8px);
            z-index: 0;
        }

        .workflow > .container {
            position: relative;
            z-index: 1;
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 80px;
            position: relative;
            padding: 0 60px;
        }

        /* 连接线 */
        .workflow-steps::before {
            content: '';
            position: absolute;
            top: 120px;
            left: 120px;
            right: 120px;
            height: 4px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--accent-blue) 20%,
                var(--accent-cyan) 80%,
                transparent 100%
            );
            z-index: 1;
        }

        .workflow-step {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 280px;
            transition: all 0.3s;
        }

        /* 圆形卡片 */
        .step-circle {
            width: 200px;
            height: 200px;
            margin: 0 auto 32px;
            border-radius: 50%;
            background: var(--bg-primary);
            border: 3px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .step-circle::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
            opacity: 0;
            transition: opacity 0.3s;
        }

        .workflow-step:hover .step-circle {
            transform: scale(1.1) translateY(-8px);
            border-color: var(--accent-blue);
            box-shadow: 0 20px 60px rgba(94, 106, 210, 0.3);
        }

        .workflow-step:hover .step-circle::before {
            opacity: 0.1;
        }

        .step-icon {
            position: relative;
            z-index: 2;
            font-size: 48px;
            font-weight: 800;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .workflow-step h4 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            letter-spacing: -0.3px;
        }

        .workflow-step p {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* 步骤指示点 */
        .step-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--border-color);
            margin: 0 auto 16px;
            position: relative;
            z-index: 3;
        }

        .workflow-step:hover .step-indicator {
            background: var(--accent-blue);
            box-shadow: 0 0 20px var(--accent-blue);
        }

        /* CTA Section */
        .cta-section {
            padding: 140px 0;
            text-align: center;
            background: var(--bg-primary);
            position: relative;
            overflow: hidden;
        }

        .cta-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, var(--glow-blue) 0%, transparent 70%);
            opacity: 0.15;
            pointer-events: none;
        }

        .cta-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 32px;
            padding: 80px 80px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        [data-theme="dark"] .cta-card {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.9), rgba(37, 37, 37, 0.9));
        }

        .cta-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-cyan));
        }

        .cta-card h2 {
            font-size: 42px;
            font-weight: 700;
            letter-spacing: -1.5px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .cta-card p {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            position: relative;
            z-index: 1;
        }

        .cta-card .btn {
            padding: 18px 40px;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        /* Scattered Demo Images */
        .cta-demo-images {
            position: absolute;
            inset: 0;
            pointer-events: none;
            overflow: visible;
            perspective: 2000px;
        }

        .demo-image {
            position: absolute;
            border-radius: 20px;
            opacity: 0.95;
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow:
                0 50px 100px rgba(0, 0, 0, 0.5),
                0 20px 60px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            filter: blur(0px);
            transform-style: preserve-3d;
        }

        /* Demo 1 - Top Left, strong 3D tilt */
        .demo-image-1 {
            width: 700px;
            top: -5%;
            left: 10%;
            transform: rotateY(-25deg) rotateX(15deg) rotateZ(-12deg);
            z-index: 10;
        }

        /* Demo 2 - Top Right, 3D perspective */
        .demo-image-2 {
            width: 750px;
            top: -2%;
            right: -4%;
            transform: rotateY(30deg) rotateX(-10deg) rotateZ(8deg);
            z-index: 11;
        }

        /* Demo 3 - Bottom Left, dramatic angle */
        .demo-image-3 {
            width: 650px;
            bottom: -6%;
            left: 0%;
            transform: rotateY(-20deg) rotateX(-15deg) rotateZ(20deg);
            z-index: 10;
        }

        /* Demo 4 - Bottom Right, floating perspective */
        .demo-image-4 {
            width: 800px;
            bottom: -8%;
            right: -2%;
            transform: rotateY(25deg) rotateX(10deg) rotateZ(-15deg);
            z-index: 11;
        }

        /* Subtle floating animation with 3D */
        @keyframes float3d {
            0%, 100% {
                transform: translateY(0px) rotateY(var(--rotate-y)) rotateX(var(--rotate-x)) rotateZ(var(--rotate-z));
            }
            50% {
                transform: translateY(-25px) rotateY(var(--rotate-y)) rotateX(var(--rotate-x)) rotateZ(var(--rotate-z));
            }
        }

        .demo-image-1 {
            --rotate-y: -25deg;
            --rotate-x: 15deg;
            --rotate-z: -12deg;
            animation: float3d 7s ease-in-out infinite;
        }

        .demo-image-2 {
            --rotate-y: 30deg;
            --rotate-x: -10deg;
            --rotate-z: 8deg;
            animation: float3d 8s ease-in-out infinite 0.5s;
        }

        .demo-image-3 {
            --rotate-y: -20deg;
            --rotate-x: -15deg;
            --rotate-z: 20deg;
            animation: float3d 9s ease-in-out infinite 1s;
        }

        .demo-image-4 {
            --rotate-y: 25deg;
            --rotate-x: 10deg;
            --rotate-z: -15deg;
            animation: float3d 7.5s ease-in-out infinite 1.5s;
        }

        /* Hover effect - enhance 3D */
        .cta-section:hover .demo-image {
            opacity: 1;
        }

        .cta-section:hover .demo-image-1 {
            transform: translateY(-40px) rotateY(-35deg) rotateX(20deg) rotateZ(-18deg) scale(1.08);
        }

        .cta-section:hover .demo-image-2 {
            transform: translateY(-35px) rotateY(40deg) rotateX(-15deg) rotateZ(12deg) scale(1.06);
        }

        .cta-section:hover .demo-image-3 {
            transform: translateY(-45px) rotateY(-30deg) rotateX(-20deg) rotateZ(25deg) scale(1.07);
        }

        .cta-section:hover .demo-image-4 {
            transform: translateY(-50px) rotateY(35deg) rotateX(15deg) rotateZ(-20deg) scale(1.09);
        }

        /* Footer */
        footer {
            padding: 60px 0;
            border-top: 1px solid var(--border-color);
            text-align: center;
            background: var(--bg-primary);
        }

        footer p {
            font-size: 14px;
            color: var(--text-muted);
        }

        footer a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
        }

        footer a:hover {
            color: var(--text-primary);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .invitation-grid {
                grid-template-columns: 1fr;
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .workflow-steps {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 769px) and (max-width: 1200px) {
            .invitation-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .invitation-card:hover {
                flex: 1.3;
            }

            .invitation-grid:hover .invitation-card:not(:hover) {
                flex: 0.8;
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 24px;
            }

            header {
                padding: 20px 24px;
            }

            .hero h1 {
                font-size: 48px;
                letter-spacing: -1.5px;
            }

            .hero-subtitle {
                font-size: 17px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .workflow-steps {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 36px;
            }

            .cta-card {
                padding: 50px 30px;
            }

            .cta-card h2 {
                font-size: 32px;
            }

            /* Hide demo images on mobile */
            .demo-image {
                display: none;
            }

            nav {
                gap: 16px;
            }

            nav a:not(.btn) {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .hero-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .hero h1 {
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="logo">
            <img src="/apps/eagent/ea-large.png" alt="EAGENT" style="width: 32px; height: 32px; object-fit: contain;">
            <span>EAGENT</span>
        </div>
        <nav>
            <a href="#invitation">邀请</a>
            <a href="#features">功能</a>
            <a href="#workflow">工作流</a>
            <a href="/apps/eagent/" class="btn btn-primary">
                进入平台
                <i data-lucide="arrow-right" style="width: 16px; height: 16px;"></i>
            </a>
            <button class="theme-toggle" id="themeToggle" aria-label="切换主题">
                <i data-lucide="moon" class="icon-moon" style="width: 20px; height: 20px;"></i>
                <i data-lucide="sun" class="icon-sun" style="width: 20px; height: 20px;"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-bg"></div>
        <div class="hero-overlay"></div>

        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <div class="hero-badge-dot"></div>
                    <i data-lucide="cpu" style="width: 16px; height: 16px;"></i>
                    <span>电镀添加剂智能筛选 · AI 助手</span>
                </div>

                <h1>智能加速<br>电镀配方研发</h1>

                <p class="hero-subtitle">
                    专为电镀行业打造的 AI 研发助手。预测添加剂表面行为与性能参数，
                    智能筛选高性能分子，加速配方研发与工艺优化。
                </p>

                <div class="hero-actions">
                    <a href="/apps/eagent/" class="btn btn-primary">
                        <i data-lucide="zap" style="width: 20px; height: 20px;"></i>
                        立即体验
                    </a>
                    <a href="#features" class="btn btn-secondary">了解更多</a>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">50000+</div>
                        <div class="stat-label">分子数据</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5800+</div>
                        <div class="stat-label">文献论文</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24✖️7</div>
                        <div class="stat-label">智能助手</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">AI</div>
                        <div class="stat-label">AI 赋能研发</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Invitation Section -->
    <section class="invitation-section" id="invitation">
        <div class="container">
            <div class="section-header">
                <div class="section-label">申请邀请</div>
                <h2 class="section-title">加入智能研发新时代</h2>
                <p class="section-description">
                    申请邀请码，体验 EAGENT 带来的智能研发加速
                </p>
            </div>

            <div class="invitation-grid">
                <div class="invitation-card">
                    <img src="/apps/eagent/Invitation1_compressed.jpg" alt="邀请码申请流程" class="invitation-image">
                    <div class="invitation-content">
                        <h3>简单申请流程</h3>
                        <p>填写基本信息，说明您的使用需求，我们会在 1-2 个工作日内审核您的申请并通过邮件发送邀请码。</p>
                    </div>
                </div>

                <div class="invitation-card">
                    <img src="/apps/eagent/Invitation2_compressed.jpg" alt="平台功能展示" class="invitation-image">
                    <div class="invitation-content">
                        <h3>强大功能支持</h3>
                        <p>获得邀请码后，即可使用分子数据库、文献库、AI 智能助手等全方位功能，加速您的研发进程。</p>
                    </div>
                </div>

                <div class="invitation-card">
                    <img src="/apps/eagent/invitation3_compressed.jpg" alt="智能研发平台" class="invitation-image">
                    <div class="invitation-content">
                        <h3>智能研发平台</h3>
                        <p>整合前沿 AI 技术，为电镀添加剂研发提供智能预测、分子筛选、配方优化等全流程技术支持。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Electric Current Effect -->
    <div class="electric-current-container">
        <svg class="electric-current-svg" viewBox="0 0 1440 100" preserveAspectRatio="none">
            <defs>
                <linearGradient id="currentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#22C55E;stop-opacity:0" />
                    <stop offset="10%" style="stop-color:#22C55E;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#4ADE80;stop-opacity:1" />
                    <stop offset="90%" style="stop-color:#22C55E;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#22C55E;stop-opacity:0" />
                </linearGradient>
                <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
            <!-- Main lightning bolt -->
            <path class="lightning-path" d="M0,50 L200,50 L250,30 L300,70 L350,50 L500,50 L550,20 L600,80 L650,50 L800,50 L850,35 L900,65 L950,50 L1100,50 L1150,25 L1200,75 L1250,50 L1440,50"
                  fill="none" stroke="url(#currentGradient)" stroke-width="3" filter="url(#glow)" />
            <!-- Secondary thinner bolt -->
            <path class="lightning-path-2" d="M0,50 L180,50 L220,40 L260,60 L300,50 L450,50 L500,25 L550,75 L600,50 L750,50 L800,30 L850,70 L900,50 L1050,50 L1100,35 L1150,65 L1200,50 L1440,50"
                  fill="none" stroke="url(#currentGradient)" stroke-width="1.5" opacity="0.6" />
        </svg>
        <div class="electric-sparks">
            <div class="spark spark-1"></div>
            <div class="spark spark-2"></div>
            <div class="spark spark-3"></div>
            <div class="spark spark-4"></div>
            <div class="spark spark-5"></div>
        </div>
    </div>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <div class="section-label">自研模型</div>
                <h2 class="section-title">四大核心 AI 预测模型</h2>
                <p class="section-description">基于海量数据训练，为电镀添加剂研发提供精准预测与智能分析</p>
            </div>

            <div class="features-grid">
                <div class="feature-card-image">
                    <img src="/apps/eagent/absorb_compressed.jpg" alt="吸附能预测模型" class="model-image">
                    <div class="model-content">
                        <h3>吸附能预测模型</h3>
                        <p>基于 1.2 万条高精度计算数据训练，精准预测分子在镀层表面的吸附能，快速筛选高性能添加剂分子</p>
                    </div>
                </div>

                <div class="feature-card-image">
                    <img src="/apps/eagent/abso_compressed.jpg" alt="吸附构型预测模型" class="model-image">
                    <div class="model-content">
                        <h3>吸附构型预测模型</h3>
                        <p>AI 驱动的微观形貌预测，准确还原分子吸附到镀层表面的三维构型与空间分布</p>
                    </div>
                </div>

                <div class="feature-card-image">
                    <img src="/apps/eagent/price_compressed.jpg" alt="可合成性预测模型" class="model-image">
                    <div class="model-content">
                        <h3>可合成性预测模型</h3>
                        <p>综合千万级原材料价格与获取难易度数据，智能评价新设计分子的合成成本与商业可行性</p>
                    </div>
                </div>

                <div class="feature-card-image">
                    <img src="/apps/eagent/cvs_compressed.jpg" alt="CVS 曲线仿真算法" class="model-image">
                    <div class="model-content">
                        <h3>CVS 曲线仿真算法</h3>
                        <p>基于分子动力学模拟的高效计算模拟，通过并行计算技术，快速生成准确的 CVS 曲线预测结果</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials" id="testimonials">
        <div class="container">
            <div class="section-header">
                <div class="section-label">用户评价</div>
                <h2 class="section-title">他们都在使用 EAGENT</h2>
                <p class="section-description">来自各行各业的科研人员与企业的真实反馈</p>
                <p class="section-description">（仅为演示虚构人物，若巧合侵犯，请联系删除）</p>
            </div>

            <!-- 第一行：向左滚动 -->
            <div class="testimonials-scroll-track">
                <div class="testimonials-scroll testimonials-scroll-left">
                    <!-- 企业用户 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>EAGENT 帮助我们研发团队将添加剂研发周期缩短了 60%。吸附能预测模型的准确率令人印象深刻，大大减少了我们的实验试错成本。</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/李建国_compressed.jpg" alt="李建国">
                            </div>
                            <div class="author-info">
                                <div class="author-name">李建国</div>
                                <div class="author-role">研发总监</div>
                                <div class="author-organization">某表面技术科技有限公司</div>
                            </div>
                        </div>
                    </div>

                    <!-- 研究者 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>作为一名电化学研究者，EAGENT 的分子库和文献库功能极大地提升了我的研究效率。能够在一个平台上完成从文献调研到分子设计的全流程工作。</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/王明远_compressed.jpg" alt="王明远">
                            </div>
                            <div class="author-info">
                                <div class="author-name">王明远</div>
                                <div class="author-role">电化学研究员</div>
                                <div class="author-organization">某化学研究所</div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>EAGENT 的智能对话系统像一位全天候的导师，无论是理论知识还是实验问题，都能给出详细专业的解答，对我的毕业论文帮助很大。</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/张雅琳_compressed.jpg" alt="张雅琳">
                            </div>
                            <div class="author-info">
                                <div class="author-name">张雅琳</div>
                                <div class="author-role">博士研究生</div>
                                <div class="author-organization">某理工大学</div>
                            </div>
                        </div>
                    </div>

                    <!-- 教授 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>EAGENT 整合了电镀领域的知识图谱和先进的 AI 预测模型，为教学和科研都提供了强有力的支持。我推荐所有相关专业的学生使用这个平台。</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/陈志强_compressed.jpg" alt="陈志强">
                            </div>
                            <div class="author-info">
                                <div class="author-name">陈志强</div>
                                <div class="author-role">教授、博士生导师</div>
                                <div class="author-organization">某化工大学</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二行：向右滚动 -->
            <div class="testimonials-scroll-track">
                <div class="testimonials-scroll testimonials-scroll-right">
                    <!-- 下游厂商 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>作为电镀厂，我们最关注的是成本和效果。EAGENT 的可合成性预测模型帮我们找到了性价比更高的添加剂方案，每月节省了数万元原料成本。</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/刘建军_compressed.jpg" alt="刘建军">
                            </div>
                            <div class="author-info">
                                <div class="author-name">刘建军</div>
                                <div class="author-role">技术厂长</div>
                                <div class="author-organization">某精密电镀厂</div>
                            </div>
                        </div>
                    </div>

                    <!-- 计算化学实习生 -->
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>只能说 EAGENT 的分子库搜索还是太全面了🐂🍺 没有这个平台我真的很难快速做完工作... 失去eagent的世界... 办不到啊😭</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="/apps/eagent/people/小鹿gugu_compressed.jpg" alt="小鹿gugu">
                            </div>
                            <div class="author-info">
                                <div class="author-name">小鹿gugu</div>
                                <div class="author-role">计算化学实习生</div>
                                <div class="author-organization">某高校</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Workflow Section -->
    <section class="workflow" id="workflow">
        <div class="container">
            <div class="section-header">
                <div class="section-label">工作流程</div>
                <h2 class="section-title">高效研发三步走</h2>
                <p class="section-description">从数据查询到实验验证，全流程智能化支持</p>
            </div>

            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-circle">
                        <div class="step-icon">01</div>
                    </div>
                    <div class="step-indicator"></div>
                    <h4>数据查询</h4>
                    <p>从分子库和文献库检索相关信息，快速获取所需数据和知识</p>
                </div>

                <div class="workflow-step">
                    <div class="step-circle">
                        <div class="step-icon">02</div>
                    </div>
                    <div class="step-indicator"></div>
                    <h4>AI 分析</h4>
                    <p>智能助手提供专业建议与方案，辅助决策和研发方向规划</p>
                </div>

                <div class="workflow-step">
                    <div class="step-circle">
                        <div class="step-icon">03</div>
                    </div>
                    <div class="step-indicator"></div>
                    <h4>实验验证</h4>
                    <p>工作台工具辅助模拟与计算，验证理论预测和优化方案</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-glow"></div>
        <!-- Scattered demo images as background decoration -->
        <div class="cta-demo-images">
            <img src="/apps/eagent/demo1.png" alt="Demo 1" class="demo-image demo-image-1">
            <img src="/apps/eagent/demo2.png" alt="Demo 2" class="demo-image demo-image-2">
            <img src="/apps/eagent/demo3.png" alt="Demo 3" class="demo-image demo-image-3">
            <img src="/apps/eagent/demo4.png" alt="Demo 4" class="demo-image demo-image-4">
        </div>
        <div class="container">
            <div class="cta-card">
                <h2>开启智能研发之旅</h2>
                <p>立即体验 EAGENT，让 AI 助力您的电镀添加剂研发</p>
                <a href="/apps/eagent/" class="btn btn-primary">
                    <i data-lucide="rocket" style="width: 20px; height: 20px;"></i>
                    开始使用
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <p>&copy; 2025 EAGENT · 电镀添加剂智能研发平台 | <a href="/apps/eagent/">进入平台</a></p>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Parallax effect for hero background
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroBg = document.querySelector('.hero-bg');
            if (heroBg) {
                heroBg.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Apply initial styles and observe elements (excluding stat-items which have their own scroll animation)
        document.querySelectorAll('.feature-card, .workflow-step, .invitation-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(el);
        });

        // Electric Current Animation on Scroll
        const electricCurrentContainer = document.querySelector('.electric-current-container');
        let hasTriggered = false;

        if (electricCurrentContainer) {
            const triggerAnimation = () => {
                if (hasTriggered) return;
                hasTriggered = true;
                electricCurrentContainer.classList.add('active');
            };

            // Trigger on page load after a short delay
            setTimeout(triggerAnimation, 1000);

            // Also trigger when scrolled into view
            const currentObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !hasTriggered) {
                        triggerAnimation();
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            currentObserver.observe(electricCurrentContainer);
        }

        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to dark
        const currentTheme = localStorage.getItem('theme') || 'dark';
        html.setAttribute('data-theme', currentTheme);

        themeToggle.addEventListener('click', () => {
            const theme = html.getAttribute('data-theme');
            const newTheme = theme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update header background immediately
            updateHeaderBackground();
        });

        // Header background on scroll - with theme support
        const header = document.querySelector('header');

        function updateHeaderBackground() {
            const theme = html.getAttribute('data-theme');
            const bgColor = theme === 'dark'
                ? (window.scrollY > 100 ? 'rgba(14, 14, 14, 0.95)' : 'rgba(14, 14, 14, 0.8)')
                : (window.scrollY > 100 ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.8)');
            header.style.background = bgColor;
        }

        window.addEventListener('scroll', updateHeaderBackground);

        // Initialize header background on page load
        updateHeaderBackground();

        // Features cards scroll animation
        const featureCards = document.querySelectorAll('.feature-card-image');
        const featuresSection = document.querySelector('.features');

        window.addEventListener('scroll', () => {
            if (!featuresSection) return;

            const rect = featuresSection.getBoundingClientRect();

            // 只有当section向上离开视口（向下滚动）时才触发
            if (rect.bottom < window.innerHeight && rect.top < 0) {
                // 计算离开视口的进度
                const distanceLeft = -rect.top;
                const maxDistance = window.innerHeight * 0.5;
                const progress = Math.min(1, distanceLeft / maxDistance);
                const maxOffset = 40; // 最多移动40px
                const offset = progress * maxOffset;

                featureCards.forEach((card, index) => {
                    const isOdd = index % 2 === 0; // 第1、3个（索引0、2）
                    const translateY = isOdd ? -offset : offset;
                    card.style.transform = `translateY(${translateY}px)`;
                });
            } else {
                // 在视口内或还没离开时保持整齐
                featureCards.forEach(card => {
                    card.style.transform = 'translateY(0)';
                });
            }
        });

        /*
        // Duplicate testimonial cards for seamless infinite scroll
        document.querySelectorAll('.testimonials-scroll').forEach(scroll => {
            const cards = scroll.innerHTML;
            // Duplicate content 3 times for smooth infinite scrolling
            scroll.innerHTML = cards + cards + cards;
        });
        */

        // Stats Grid scroll animation
        const statsGrid = document.querySelector('.stats-grid');
        const statItems = document.querySelectorAll('.stat-item');

        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            const maxScroll = 400; // 滚动距离

            if (scrollY <= maxScroll) {
                const progress = scrollY / maxScroll;
                const cornerRadius = 12 + (progress * 20); // 圆角从 12px 增加到 32px
                const translateX = progress * 150; // 向两边移动 150px
                const opacity = 1 - (progress * 1.5); // 透明度逐渐降低
                const scale = 1 - (progress * 0.2); // 稍微缩小

                statItems.forEach((item, index) => {
                    // 计算每个卡片的移动方向
                    const direction = index < 2 ? -1 : 1; // 前两个向左，后两个向右
                    const finalTranslateX = translateX * direction;

                    item.style.borderRadius = `${cornerRadius}px`;
                    item.style.transform = `translateX(${finalTranslateX}px) scale(${scale})`;
                    item.style.opacity = Math.max(0, opacity);
                });
            }
        });
    </script>
</body>
</html>
