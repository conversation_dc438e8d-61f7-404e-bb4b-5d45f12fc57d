"""
分子生成日志组件
支持层次化、beam search 形式的日志记录，便于前端可视化
支持本地文件和 MinIO 远程存储
"""

import json
import logging
import time
import asyncio
from datetime import datetime
from typing import TYPE_CHECKING, Dict, List, Optional
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import httpx


if TYPE_CHECKING:
    from .mol_generator import MolGenerator

try:
    from minio import Minio
    from minio.error import S3Error
    HAS_MINIO = True
except ImportError:
    HAS_MINIO = False
import io
import os


class LogLevel(Enum):
    """日志级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class MolLoggerHandler(logging.Handler):
    """
    自定义 logging Handler，将 Python logging 输出转发到 MolGenerationLogger

    这样 mol_generator.py 中的 logger.info() 等调用会自动被捕获到 mol_logger.messages 中
    """

    def __init__(self, mol_logger: 'MolGenerationLogger'):
        """初始化 Handler

        Args:
            mol_logger: MolGenerationLogger 实例
        """
        super().__init__()
        self.mol_logger = mol_logger

    def emit(self, record: logging.LogRecord):
        """处理日志记录

        Args:
            record: Python logging LogRecord 对象
        """
        try:
            # 格式化日志消息
            msg = self.format(record)

            # 映射 logging level 到 MolLogger level
            level_map = {
                logging.INFO: LogLevel.INFO,
                logging.WARNING: LogLevel.WARNING,
                logging.ERROR: LogLevel.ERROR,
                logging.DEBUG: LogLevel.INFO,
                logging.CRITICAL: LogLevel.ERROR,
            }
            level = level_map.get(record.levelno, LogLevel.INFO)

            # 添加到 mol_logger.messages
            self.mol_logger.add_message({
                "type": "log",
                "level": level.value,
                "message": msg,
                "timestamp": time.time(),
            })
        except Exception:
            # Handler 不能抛出异常，否则会中断日志流程
            self.handleError(record)


@dataclass
class LayerInfo:
    """层信息"""

    layer_num: int
    start_time: float
    end_time: Optional[float] = None
    total_generated: int = 0
    unique_mols: int = 0
    filtered_mols: int = 0
    scored_mols: int = 0
    top_n: int = 0
    best_smiles: Optional[str] = None
    best_score: float = 9999

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "layer_num": self.layer_num,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": (self.end_time - self.start_time) if self.end_time else None,
            "total_generated": self.total_generated,
            "unique_mols": self.unique_mols,
            "filtered_mols": self.filtered_mols,
            "scored_mols": self.scored_mols,
            "top_n": self.top_n,
            "best_smiles": self.best_smiles,
            "best_score": self.best_score,
        }


@dataclass
class CandidateInfo:
    """候选分子信息"""

    smiles: str
    score: float
    volume: float
    diffusion: float
    adsorption: float
    layer: int
    parent_smiles: Optional[str] = None
    is_selected: bool = False
    rank: Optional[int] = None

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "smiles": self.smiles,
            "score": self.score,
            "volume": self.volume,
            "diffusion": self.diffusion,
            "adsorption": self.adsorption,
            "layer": self.layer,
            "parent_smiles": self.parent_smiles,
            "is_selected": self.is_selected,
            "rank": self.rank,
        }


# =============================================================================
# MinIO 远程存储客户端
# =============================================================================

class MinIOLogClient:
    """MinIO 远程日志存储客户端"""

    def __init__(
        self,
        endpoint: str = "localhost:9000",
        access_key: str = "minioadmin",
        secret_key: str = "minioadmin",
        bucket_name: str = "molecule-gen",
        secure: bool = False,
        user_id: str = "anonymous",
        session_id: str = "default",
        run_id: str = "default",
    ):
        """
        初始化 MinIO 客户端
        """
        self.bucket_name = bucket_name
        self.user_id = user_id
        self.session_id = session_id
        self.run_id = run_id
        self.log_id = run_id
        self.is_connected = False
        
        # Buffer for logs
        self.logs_buffer = []

        if HAS_MINIO:
            try:
                self.client = Minio(
                    endpoint,
                    access_key=access_key,
                    secret_key=secret_key,
                    secure=secure
                )
                self.is_connected = True
                
                # Check if bucket exists
                if not self.client.bucket_exists(bucket_name):
                    try:
                        self.client.make_bucket(bucket_name)
                    except Exception:
                        pass
            except Exception as e:
                print(f"MinIO Connection Failed: {e}")

    def _get_path(self, filename: str) -> str:
        # Path: runs/run_id/filename (简化路径)
        return f"runs/{self.run_id}/{filename}"

    def _upload_json(self, filename: str, data: Dict):
        if not self.is_connected:
            return
        try:
            content = json.dumps(data, ensure_ascii=False).encode('utf-8')
            self.client.put_object(
                self.bucket_name,
                self._get_path(filename),
                io.BytesIO(content),
                len(content),
                content_type="application/json"
            )
        except Exception as e:
            print(f"MinIO Upload Error ({filename}): {e}")

    def _upload_text(self, filename: str, text: str):
        if not self.is_connected:
            return
        try:
            content = text.encode('utf-8')
            self.client.put_object(
                self.bucket_name,
                self._get_path(filename),
                io.BytesIO(content),
                len(content),
                content_type="text/plain"
            )
        except Exception as e:
            print(f"MinIO Upload Error ({filename}): {e}")

    async def start_log(self, input_smiles: str) -> str:
        """开始一个新的日志记录"""
        status = {
            "status": "running",
            "log_id": self.run_id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "input_smiles": input_smiles,
            "start_time": time.time(),
            "last_update": time.time(),
            "messages": [] # Initial empty messages
        }
        self._upload_json("status.json", status)
        self._upload_text("logs.jsonl", "")
        return self.run_id

    async def append_log(self, level: str, message: str, data: Optional[Dict] = None):
        """添加一条日志记录"""
        log_entry = {
            "timestamp": time.time(),
            "level": level,
            "message": message,
            "data": data,
        }
        self.logs_buffer.append(log_entry)
        
        # Upload logs.jsonl (overwrite)
        # Note: This might be heavy for many logs, but meets "stream write to file" requirement
        logs_str = "\n".join([json.dumps(entry, ensure_ascii=False) for entry in self.logs_buffer])
        self._upload_text("logs.jsonl", logs_str)
        
        # Update status.json with simple summary
        status = {
            "status": "running",
            "log_id": self.run_id,
            "last_update": time.time(),
            "summary": message,
            # We could include latest N messages here for quick preview
            "latest_messages": self.logs_buffer[-5:] if self.logs_buffer else []
        }
        self._upload_json("status.json", status)

    async def complete_log(
        self,
        metadata: Dict,
        layers: List[Dict],
        candidates: Dict[int, List[Dict]],
        final_results: Dict[str, Dict],
    ):
        """完成日志记录"""
        # 确保 metadata 包含关联信息
        enriched_metadata = {
            **metadata,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "run_id": self.run_id,
        }

        # Final status
        status = {
            "status": enriched_metadata.get("status", "completed"),
            "log_id": self.run_id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "metadata": enriched_metadata,
            "last_update": time.time()
        }
        self._upload_json("status.json", status)

        # Full log
        log_data = {
            "metadata": enriched_metadata,
            "layers": layers,
            "candidates": candidates,
            "final_results": final_results,
        }
        self._upload_json("complete_log.json", log_data)

    async def close(self):
        """关闭客户端"""
        pass


# =============================================================================
# 分子生成日志记录器
# =============================================================================


class MolGenerationLogger:
    """
    分子生成日志记录器

    支持层次化日志记录，便于前端可视化 beam search 过程
    """

    def __init__(
        self,
        output_dir: str,
        log_name: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        enable_json: bool = True,
        enable_remote: bool = False,
        minio_endpoint: str = "localhost:9000",
        minio_access_key: str = "minioadmin",
        minio_secret_key: str = "minioadmin123",
        minio_bucket: str = "molecule-gen",
        user_id: str = "anonymous",
        session_id: str = "default",
    ):
        """
        初始化日志记录器

        Args:
            output_dir: 输出目录
            log_name: 日志名称（默认使用时间戳）
            enable_console: 是否启用控制台输出
            enable_file: 是否启用文本文件输出
            enable_json: 是否启用 JSON 文件输出（用于前端可视化）
            enable_remote: 是否启用 MinIO 远程存储
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 日志名称
        if log_name is None:
            log_name = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_name = log_name

        # 配置
        self.enable_console = enable_console
        self.enable_file = enable_file
        self.enable_json = enable_json
        self.enable_remote = enable_remote

        # 远程存储客户端
        self.remote_client: Optional[MinIOLogClient] = None
        if enable_remote:
            self.remote_client = MinIOLogClient(
                endpoint=minio_endpoint,
                access_key=minio_access_key,
                secret_key=minio_secret_key,
                bucket_name=minio_bucket,
                user_id=user_id,
                session_id=session_id,
                run_id=self.log_name,
            )

        # 日志数据
        self.start_time = time.time()
        self.end_time = None

        # 输入信息
        self.input_smiles: Optional[str] = None
        self.orig_properties: Optional[Dict] = None

        # 配置信息
        self.config: Dict = {}

        # 层信息列表
        self.layers: List[LayerInfo] = []

        # 所有候选分子（按层组织）
        self.candidates_by_layer: Dict[int, List[CandidateInfo]] = {}

        # 当前层信息
        self.current_layer: Optional[LayerInfo] = None

        # 树状结构（用于前端可视化 beam search）
        self.tree: Dict = {
            "root": None,
            "nodes": {},
            "edges": [],
        }

        # 消息日志（用于实时显示）
        self.messages: List[Dict] = []

        # 初始化文件日志
        if self.enable_file:
            self._init_file_logger()

    def _init_file_logger(self):
        """初始化文件日志记录器"""
        log_file = self.output_dir / f"{self.log_name}.log"

        # 配置 logging
        self.logger = logging.getLogger(f"MolGen_{self.log_name}")
        self.logger.setLevel(logging.INFO)

        # 清除已有的 handlers
        self.logger.handlers.clear()

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # 控制台 handler
        if self.enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter("%(levelname)s - %(message)s")
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

    def attach_to_logger(self, target_logger: logging.Logger):
        """
        将此 mol_logger 附加到指定的 Python logger

        这样 target_logger 的所有日志输出会自动被捕获到 mol_logger.messages 中

        Args:
            target_logger: 要附加的 Python logger 对象

        Example:
            >>> import logging
            >>> mol_logger = MolGenerationLogger(output_dir="./logs")
            >>> generator_logger = logging.getLogger("mol_generator")
            >>> mol_logger.attach_to_logger(generator_logger)
        """
        # 创建自定义 Handler
        handler = MolLoggerHandler(self)
        handler.setLevel(logging.INFO)

        # 设置格式（与文件日志一致）
        formatter = logging.Formatter("%(levelname)s - %(message)s")
        handler.setFormatter(formatter)

        # 添加到目标 logger
        target_logger.addHandler(handler)

        # 记录挂载信息
        self.info(f"已挂载到 logger: {target_logger.name}")

    def set_input(
        self, smiles: str, orig_vol: float, orig_diff: float, orig_ads: float
    ):
        """设置输入信息"""
        self.input_smiles = smiles
        self.orig_properties = {
            "volume": orig_vol,
            "diffusion": orig_diff,
            "adsorption": orig_ads,
        }

        # 设置树根节点
        self.tree["root"] = smiles
        self.tree["nodes"][smiles] = {
            "smiles": smiles,
            "layer": 0,
            "properties": self.orig_properties,
            "is_root": True,
        }

        self.info(f"输入分子: {smiles}")
        self.info(
            f"原始属性 - 体积: {orig_vol:.4f}, 扩散: {orig_diff:.4f}, 吸附: {orig_ads:.4f}"
        )

        # 启动远程日志
        if self.enable_remote and self.remote_client:
            try:
                # 在新线程中启动远程日志（避免阻塞）
                def _start_remote():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        log_id = loop.run_until_complete(
                            self.remote_client.start_log(smiles)
                        )
                        self.info(f"远程日志已启动: {log_id}")
                    finally:
                        loop.close()

                import threading
                thread = threading.Thread(target=_start_remote, daemon=True)
                thread.start()
            except Exception as e:
                self.warning(f"远程日志启动失败: {e}")

    def set_config(self, **kwargs):
        """设置配置信息"""
        self.config = kwargs
        self.info(f"配置: {kwargs}")

    def start_layer(self, layer_num: int) -> LayerInfo:
        """开始新的一层"""
        self.current_layer = LayerInfo(
            layer_num=layer_num,
            start_time=time.time(),
        )
        self.layers.append(self.current_layer)

        self.info(f"========== 第 {layer_num + 1} 层开始 ==========")
        return self.current_layer

    def update_layer_generation(
        self,
        total_generated: int,
        unique_mols: int,
        elapsed: float,
    ):
        """更新层生成信息"""
        if self.current_layer:
            self.current_layer.total_generated = total_generated
            self.current_layer.unique_mols = unique_mols

            speed = total_generated / elapsed if elapsed > 0 else 0
            self.info(
                f"生成: {total_generated} 个分子, 去重后 {unique_mols} 个, "
                f"耗时 {elapsed:.2f}s, 速度: {speed:.2f} mol/s"
            )

            self.add_message(
                {
                    "type": "generation",
                    "layer": self.current_layer.layer_num,
                    "total_generated": total_generated,
                    "unique_mols": unique_mols,
                    "elapsed": elapsed,
                    "speed": speed,
                }
            )

    def update_layer_filtering(self, filtered_count: int):
        """更新层筛选信息"""
        if self.current_layer:
            self.current_layer.filtered_mols = filtered_count
            removed = self.current_layer.unique_mols - filtered_count
            self.info(f"筛选: 剩余 {filtered_count} 个分子 （移除 {removed} 个）")

            self.add_message(
                {
                    "type": "filtering",
                    "layer": self.current_layer.layer_num,
                    "filtered_count": filtered_count,
                    "removed": removed,
                }
            )

    def update_layer_scoring(
        self,
        scored_count: int,
        best_smiles: str,
        best_score: float,
    ):
        """更新层评分信息"""
        if self.current_layer:
            self.current_layer.scored_mols = scored_count
            self.current_layer.best_smiles = best_smiles
            self.current_layer.best_score = best_score

            self.info(
                f"评分: 完成 {scored_count} 个分子, "
                f"最佳: {best_smiles}, Score: {best_score:.4f}"
            )

            self.add_message(
                {
                    "type": "scoring",
                    "layer": self.current_layer.layer_num,
                    "scored_count": scored_count,
                    "best_smiles": best_smiles,
                    "best_score": best_score,
                }
            )

    def update_layer_selection(self, top_n: int, selected_smiles: List[str]):
        """更新层选择信息"""
        if self.current_layer:
            self.current_layer.top_n = top_n
            self.info(f"选择: Top {top_n} 个候选分子")

            self.add_message(
                {
                    "type": "selection",
                    "layer": self.current_layer.layer_num,
                    "top_n": top_n,
                    "selected_smiles": selected_smiles[:5],  # 只保存前5个用于显示
                }
            )

    def end_layer(self):
        """结束当前层"""
        if self.current_layer:
            self.current_layer.end_time = time.time()
            duration = self.current_layer.end_time - self.current_layer.start_time

            self.info(
                f"========== 第 {self.current_layer.layer_num} 层完成 "
                f"(耗时 {duration:.2f}s) =========="
            )

            self.add_message(
                {
                    "type": "layer_end",
                    "layer": self.current_layer.layer_num,
                    "duration": duration,
                }
            )

            self.current_layer = None

    def add_candidate(
        self,
        smiles: str,
        score: float,
        volume: float,
        diffusion: float,
        adsorption: float,
        layer: int,
        parent_smiles: Optional[str] = None,
        is_selected: bool = False,
        rank: Optional[int] = None,
    ):
        """添加候选分子信息"""
        candidate = CandidateInfo(
            smiles=smiles,
            score=score,
            volume=volume,
            diffusion=diffusion,
            adsorption=adsorption,
            layer=layer,
            parent_smiles=parent_smiles,
            is_selected=is_selected,
            rank=rank,
        )

        if layer not in self.candidates_by_layer:
            self.candidates_by_layer[layer] = []
        self.candidates_by_layer[layer].append(candidate)

        # 添加到树结构
        self.tree["nodes"][smiles] = {
            "smiles": smiles,
            "layer": layer,
            "score": score,
            "volume": volume,
            "diffusion": diffusion,
            "adsorption": adsorption,
            "is_selected": is_selected,
            "rank": rank,
        }

        # 添加边
        if parent_smiles:
            self.tree["edges"].append(
                {
                    "from": parent_smiles,
                    "to": smiles,
                    "layer": layer,
                }
            )

    def info(self, message: str):
        """记录 INFO 级别消息"""
        if self.enable_file or self.enable_console:
            self.logger.info(message)

        self.add_message(
            {
                "type": "log",
                "level": LogLevel.INFO.value,
                "message": message,
                "timestamp": time.time(),
            }
        )

    def warning(self, message: str):
        """记录 WARNING 级别消息"""
        if self.enable_file or self.enable_console:
            self.logger.warning(message)

        self.add_message(
            {
                "type": "log",
                "level": LogLevel.WARNING.value,
                "message": message,
                "timestamp": time.time(),
            }
        )

    def error(self, message: str):
        """记录 ERROR 级别消息"""
        if self.enable_file or self.enable_console:
            self.logger.error(message)

        self.add_message(
            {
                "type": "log",
                "level": LogLevel.ERROR.value,
                "message": message,
                "timestamp": time.time(),
            }
        )

    def success(self, message: str):
        """记录 SUCCESS 级别消息"""
        if self.enable_file or self.enable_console:
            self.logger.info(f"✓ {message}")

        self.add_message(
            {
                "type": "log",
                "level": LogLevel.SUCCESS.value,
                "message": message,
                "timestamp": time.time(),
            }
        )

    def _sync_to_remote(self, level: str, message: str, data: Optional[Dict] = None):
        """同步日志到远程存储（异步，不阻塞主流程）"""
        if not self.enable_remote or not self.remote_client:
            return

        def _sync():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(
                        self.remote_client.append_log(level, message, data)
                    )
                finally:
                    loop.close()
            except Exception as e:
                # 远程同步失败不影响主流程
                print(f"Remote sync failed: {e}")

        import threading
        # ✅ 修复：使用非 daemon 线程，确保上传完成
        # 但是设置超时避免阻塞太久
        thread = threading.Thread(target=_sync, daemon=False)
        thread.start()
        # 等待最多 1 秒，如果还没完成就继续（不阻塞主流程）
        thread.join(timeout=1.0)

    def add_message(self, message: Dict):
        """添加消息"""
        message["timestamp"] = message.get("timestamp", time.time())
        self.messages.append(message)

        # ✅ 实时同步到远程存储（MinIO）
        if self.enable_remote and self.remote_client:
            level = message.get("level", "info")
            msg = message.get("message", "")
            self._sync_to_remote(level, msg, message.get("data"))

    def finish(self, total_results: int, wait_remote: bool = True):
        """完成生成

        Args:
            total_results: 生成的结果总数
            wait_remote: 是否等待远程上传完成（默认 True）
        """
        self.end_time = time.time()
        duration = self.end_time - self.start_time

        self.success(f"生成完成！共 {total_results} 个结果，总耗时 {duration:.2f}s")

        self.add_message(
            {
                "type": "finish",
                "total_results": total_results,
                "duration": duration,
            }
        )

        # 保存 JSON 文件
        if self.enable_json:
            self.save_json()

        # 上传到远程存储
        upload_thread = None
        if self.enable_remote and self.remote_client:
            def _upload_remote():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # 准备完整日志数据
                        metadata = {
                            "log_id": getattr(self.remote_client, 'log_id', self.log_name),
                            "input_smiles": self.input_smiles,
                            "created_at": datetime.fromtimestamp(self.start_time).isoformat(),
                            "duration": duration,
                            "total_layers": len(self.layers),
                            "total_generated": sum(lr.total_generated for lr in self.layers),
                            "total_results": total_results,
                            "status": "completed",
                        }

                        layers = [layer.to_dict() for layer in self.layers]
                        candidates = {
                            str(layer): [c.to_dict() for c in candidates]
                            for layer, candidates in self.candidates_by_layer.items()
                        }

                        # final_results 从 candidates 提取
                        final_results = {}
                        for smiles, props in self.tree["nodes"].items():
                            if smiles != self.tree.get("root"):  # 排除根节点
                                final_results[smiles] = {
                                    "volume": props.get("volume", 0),
                                    "diffusion_coeff": props.get("diffusion", 0),
                                    "adsorption_energy": props.get("adsorption", 0),
                                    "score": props.get("score", 9999),
                                }

                        loop.run_until_complete(
                            self.remote_client.complete_log(
                                metadata=metadata,
                                layers=layers,
                                candidates=candidates,
                                final_results=final_results,
                            )
                        )
                        self.info(f"远程日志已上传: {metadata['log_id']}")
                    finally:
                        loop.close()
                except Exception as e:
                    self.warning(f"远程日志上传失败: {e}")

            import threading
            # 使用非 daemon 线程，确保上传完成
            thread = threading.Thread(target=_upload_remote, daemon=False)
            thread.start()
            upload_thread = thread

        # 如果需要等待远程上传
        if wait_remote and upload_thread:
            upload_thread.join(timeout=30)  # 最多等待 30 秒

    def save_json(self):
        """保存 JSON 格式的日志（用于前端可视化）"""
        json_file = self.output_dir / f"{self.log_name}.json"

        data = {
            "metadata": {
                "log_name": self.log_name,
                "start_time": self.start_time,
                "end_time": self.end_time,
                "duration": (self.end_time - self.start_time)
                if self.end_time
                else None,
                "input_smiles": self.input_smiles,
                "orig_properties": self.orig_properties,
                "config": self.config,
            },
            "layers": [layer.to_dict() for layer in self.layers],
            "candidates": {
                str(layer): [c.to_dict() for c in candidates]
                for layer, candidates in self.candidates_by_layer.items()
            },
            "tree": self.tree,
            "messages": self.messages,
        }

        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        self.info(f"JSON 日志已保存到: {json_file}")

    def get_summary(self) -> Dict:
        """获取摘要信息"""
        return {
            "log_name": self.log_name,
            "input_smiles": self.input_smiles,
            "orig_properties": self.orig_properties,
            "total_layers": len(self.layers),
            "total_generated": sum(lr.total_generated for lr in self.layers),
            "duration": (self.end_time - self.start_time)
            if self.end_time
            else (time.time() - self.start_time),
            "config": self.config,
        }

    def get_layers_summary(self) -> List[Dict]:
        """获取各层摘要"""
        return [layer.to_dict() for layer in self.layers]

    def get_top_candidates(self, n: int = 10) -> List[Dict]:
        """获取 Top N 候选分子"""
        all_candidates = []
        for candidates in self.candidates_by_layer.values():
            all_candidates.extend(candidates)

        # 按评分排序
        sorted_candidates = sorted(all_candidates, key=lambda c: c.score)
        return [c.to_dict() for c in sorted_candidates[:n]]

    def get_tree_data(self) -> Dict:
        """获取树状结构数据（用于前端可视化）"""
        return self.tree

    def get_messages(self, after_timestamp: Optional[float] = None) -> List[Dict]:
        """获取消息（用于实时更新）"""
        if after_timestamp is None:
            return self.messages

        return [m for m in self.messages if m["timestamp"] > after_timestamp]


# 便捷函数
def create_logger(
    output_dir: str,
    log_name: Optional[str] = None,
    enable_console: bool = True,
    enable_file: bool = True,
    enable_json: bool = True,
    enable_remote: bool = False,
    minio_endpoint: str = "localhost:9000",
    minio_access_key: str = "minioadmin",
    minio_secret_key: str = "minioadmin123",
    minio_bucket: str = "molecule-gen",
    user_id: str = "anonymous",
    session_id: str = "default",
) -> MolGenerationLogger:
    """
    创建分子生成日志记录器

    Args:
        output_dir: 输出目录
        log_name: 日志名称
        enable_console: 是否启用控制台输出
        enable_file: 是否启用文本文件输出
        enable_json: 是否启用 JSON 文件输出
        enable_remote: 是否启用 MinIO 远程存储

    Returns:
        MolGenerationLogger 实例
    """
    return MolGenerationLogger(
        output_dir=output_dir,
        log_name=log_name,
        enable_console=enable_console,
        enable_file=enable_file,
        enable_json=enable_json,
        enable_remote=enable_remote,
        minio_endpoint=minio_endpoint,
        minio_access_key=minio_access_key,
        minio_secret_key=minio_secret_key,
        minio_bucket=minio_bucket,
        user_id=user_id,
        session_id=session_id,
    )
