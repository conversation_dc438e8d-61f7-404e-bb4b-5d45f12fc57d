"""
分子生成器
整合所有组件，实现完整的分子生成流程
"""

from rdkit import Chem
from rdkit.Chem import AllChem, rdMolDescriptors, Crippen
from rdkit import RDLogger
from typing import List, Dict, Tuple, Optional, Callable, TYPE_CHECKING
import logging
import time

from .mol_modifier import MolModifier
from .predictor_client import RemotePredictorClient, get_predictor

# 禁用 RDKit 的警告日志
RDLogger.DisableLog("rdApp.*")

# 类型提示
if TYPE_CHECKING:
    from .mol_logger import MolGenerationLogger


# =============================================================================
# 分子预处理器
# =============================================================================


class MolPreprocessor:
    """分子预处理器"""

    def generate_conformer(self, mol: Chem.Mol) -> Chem.Mol:
        """
        生成分子构象

        按优先级尝试多种方法:
        1. ETKDG (高精度)
        2. MMFF (中精度)
        3. UFF (中低精度)
        4. 随机构象 (低精度)
        """
        # ETKDG
        res = AllChem.EmbedMolecule(mol, AllChem.ETKDG())
        if res == 0:
            return mol

        # MMFF
        res = AllChem.EmbedMolecule(mol, randomSeed=42)
        if res == 0:
            AllChem.MMFFOptimizeMolecule(mol)
            return mol

        # UFF
        res = AllChem.EmbedMolecule(mol, randomSeed=42)
        if res == 0:
            AllChem.UFFOptimizeMolecule(mol)
            return mol

        # 随机构象
        num_confs = 10
        conf_ids = AllChem.EmbedMultipleConfs(mol, numConfs=num_confs)
        if conf_ids:
            return mol

        raise ValueError("所有构象生成方法均失败")

    def calculate_volume(self, mol: Chem.Mol) -> float:
        """计算分子体积"""
        volume = AllChem.ComputeMolVolume(mol)
        return volume

    def preprocess_mol(self, mol: Chem.Mol) -> Tuple[Chem.Mol, float]:
        """
        完整的预处理流程

        包括: 加氢、kekulize、构象生成、体积计算
        """
        if mol is None:
            raise ValueError("分子对象为 None")

        # 加氢
        mol = Chem.AddHs(mol)

        # Kekulize (尽可能尝试，失败也不影响后续流程)
        try:
            Chem.Kekulize(mol, clearAromaticFlags=True)
        except Exception:
            # Kekulize 失败时，尝试使用 SanitizeMol
            try:
                Chem.SanitizeMol(mol, sanitizeOps=Chem.SANITIZE_KEKULIZE)
            except Exception:
                pass  # 如果还是失败，继续执行

        # 更新属性缓存 (使用 strict=False 容忍错误)
        mol.UpdatePropertyCache(strict=False)
        try:
            _ = mol.GetRingInfo()
        except Exception:
            pass

        # 生成构象
        mol = self.generate_conformer(mol)

        # 计算体积
        volume = self.calculate_volume(mol)

        return mol, volume


# =============================================================================
# 分子筛选器
# =============================================================================


class MolFilter:
    """分子筛选器"""

    def __init__(
        self,
        max_molecular_weight_ratio: float = 2.0,
        min_logp: float = -10.0,
    ):
        """
        初始化分子筛选器

        Args:
            max_molecular_weight_ratio: 最大分子量倍数（相对于原分子）
            min_logp: 最小 logP 值
        """
        self.max_molecular_weight_ratio = max_molecular_weight_ratio
        self.min_logp = min_logp

    def calc_logp(self, mol: Chem.Mol) -> float:
        """计算 logP"""
        try:
            return Crippen.MolLogP(mol)
        except Exception:
            return float("-inf")

    def calc_molecular_weight(self, mol: Chem.Mol) -> float:
        """计算分子量"""
        return rdMolDescriptors.CalcExactMolWt(mol)

    def filter_by_properties(
        self,
        mols: List[Chem.Mol],
        orig_mol: Chem.Mol,
        logger: Optional[logging.Logger] = None,
    ) -> List[Chem.Mol]:
        """
        根据性质筛选分子

        筛选条件:
        1. 分子量 < 原分子量 * max_molecular_weight_ratio
        2. logP >= min_logp
        """
        if not mols:
            return []

        # 计算原始分子量
        orig_mol_weight = self.calc_molecular_weight(orig_mol)
        max_weight = orig_mol_weight * self.max_molecular_weight_ratio

        filtered_mols = []
        filtered_by_weight = 0
        filtered_by_logp = 0

        for mol in mols:
            if mol is None:
                continue

            # 检查分子量
            mol_weight = self.calc_molecular_weight(mol)
            if mol_weight >= max_weight:
                filtered_by_weight += 1
                continue

            # 检查 logP
            logp = self.calc_logp(mol)
            if logp < self.min_logp:
                filtered_by_logp += 1
                continue

            filtered_mols.append(mol)

        if logger and (filtered_by_weight > 0 or filtered_by_logp > 0):
            logger.info(f"    [筛选详情] 因分子量过滤: {filtered_by_weight} 个 (Max: {max_weight:.2f})")
            logger.info(f"    [筛选详情] 因 LogP 过滤: {filtered_by_logp} 个 (Min: {self.min_logp:.2f})")

        return filtered_mols


# =============================================================================
# 分子评分器
# =============================================================================


class MolScorer:
    """分子评分器"""

    def __init__(
        self,
        volume_weight: float = 1.0,
        diffusion_weight: float = 1.0,
        adsorption_weight: float = 3.0,
    ):
        """
        初始化分子评分器

        Args:
            volume_weight: 体积差异的权重
            diffusion_weight: 扩散系数差异的权重
            adsorption_weight: 吸附能差异的权重
        """
        self.weights = {
            "volume": volume_weight,
            "diffusion": diffusion_weight,
            "adsorption": adsorption_weight,
        }

    def calculate_score(
        self,
        mol_properties: Dict[str, float],
        orig_properties: Dict[str, float],
    ) -> float:
        """
        计算综合评分

        评分公式:
        score = volume_weight * |Δvolume/volume| +
                diffusion_weight * |Δdiff/diff| +
                adsorption_weight * |Δads/ads|

        评分越低表示与原分子性质越接近
        """
        # 检查是否有无效值
        if (
            mol_properties.get("volume", 9999) == 9999
            or mol_properties.get("diffusion", 9999) == 9999
            or mol_properties.get("adsorption", 9999) == 9999
        ):
            return 9999

        # 计算各项差异
        diff_vol = abs(
            (mol_properties["volume"] - orig_properties["volume"])
            / orig_properties["volume"]
        )
        diff_diff = abs(
            (mol_properties["diffusion"] - orig_properties["diffusion"])
            / orig_properties["diffusion"]
        )
        diff_ads = abs(
            (mol_properties["adsorption"] - orig_properties["adsorption"])
            / orig_properties["adsorption"]
        )

        # 加权求和
        score = (
            self.weights["volume"] * diff_vol
            + self.weights["diffusion"] * diff_diff
            + self.weights["adsorption"] * diff_ads
        )

        return score

    def rank_candidates(
        self,
        scored_candidates: Dict[str, Tuple[Dict, Chem.Mol]],
    ) -> List[Tuple[str, Dict, Chem.Mol]]:
        """
        对候选分子排序

        Args:
            scored_candidates: 候选分子字典
                {
                    smiles: (properties_dict, mol),
                    ...
                }

        Returns:
            排序后的列表 [(smiles, properties_dict, mol), ...]
        """
        sorted_candidates = sorted(
            [
                (smiles, props_dict, mol)
                for smiles, (props_dict, mol) in scored_candidates.items()
            ],
            key=lambda item: item[1]["score"],
        )

        return sorted_candidates

    def get_top_candidates(
        self,
        sorted_candidates: List[Tuple[str, Dict, Chem.Mol]],
        top_n: int,
        deduplicate: bool = True,
    ) -> List[Tuple[str, Dict, Chem.Mol]]:
        """
        获取前 N 个候选分子
        """
        if not sorted_candidates:
            return []

        if not deduplicate:
            return sorted_candidates[:top_n]

        # 去重
        unique_smiles = set()
        top_candidates = []

        for smiles, props_dict, mol in sorted_candidates:
            if len(unique_smiles) >= top_n:
                break

            if smiles not in unique_smiles:
                unique_smiles.add(smiles)
                top_candidates.append((smiles, props_dict, mol))

        return top_candidates

    def format_results(
        self,
        sorted_candidates: List[Tuple[str, Dict, Chem.Mol]],
    ) -> Dict[str, Dict]:
        """
        格式化结果为标准字典格式
        """
        results = {}
        for smiles, props_dict, _ in sorted_candidates:
            results[smiles] = {
                "volume": props_dict.get("volume", 0.0),
                "diffusion_coeff": props_dict.get("diffusion", 0.0),
                "adsorption_energy": props_dict.get("adsorption", 0.0),
                "score": props_dict.get("score", 9999),
            }
        return results


# =============================================================================
# 批量预测协调器
# =============================================================================


class PropertyCalculator:
    """属性计算器（预处理 + 批量预测 + 评分）"""

    def __init__(
        self,
        preprocessor: Optional[MolPreprocessor] = None,
        predictor: Optional[RemotePredictorClient] = None,
    ):
        self.preprocessor = preprocessor or MolPreprocessor()
        self.predictor = predictor or get_predictor()

    def process_mols_with_scoring(
        self,
        mols: List[Chem.Mol],
        orig_properties: Dict[str, float],
    ) -> Dict[str, tuple]:
        """
        批量处理分子并返回带评分的结果

        Returns:
            候选分子字典 {smiles: (properties_dict, mol), ...}
        """
        if not mols:
            return {}

        # 1. 预处理：计算体积
        mol_volume_pairs = []
        smiles_list = []

        for mol in mols:
            if mol is None:
                continue
            try:
                processed_mol, volume = self.preprocessor.preprocess_mol(mol)
                smiles = Chem.MolToSmiles(processed_mol, canonical=True)
                mol_volume_pairs.append((processed_mol, volume))
                smiles_list.append(smiles)
            except Exception:
                continue

        if not smiles_list:
            return {}

        # 2. 批量预测吸附能和扩散系数
        prediction_results = self.predictor.predict_batch(smiles_list)

        # 3. 计算评分并构建结果
        scored_candidates = {}

        for (mol, volume), pred_result in zip(mol_volume_pairs, prediction_results):
            if not pred_result.success:
                continue

            properties = {
                "volume": volume,
                "adsorption": pred_result.adsorption,
                "diffusion": pred_result.diffusion,
            }

            # 计算评分
            properties["score"] = self._calculate_score(properties, orig_properties)

            smiles = Chem.MolToSmiles(mol, canonical=True)
            scored_candidates[smiles] = (properties, mol)

        return scored_candidates

    def _calculate_score(
        self,
        mol_properties: Dict[str, float],
        orig_properties: Dict[str, float],
    ) -> float:
        """计算评分"""
        if (
            mol_properties.get("volume", 9999) == 9999
            or mol_properties.get("diffusion", 9999) == 9999
            or mol_properties.get("adsorption", 9999) == 9999
        ):
            return 9999

        # 计算各项差异
        diff_vol = abs(
            (mol_properties["volume"] - orig_properties["volume"])
            / orig_properties["volume"]
        )
        diff_diff = abs(
            (mol_properties["diffusion"] - orig_properties["diffusion"])
            / orig_properties["diffusion"]
        )
        diff_ads = abs(
            (mol_properties["adsorption"] - orig_properties["adsorption"])
            / orig_properties["adsorption"]
        )

        # 加权求和（默认权重：volume=1, diffusion=1, adsorption=3）
        return diff_vol + diff_diff + 3 * diff_ads

    def clear_cache(self):
        """清空缓存"""
        if self.predictor:
            self.predictor.clear_cache()


# =============================================================================
# 分子生成器 - 主流程协调器
# =============================================================================


class MolGenerator:
    """分子生成器 - 主流程协调器"""

    def __init__(
        self,
        modifier: MolModifier,
        preprocessor: Optional[MolPreprocessor] = None,
        calculator: Optional[PropertyCalculator] = None,
        filter: Optional[MolFilter] = None,
        scorer: Optional[MolScorer] = None,
        mol_logger: Optional["MolGenerationLogger"] = None,
    ):
        """
        初始化分子生成器

        Args:
            modifier: 分子修饰器
            preprocessor: 分子预处理器（可选，默认创建新实例）
            calculator: 属性计算器（可选，默认创建新实例）
            filter: 分子筛选器（可选，默认创建新实例）
            scorer: 分子评分器（可选，默认创建新实例）
            mol_logger: 分子生成日志记录器（可选）
        """
        self.modifier = modifier
        self.preprocessor = preprocessor or MolPreprocessor()
        self.calculator = calculator or PropertyCalculator(
            preprocessor=self.preprocessor
        )
        self.filter = filter or MolFilter()
        self.scorer = scorer or MolScorer()
        self.mol_logger = mol_logger

        # 统计信息
        self.stats = {
            "total_layers": 0,
            "total_generated": 0,
            "total_filtered": 0,
            "total_time": 0.0,
        }

    def generate(
        self,
        scaffold_smiles: str,
        orig_vol: float,
        orig_diff: float,
        orig_ads: float,
        top_num: int = 50,
        top_num_last: int = 100,
        logger: Optional[logging.Logger] = None,
        progress_callback: Optional[Callable[[int, str, any], None]] = None,
    ) -> Dict[str, Dict]:
        """
        执行完整的分子生成流程

        Args:
            scaffold_smiles: 骨架分子的 SMILES
            orig_vol: 原始分子体积
            orig_diff: 原始分子扩散系数
            orig_ads: 原始分子吸附能
            top_num: 中间层保留的候选数
            top_num_last: 最后一层保留的候选数
            logger: 日志记录器（可选）
            progress_callback: 进度回调函数 (layer, message, data) -> None
                - layer: 当前层数
                - message: 进度消息
                - data: 附加数据字典 (可选)
                  - level: 日志级别 ('info', 'warning', 'error', 'success')
                  - smiles: 当前处理的分子 SMILES
                  - properties: 分子属性字典
                  - stats: 统计信息

        Returns:
            结果字典
            {
                smiles: {
                    "volume": v,
                    "diffusion_coeff": d,
                    "adsorption_energy": a,
                    "score": s,
                },
                ...
            }
        """
        if logger is None:
            logger = logging.getLogger(__name__)

        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # 将 mol_logger 挂载到 logger，这样所有 logger.info() 输出都会被捕获到 mol_logger.messages
        # if self.mol_logger:
        #     self.mol_logger.attach_to_logger(logger)

        # 原始属性
        orig_properties = {
            "volume": orig_vol,
            "diffusion": orig_diff,
            "adsorption": orig_ads,
        }

        logger.info("=" * 60)
        logger.info("开始分子生成流程")
        logger.info(f"骨架分子: {scaffold_smiles}")
        logger.info(
            f"原始属性 - 体积: {orig_vol:.2f}, 扩散: {orig_diff:.4f}, 吸附: {orig_ads:.4f}"
        )
        logger.info(f"配置参数 - 中间层保留数: {top_num}, 最终保留数: {top_num_last}")
        logger.info("=" * 60)

        # 发送开始事件
        if progress_callback:
            progress_callback(-1, "开始分子生成流程", {
                "level": "info",
                "smiles": scaffold_smiles,
                "properties": {
                    "volume": orig_vol,
                    "diffusion": orig_diff,
                    "adsorption": orig_ads,
                },
            })

        # 使用 mol_logger 记录输入
        if self.mol_logger:
            self.mol_logger.set_input(scaffold_smiles, orig_vol, orig_diff, orig_ads)
            self.mol_logger.set_config(
                top_num=top_num,
                top_num_last=top_num_last,
            )

        # 查找可替换位点
        atoms_sites, scaffold_mol = self.modifier.find_substitutable_atoms(
            scaffold_smiles
        )
        total_layers = len(atoms_sites)

        logger.info(f"识别到 {total_layers} 个可替换位点")
        logger.info(f"可替换原子索引: {atoms_sites}")

        # 发送层数信息
        if progress_callback:
            progress_callback(-1, f"识别到 {total_layers} 个可替换位点", {
                "level": "info",
                "stats": {"total_layers": total_layers},
            })

        if self.mol_logger:
            self.mol_logger.info(f"共 {total_layers} 个可替换位点")

        # 初始化队列
        mols_list = [scaffold_mol]
        atoms_sites_list = [atoms_sites]
        layer_list = [0]

        # 重置统计
        self.stats = {
            "total_layers": total_layers,
            "total_generated": 0,
            "total_filtered": 0,
            "total_time": 0.0,
        }

        start_time = time.time()

        # 逐层生成
        for layer in range(total_layers):
            if progress_callback:
                progress_callback(layer, f"开始第 {layer + 1}/{total_layers} 层生成", {
                    "level": "info",
                    "stats": {
                        "current_layer": layer + 1,
                        "total_layers": total_layers,
                    },
                })

            logger.info("")
            logger.info("=" * 60)
            logger.info(f"第 {layer + 1}/{total_layers} 层生成")
            logger.info("=" * 60)

            # 开始层日志
            if self.mol_logger:
                self.mol_logger.start_layer(layer)

            # 记录当前层的父分子
            parent_smiles_list = [Chem.MolToSmiles(mol) for mol in mols_list]

            # 处理当前层
            (
                new_mols_list,
                new_atoms_sites_list,
                new_layer_list,
                elapsed_time,
            ) = self._process_layer(
                mols_list,
                atoms_sites_list,
                layer_list,
                layer,
                total_layers,
                logger,
            )

            if not new_mols_list:
                logger.warning(f"第 {layer} 层无有效分子，停止生成")
                if self.mol_logger:
                    self.mol_logger.warning(f"第 {layer} 层无有效分子")
                break

            logger.info(f"✓ 生成 {len(new_mols_list)} 个新分子")

            # 更新生成日志
            if self.mol_logger:
                self.mol_logger.update_layer_generation(
                    total_generated=len(new_mols_list),
                    unique_mols=len(new_mols_list),
                    elapsed=elapsed_time,
                )

            # 发送生成进度
            if progress_callback:
                progress_callback(layer, f"生成 {len(new_mols_list)} 个新分子", {
                    "level": "success",
                    "stats": {
                        "generated": len(new_mols_list),
                        "unique": len(new_mols_list),
                    },
                })

            # 筛选
            if progress_callback:
                progress_callback(layer, f"筛选分子（共 {len(new_mols_list)} 个）")

            filtered_mols = self.filter.filter_by_properties(
                new_mols_list, scaffold_mol, logger=logger
            )

            logger.info(
                f"✓ 筛选后剩余 {len(filtered_mols)} 个分子 (过滤掉 {len(new_mols_list) - len(filtered_mols)} 个)"
            )

            # 发送筛选进度
            if progress_callback:
                progress_callback(layer, f"筛选后剩余 {len(filtered_mols)} 个分子", {
                    "level": "info",
                    "stats": {
                        "filtered": len(filtered_mols),
                        "filtered_out": len(new_mols_list) - len(filtered_mols),
                    },
                })

            # 更新筛选日志
            if self.mol_logger:
                self.mol_logger.update_layer_filtering(len(filtered_mols))

            if not filtered_mols:
                logger.warning("筛选后无有效分子，停止生成")
                if self.mol_logger:
                    self.mol_logger.warning("筛选后无有效分子")
                break

            # 批量预测和评分
            if progress_callback:
                progress_callback(layer, f"预测性质（共 {len(filtered_mols)} 个分子）", {
                    "level": "info",
                })

            logger.info(f"⏳ 正在批量预测 {len(filtered_mols)} 个分子的性质...")
            scored_candidates = self.calculator.process_mols_with_scoring(
                filtered_mols, orig_properties
            )
            logger.info(f"✓ 完成 {len(scored_candidates)} 个分子的性质预测")

            # 排序
            sorted_candidates = self.scorer.rank_candidates(scored_candidates)

            # 更新评分日志
            if self.mol_logger and sorted_candidates:
                best_smiles, best_props, _ = sorted_candidates[0]
                self.mol_logger.update_layer_scoring(
                    scored_count=len(sorted_candidates),
                    best_smiles=best_smiles,
                    best_score=best_props.get("score", 9999),
                )

                # 记录所有候选分子到日志
                for rank, (smiles, props, _) in enumerate(sorted_candidates, 1):
                    # 尝试从父分子列表中查找（简化逻辑）
                    parent_smiles = (
                        parent_smiles_list[0] if parent_smiles_list else None
                    )

                    self.mol_logger.add_candidate(
                        smiles=smiles,
                        score=props.get("score", 9999),
                        volume=props.get("volume", 0),
                        diffusion=props.get("diffusion", 0),
                        adsorption=props.get("adsorption", 0),
                        layer=layer,
                        parent_smiles=parent_smiles,
                        rank=rank,
                        is_selected=False,  # 稍后更新
                    )

            # 选择 Top N
            current_top_num = top_num_last if layer == total_layers - 1 else top_num
            top_candidates = self.scorer.get_top_candidates(
                sorted_candidates, current_top_num, deduplicate=True
            )

            logger.info(f"✓ 选择 Top {len(top_candidates)} 个候选分子进入下一层")

            # 发送选择进度
            if progress_callback:
                progress_callback(layer, f"选择 Top {len(top_candidates)} 个候选分子", {
                    "level": "info",
                    "stats": {"top_n": len(top_candidates)},
                })

            # 更新选择日志
            if self.mol_logger:
                selected_smiles = [smiles for smiles, _, _ in top_candidates]
                self.mol_logger.update_layer_selection(
                    len(top_candidates), selected_smiles
                )

                # 标记选中的候选
                for smiles, _, _ in top_candidates:
                    if layer in self.mol_logger.candidates_by_layer:
                        for cand in self.mol_logger.candidates_by_layer[layer]:
                            if cand.smiles == smiles:
                                cand.is_selected = True

            # 打印最佳结果
            if sorted_candidates:
                best_smiles, best_props, _ = sorted_candidates[0]
                logger.info(f"  最佳候选: {best_smiles}")
                logger.info(f"    Score: {best_props.get('score', 9999):.4f}")
                logger.info(f"    体积: {best_props.get('volume', 0):.2f}")
                logger.info(f"    扩散: {best_props.get('diffusion', 0):.4f}")
                logger.info(f"    吸附: {best_props.get('adsorption', 0):.4f}")

                # 发送最佳分子信息
                if progress_callback:
                    progress_callback(layer, f"最佳候选: {best_smiles}", {
                        "level": "success",
                        "smiles": best_smiles,
                        "properties": {
                            "volume": best_props.get('volume', 0),
                            "diffusion": best_props.get('diffusion', 0),
                            "adsorption": best_props.get('adsorption', 0),
                            "score": best_props.get('score', 9999),
                        },
                    })

            # 更新队列
            mols_list = [mol for _, _, mol in top_candidates]
            new_atoms_sites_list = [
                new_atoms_sites_list[i] for i in range(len(top_candidates))
            ]
            new_layer_list = [new_layer_list[i] for i in range(len(top_candidates))]

            atoms_sites_list = new_atoms_sites_list
            layer_list = new_layer_list

            # 结束层日志
            if self.mol_logger:
                self.mol_logger.end_layer()

        # 完成
        elapsed_time = time.time() - start_time
        self.stats["total_time"] = elapsed_time

        # 格式化最终结果
        final_candidates = self._get_final_candidates(mols_list, orig_properties)
        results = self.scorer.format_results(final_candidates)

        logger.info("")
        logger.info("=" * 60)
        logger.info("分子生成完成！")
        logger.info(f"总耗时: {elapsed_time:.2f} 秒")
        logger.info(f"最终结果数: {len(results)} 个分子")
        logger.info("=" * 60)

        # 发送完成事件
        if progress_callback:
            progress_callback(total_layers, "分子生成完成！", {
                "level": "success",
                "stats": {
                    "total_generated": self.stats.get("total_generated", 0),
                    "total_filtered": self.stats.get("total_filtered", 0),
                    "total_results": len(results),
                    "duration": elapsed_time,
                    "total_layers": total_layers,
                },
            })

        # 打印 Top 5 结果
        if results:
            sorted_results = sorted(
                results.items(), key=lambda x: x[1].get("score", 9999)
            )
            logger.info("\nTop 5 候选分子:")
            for i, (smiles, props) in enumerate(sorted_results[:5], 1):
                logger.info(f"  {i}. {smiles}")
                logger.info(
                    f"     Score: {props.get('score', 9999):.4f}, "
                    f"体积: {props.get('volume', 0):.2f}, "
                    f"扩散: {props.get('diffusion_coeff', 0):.4f}, "
                    f"吸附: {props.get('adsorption_energy', 0):.4f}"
                )

        # 完成日志
        if self.mol_logger:
            self.mol_logger.finish(total_results=len(results))

        return results

    def _process_layer(
        self,
        mols_list: List[Chem.Mol],
        atoms_sites_list: List[List[int]],
        layer_list: List[int],
        current_layer: int,
        total_layers: int,
        logger: logging.Logger,
    ) -> Tuple[List[Chem.Mol], List[List[int]], List[int], float]:
        """
        处理单层生成

        Args:
            mols_list: 当前分子队列
            atoms_sites_list: 当前原子位点队列
            layer_list: 当前层数队列
            current_layer: 当前层号
            total_layers: 总层数
            logger: 日志记录器

        Returns:
            (新分子列表, 新原子位点列表, 新层数列表)
        """
        new_mols_list = []
        new_atoms_sites_list = []
        new_layer_list = []

        start_time = time.time()

        # 遍历队列
        while mols_list and (len(atoms_sites_list[0]) + current_layer == total_layers):
            current_mol = mols_list.pop(0)
            current_atoms_sites = atoms_sites_list.pop(0)
            current_layer_num = layer_list.pop(0)
            new_layer = current_layer_num + 1

            # 对每个位点进行修饰
            for atom_site in current_atoms_sites:
                modified_atoms_sites = current_atoms_sites.copy()
                modified_atoms_sites.remove(atom_site)

                # 不修饰的选项
                modified_mols = [current_mol]

                # 添加官能团
                group_mols, _ = self.modifier.add_groups(current_mol, atom_site)
                modified_mols.extend(group_mols)

                # 添加到新列表
                for mol in modified_mols:
                    new_mols_list.append(mol)
                    new_atoms_sites_list.append(modified_atoms_sites)
                    new_layer_list.append(new_layer)

        # 去重
        unique_mols, unique_sites, unique_layers = self._deduplicate_mols(
            new_mols_list, new_atoms_sites_list, new_layer_list
        )

        elapsed_time = time.time() - start_time
        logger.info(
            f"第 {current_layer} 层生成 {len(new_mols_list)} 个分子，"
            f"去重后 {len(unique_mols)} 个，"
            f"耗时 {elapsed_time:.2f} 秒"
        )

        # 更新统计
        self.stats["total_generated"] += len(new_mols_list)

        return unique_mols, unique_sites, unique_layers, elapsed_time

    def _deduplicate_mols(
        self,
        mols: List[Chem.Mol],
        atoms_sites_list: List[List[int]],
        layer_list: List[int],
    ) -> Tuple[List[Chem.Mol], List[List[int]], List[int]]:
        """
        去重分子（基于 SMILES + 原子位点 + 层数）
        """
        seen = set()
        unique_mols = []
        unique_sites = []
        unique_layers = []

        for mol, sites, layer in zip(mols, atoms_sites_list, layer_list):
            smiles = Chem.MolToSmiles(mol, canonical=True)
            key = (smiles, tuple(sites), layer)

            if key not in seen:
                seen.add(key)
                unique_mols.append(mol)
                unique_sites.append(sites)
                unique_layers.append(layer)

        return unique_mols, unique_sites, unique_layers

    def _get_final_candidates(
        self,
        mols: List[Chem.Mol],
        orig_properties: Dict[str, float],
    ) -> List[Tuple[str, Dict, Chem.Mol]]:
        """获取最终候选分子列表"""
        scored_candidates = self.calculator.process_mols_with_scoring(
            mols, orig_properties
        )
        candidates = [
            (smiles, props, mol) for smiles, (props, mol) in scored_candidates.items()
        ]
        return sorted(candidates, key=lambda x: x[1].get("score", 9999))

    def get_stats(self) -> Dict[str, float]:
        """获取统计信息"""
        return self.stats.copy()

    def clear_cache(self):
        """清空所有缓存"""
        self.calculator.clear_cache()

    @classmethod
    def run(
        cls,
        smiles: str,
        max_results: int = 200,
        depth: int = 5,
        groups_csv_path: str = "notring_fragments.csv",
        output_dir: Optional[str] = None,
        enable_logging: bool = True,
        **kwargs,
    ) -> Dict[str, Dict]:
        """
        一键运行分子生成（简化 API）

        Args:
            smiles: 起始分子的 SMILES
            max_results: 最大结果数（默认 200）
            depth: 生成深度/层数（默认 5）
            groups_csv_path: 官能团 CSV 文件路径
            output_dir: 输出目录（用于日志，默认 None）
            enable_logging: 是否启用日志（默认 True）
            **kwargs: 其他配置参数
                - top_num: 中间层保留的候选数（默认 50）
                - max_molecular_weight_ratio: 最大分子量倍数（默认 2.0）
                - min_logp: 最小 logP（默认 0.0）
                - volume_weight: 体积权重（默认 1.0）
                - diffusion_weight: 扩散权重（默认 1.0）
                - adsorption_weight: 吸附权重（默认 3.0）

        Returns:
            结果字典
            {
                smiles: {
                    "volume": v,
                    "diffusion_coeff": d,
                    "adsorption_energy": a,
                    "score": s,
                },
                ...
            }

        Example:
            >>> results = MolGenerator.run("Sc1ncccn1", max_results=200, depth=5)
            >>> print(f"生成了 {len(results)} 个分子")
        """
        # 1. 创建日志记录器
        mol_logger = None
        if enable_logging and output_dir:
            from .mol_logger import create_logger

            # 从 kwargs 提取远程存储配置
            enable_remote = kwargs.get("enable_remote", False)
            user_id = kwargs.get("user_id", "anonymous")
            session_id = kwargs.get("session_id", "default")
            minio_endpoint = kwargs.get("minio_endpoint", "localhost:9000")
            minio_access_key = kwargs.get("minio_access_key", "minioadmin")
            minio_secret_key = kwargs.get("minio_secret_key", "minioadmin123")
            minio_bucket = kwargs.get("minio_bucket", "molecule-gen")

            mol_logger = create_logger(
                output_dir=output_dir,
                enable_remote=enable_remote,
                user_id=user_id,
                session_id=session_id,
                minio_endpoint=minio_endpoint,
                minio_access_key=minio_access_key,
                minio_secret_key=minio_secret_key,
                minio_bucket=minio_bucket,
            )

        # 2. 计算原始分子属性
        preprocessor = MolPreprocessor()
        orig_mol = Chem.MolFromSmiles(smiles)
        if orig_mol is None:
            raise ValueError(f"无法解析 SMILES: {smiles}")

        try:
            orig_mol, orig_vol = preprocessor.preprocess_mol(orig_mol)
        except Exception:
            # 如果预处理失败，使用默认值
            orig_vol = kwargs.get("orig_vol", 150.0)

        # 获取扩散系数和吸附能
        try:
            predictor_client = get_predictor()
            result = predictor_client.predict(smiles)
            if result.success:
                orig_diff = result.diffusion
                orig_ads = result.adsorption
            else:
                # 使用默认值
                orig_diff = kwargs.get("orig_diff", 0.5)
                orig_ads = kwargs.get("orig_ads", -2.0)
        except Exception:
            # 使用默认值
            orig_diff = kwargs.get("orig_diff", 0.5)
            orig_ads = kwargs.get("orig_ads", -2.0)

        # 3. 初始化组件
        modifier = MolModifier(
            replace_atoms_dict={"C": 4, "N": 3, "O": 2, "S": 2},
            groups_csv_path=groups_csv_path,
        )

        mol_filter = MolFilter(
            max_molecular_weight_ratio=kwargs.get("max_molecular_weight_ratio", 2.0),
            min_logp=kwargs.get("min_logp", -10.0),
        )

        scorer = MolScorer(
            volume_weight=kwargs.get("volume_weight", 1.0),
            diffusion_weight=kwargs.get("diffusion_weight", 1.0),
            adsorption_weight=kwargs.get("adsorption_weight", 3.0),
        )

        # 4. 创建生成器
        generator = cls(
            modifier=modifier,
            preprocessor=preprocessor,
            filter=mol_filter,
            scorer=scorer,
            mol_logger=mol_logger,
        )

        # 5. 执行生成
        # 获取 top_num 参数
        top_num = kwargs.get("top_num", 50)
        top_num_last = max_results

        # 创建 logger 用于生成过程
        import logging
        gen_logger = logging.getLogger(f"{__name__}.run")

        results = generator.generate(
            scaffold_smiles=smiles,
            orig_vol=orig_vol,
            orig_diff=orig_diff,
            orig_ads=orig_ads,
            top_num=top_num,
            top_num_last=top_num_last,
            logger=gen_logger,  # ← 传入 logger
            progress_callback=None,  # ← run() 方法不支持进度回调
        )

        return results
