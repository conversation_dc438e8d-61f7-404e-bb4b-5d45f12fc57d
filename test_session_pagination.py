#!/usr/bin/env python3
"""
测试 session 分页和排序问题的脚本

这个脚本会：
1. 创建多个测试 sessions
2. 验证分页和排序是否正确
3. 检查新创建的 session 是否出现在正确位置
"""

import sys
import os
import time
import uuid
from datetime import datetime

# Add project root to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.store.storage import sesssion_database
from agno.session.agent import AgentSession


def create_test_session(user_id: str, agent_id: str = "test_agent") -> str:
    """创建一个测试 session"""
    session_id = str(uuid.uuid4())
    current_time = int(time.time() * 1000)  # 毫秒时间戳
    
    session = AgentSession(
        session_id=session_id,
        agent_id=agent_id,
        user_id=user_id,
        agent_data={},
        session_data={"session_name": f"Test Session {session_id[:8]}"},
        metadata={},
        created_at=current_time,
    )
    
    # 保存到数据库
    result = sesssion_database.upsert_session(session=session, deserialize=False)
    print(f"Created session: {session_id[:8]}... at {datetime.fromtimestamp(current_time/1000)}")
    
    return session_id


def test_session_pagination():
    """测试 session 分页功能"""
    print("=" * 60)
    print("测试 Session 分页和排序功能")
    print("=" * 60)
    
    test_user_id = "test_user_pagination"
    
    # 1. 清理之前的测试数据
    print("\n1. 清理之前的测试数据...")
    try:
        # 这里可以添加清理逻辑，但要小心不要删除真实数据
        pass
    except Exception as e:
        print(f"清理数据时出错: {e}")
    
    # 2. 创建多个测试 sessions（超过分页限制）
    print("\n2. 创建测试 sessions...")
    created_sessions = []
    
    # 创建足够多的 sessions 来测试分页（超过默认的 100 个限制）
    num_sessions = 15  # 创建15个，用limit=10来测试分页
    for i in range(num_sessions):
        session_id = create_test_session(test_user_id)
        created_sessions.append(session_id)
        time.sleep(0.05)  # 确保时间戳不同
    
    print(f"创建了 {len(created_sessions)} 个测试 sessions")
    
    # 3. 测试第一页数据
    print("\n3. 测试第一页数据...")
    limit = 10
    result = sesssion_database.get_sessions(
        session_type="agent",
        user_id=test_user_id,
        limit=limit,
        page=1,
        sort_by="created_at",
        sort_order="desc",
        deserialize=False
    )
    
    if isinstance(result, tuple):
        sessions_data, total_count = result
        print(f"总数: {total_count}")
        print(f"第一页返回: {len(sessions_data)} 个 sessions")
        
        # 检查排序是否正确（最新的在前面）
        if len(sessions_data) >= 2:
            first_time = sessions_data[0].get("created_at", 0)
            second_time = sessions_data[1].get("created_at", 0)
            
            print(f"第一个 session 时间: {datetime.fromtimestamp(first_time/1000 if first_time > 10000000000 else first_time)}")
            print(f"第二个 session 时间: {datetime.fromtimestamp(second_time/1000 if second_time > 10000000000 else second_time)}")
            
            if first_time >= second_time:
                print("✅ 排序正确：最新的 session 在前面")
            else:
                print("❌ 排序错误：最新的 session 不在前面")
        
        # 检查最新创建的 session 是否在第一页
        latest_session_id = created_sessions[-1]
        found_in_first_page = any(s.get("session_id") == latest_session_id for s in sessions_data)
        
        if found_in_first_page:
            print("✅ 最新创建的 session 出现在第一页")
        else:
            print("❌ 最新创建的 session 没有出现在第一页")
            print(f"最新 session ID: {latest_session_id[:8]}...")
            print("第一页的 session IDs:")
            for s in sessions_data:
                print(f"  - {s.get('session_id', '')[:8]}...")

    # 4. 测试第二页数据
    print("\n4. 测试第二页数据...")
    result2 = sesssion_database.get_sessions(
        session_type="agent",
        user_id=test_user_id,
        limit=limit,
        page=2,
        sort_by="created_at",
        sort_order="desc",
        deserialize=False
    )

    if isinstance(result2, tuple):
        sessions_data2, total_count2 = result2
        print(f"第二页返回: {len(sessions_data2)} 个 sessions")

        # 检查第二页的第一个session时间应该小于第一页的最后一个session时间
        if len(sessions_data) > 0 and len(sessions_data2) > 0:
            last_first_page_time = sessions_data[-1].get("created_at", 0)
            first_second_page_time = sessions_data2[0].get("created_at", 0)

            print(f"第一页最后一个时间: {datetime.fromtimestamp(last_first_page_time/1000 if last_first_page_time > 10000000000 else last_first_page_time)}")
            print(f"第二页第一个时间: {datetime.fromtimestamp(first_second_page_time/1000 if first_second_page_time > 10000000000 else first_second_page_time)}")

            if last_first_page_time >= first_second_page_time:
                print("✅ 分页正确：第一页最后一个时间 >= 第二页第一个时间")
            else:
                print("❌ 分页错误：时间顺序不正确")

    print("\n测试完成！")


if __name__ == "__main__":
    test_session_pagination()
