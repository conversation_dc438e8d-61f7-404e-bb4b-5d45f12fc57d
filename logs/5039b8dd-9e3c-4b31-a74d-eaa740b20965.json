{"metadata": {"log_name": "5039b8dd-9e3c-4b31-a74d-eaa740b20965", "start_time": 1770945759.460963, "end_time": 1770945786.172375, "duration": 26.711411952972412, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.03200000000002, "diffusion": 4.971, "adsorption": -50.23}, "config": {"top_num": 8, "top_num_last": 15}}, "layers": [{"layer_num": 0, "start_time": 1770945760.1370244, "end_time": 1770945770.846186, "duration": 10.709161520004272, "total_generated": 172, "unique_mols": 172, "filtered_mols": 172, "scored_mols": 119, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12051651456583529}, {"layer_num": 1, "start_time": 1770945770.9558938, "end_time": 1770945774.2975194, "duration": 3.341625690460205, "total_generated": 48, "unique_mols": 48, "filtered_mols": 48, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.06679538340634371}, {"layer_num": 2, "start_time": 1770945774.4211304, "end_time": 1770945777.3972611, "duration": 2.976130723953247, "total_generated": 40, "unique_mols": 40, "filtered_mols": 40, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1412502310465949}, {"layer_num": 3, "start_time": 1770945777.5214512, "end_time": 1770945779.9513595, "duration": 2.429908275604248, "total_generated": 32, "unique_mols": 32, "filtered_mols": 32, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.010927204527003654}, {"layer_num": 4, "start_time": 1770945780.0892985, "end_time": 1770945782.1375334, "duration": 2.0482349395751953, "total_generated": 24, "unique_mols": 24, "filtered_mols": 24, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12509700024272935}, {"layer_num": 5, "start_time": 1770945782.2657113, "end_time": 1770945783.772497, "duration": 1.5067856311798096, "total_generated": 16, "unique_mols": 16, "filtered_mols": 16, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.11144599852948982}, {"layer_num": 6, "start_time": 1770945783.8991277, "end_time": 1770945784.8826108, "duration": 0.983483076095581, "total_generated": 8, "unique_mols": 8, "filtered_mols": 8, "scored_mols": 8, "top_n": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1391193079999735}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.12051651456583529, "volume": 110.23200000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.19535893883736644, "volume": 122.04800000000003, "diffusion": 4.66, "adsorption": -49.835, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2162521240222981, "volume": 127.08000000000003, "diffusion": 5.247, "adsorption": -50.327, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.24192074487079998, "volume": 132.88000000000002, "diffusion": 5.033, "adsorption": -50.595, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.24675264894801996, "volume": 122.48000000000003, "diffusion": 4.791, "adsorption": -51.861, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2505883640700489, "volume": 132.90400000000002, "diffusion": 4.905, "adsorption": -50.723, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.2570584466726106, "volume": 126.79200000000003, "diffusion": 4.961, "adsorption": -51.95, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2632585065666182, "volume": 122.94400000000003, "diffusion": 5.058, "adsorption": -52.38, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 8}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.270731214135044, "volume": 119.15200000000003, "diffusion": 5.387, "adsorption": -48.486, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.3085017389547287, "volume": 138.94400000000005, "diffusion": 4.86, "adsorption": -49.838, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.31687749008774213, "volume": 126.35200000000003, "diffusion": 5.713, "adsorption": -49.907, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.325693946544662, "volume": 132.66400000000004, "diffusion": 5.344, "adsorption": -49.477, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.3277070082436039, "volume": 133.72800000000004, "diffusion": 4.881, "adsorption": -51.808, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.32894075976225123, "volume": 122.68800000000003, "diffusion": 4.692, "adsorption": -52.872, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.33082470748215836, "volume": 139.26400000000004, "diffusion": 5.256, "adsorption": -50.361, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3527466621279054, "volume": 138.23200000000003, "diffusion": 5.354, "adsorption": -49.905, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.35531997088690986, "volume": 118.85600000000002, "diffusion": 5.76, "adsorption": -48.281, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.3568483945454918, "volume": 127.21600000000004, "diffusion": 4.876, "adsorption": -47.19, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.3571072733854518, "volume": 127.67200000000003, "diffusion": 4.629, "adsorption": -52.373, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.35726921310622867, "volume": 129.25600000000003, "diffusion": 4.839, "adsorption": -47.618, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3695331902496629, "volume": 127.52000000000002, "diffusion": 4.012, "adsorption": -50.526, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.40648871348985205, "volume": 126.58400000000003, "diffusion": 5.553, "adsorption": -47.903, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4089605853977681, "volume": 129.56000000000003, "diffusion": 5.75, "adsorption": -51.482, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.4441769236268774, "volume": 128.69600000000003, "diffusion": 4.998, "adsorption": -54.736, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.45601861057855764, "volume": 134.45600000000005, "diffusion": 5.03, "adsorption": -53.95, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.4570964441590398, "volume": 138.08000000000004, "diffusion": 5.219, "adsorption": -52.78, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.4638942240852537, "volume": 146.75200000000004, "diffusion": 4.516, "adsorption": -49.583, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4679786181063581, "volume": 133.05600000000004, "diffusion": 5.65, "adsorption": -48.185, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.47511945437306397, "volume": 146.68800000000005, "diffusion": 5.656, "adsorption": -50.16, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4833896891612978, "volume": 150.50400000000005, "diffusion": 5.373, "adsorption": -50.811, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4950563632237167, "volume": 135.70400000000004, "diffusion": 4.098, "adsorption": -48.788, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.4954238741391419, "volume": 139.36800000000002, "diffusion": 5.46, "adsorption": -52.414, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5006663682747869, "volume": 145.21600000000004, "diffusion": 5.199, "adsorption": -52.491, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5058267943999978, "volume": 154.90400000000002, "diffusion": 4.938, "adsorption": -48.7, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5058353821116297, "volume": 135.40000000000003, "diffusion": 5.368, "adsorption": -46.958, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5228991917609764, "volume": 138.97600000000003, "diffusion": 5.728, "adsorption": -48.429, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5238626575717059, "volume": 144.86400000000003, "diffusion": 5.685, "adsorption": -51.296, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5299498151195656, "volume": 131.80800000000002, "diffusion": 5.244, "adsorption": -54.87, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.5354102074583135, "volume": 128.23200000000003, "diffusion": 5.251, "adsorption": -55.482, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.5354764050046928, "volume": 126.35200000000003, "diffusion": 4.81, "adsorption": -56.17, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5542531414747897, "volume": 127.64800000000002, "diffusion": 5.051, "adsorption": -43.9, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.559002034515947, "volume": 123.40000000000003, "diffusion": 4.291, "adsorption": -45.195, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5668106957613191, "volume": 128.72800000000004, "diffusion": 6.406, "adsorption": -52.042, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5671429742887638, "volume": 129.47200000000004, "diffusion": 5.766, "adsorption": -46.37, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.5713335422383761, "volume": 128.68000000000004, "diffusion": 5.059, "adsorption": -56.662, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5769079816976171, "volume": 143.82400000000004, "diffusion": 5.735, "adsorption": -48.286, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5890055974985976, "volume": 119.20800000000003, "diffusion": 5.964, "adsorption": -45.109, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5937685542279276, "volume": 129.12000000000003, "diffusion": 4.156, "adsorption": -45.938, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5967778673393653, "volume": 120.09600000000003, "diffusion": 6.165, "adsorption": -45.791, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.5967933973095472, "volume": 143.26400000000004, "diffusion": 4.994, "adsorption": -55.088, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.5973769653498262, "volume": 129.64000000000004, "diffusion": 6.387, "adsorption": -47.981, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N1C([H])(SS(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6062328712892293, "volume": 154.68800000000005, "diffusion": 5.482, "adsorption": -48.596, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.6119845349814321, "volume": 130.52000000000004, "diffusion": 5.001, "adsorption": -57.258, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6230749149302774, "volume": 131.30400000000003, "diffusion": 5.863, "adsorption": -46.039, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6498344686409177, "volume": 126.85600000000004, "diffusion": 4.283, "adsorption": -44.227, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6499146285949121, "volume": 132.41600000000003, "diffusion": 4.16, "adsorption": -45.486, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6627383157906817, "volume": 143.85600000000002, "diffusion": 4.986, "adsorption": -44.331, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6641753592299215, "volume": 146.84000000000003, "diffusion": 5.263, "adsorption": -45.694, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6763000833919373, "volume": 127.11200000000002, "diffusion": 5.855, "adsorption": -44.483, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6885508904313432, "volume": 138.84800000000004, "diffusion": 6.752, "adsorption": -49.085, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.6909305332321585, "volume": 145.86400000000003, "diffusion": 6.166, "adsorption": -48.139, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.7018826737476701, "volume": 138.17600000000004, "diffusion": 5.542, "adsorption": -44.684, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.7105056611026833, "volume": 155.85600000000002, "diffusion": 5.314, "adsorption": -53.998, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7170652477240053, "volume": 146.40800000000004, "diffusion": 5.864, "adsorption": -53.693, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.7184657868669229, "volume": 156.07200000000003, "diffusion": 5.842, "adsorption": -48.14, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7273911748388945, "volume": 138.50400000000002, "diffusion": 7.096, "adsorption": -49.541, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7281300831524984, "volume": 134.10400000000004, "diffusion": 5.822, "adsorption": -44.568, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7351022634847926, "volume": 136.22400000000005, "diffusion": 5.415, "adsorption": -43.403, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.7398082143419972, "volume": 149.56000000000003, "diffusion": 6.576, "adsorption": -49.264, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.740351794048888, "volume": 154.84000000000003, "diffusion": 5.266, "adsorption": -45.646, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]N1C([H])(SS(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7420808024957315, "volume": 161.27200000000005, "diffusion": 5.769, "adsorption": -48.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.7491730356136437, "volume": 137.35200000000003, "diffusion": 5.901, "adsorption": -44.976, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.7492240702319126, "volume": 142.86400000000003, "diffusion": 6.382, "adsorption": -53.026, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.7529642847968545, "volume": 155.17600000000004, "diffusion": 5.774, "adsorption": -47.197, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)C([H])([H])[H]", "score": 0.7597167608722948, "volume": 153.71200000000005, "diffusion": 5.817, "adsorption": -47.006, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7707856341071637, "volume": 145.59200000000004, "diffusion": 5.643, "adsorption": -55.461, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.7746487480410358, "volume": 126.58400000000003, "diffusion": 5.722, "adsorption": -58.152, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7765340279508787, "volume": 139.41600000000003, "diffusion": 7.403, "adsorption": -49.891, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7904393064225599, "volume": 154.59200000000004, "diffusion": 6.223, "adsorption": -52.467, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)(=O)C([H])([H])[H]", "score": 0.802451837020629, "volume": 161.56800000000004, "diffusion": 6.336, "adsorption": -51.226, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1(S(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8065854241042159, "volume": 162.12000000000003, "diffusion": 6.323, "adsorption": -49.205, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8131179390423287, "volume": 149.91200000000003, "diffusion": 5.894, "adsorption": -45.793, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.81536442588976, "volume": 121.59200000000003, "diffusion": 5.139, "adsorption": -61.557, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.8182575299138442, "volume": 140.44800000000004, "diffusion": 4.827, "adsorption": -41.643, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8276637941745864, "volume": 130.67200000000003, "diffusion": 6.169, "adsorption": -43.548, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.8296302166175903, "volume": 146.20000000000005, "diffusion": 5.645, "adsorption": -56.347, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.8322503907974157, "volume": 129.52800000000002, "diffusion": 6.5, "adsorption": -44.412, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.8421150822578232, "volume": 135.57600000000002, "diffusion": 5.712, "adsorption": -57.947, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.8429388984481926, "volume": 118.71200000000003, "diffusion": 7.374, "adsorption": -45.531, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8432993972098185, "volume": 154.42400000000004, "diffusion": 5.562, "adsorption": -44.856, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])N1[H]", "score": 0.8552615925625766, "volume": 154.23200000000003, "diffusion": 6.389, "adsorption": -53.048, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.8574632737448717, "volume": 145.84800000000004, "diffusion": 5.394, "adsorption": -57.712, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8646138067946042, "volume": 138.29600000000002, "diffusion": 5.391, "adsorption": -41.469, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8716607714251746, "volume": 133.60800000000003, "diffusion": 6.185, "adsorption": -43.312, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8885659405055906, "volume": 128.84800000000004, "diffusion": 5.947, "adsorption": -58.957, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.9020000850461187, "volume": 141.69600000000003, "diffusion": 6.737, "adsorption": -45.894, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9094578420458118, "volume": 155.94400000000005, "diffusion": 4.846, "adsorption": -42.41, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9237356461660391, "volume": 137.09600000000003, "diffusion": 5.52, "adsorption": -40.731, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9344853301122626, "volume": 129.68000000000004, "diffusion": 7.015, "adsorption": -44.458, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.9554464409200372, "volume": 151.95200000000003, "diffusion": 5.415, "adsorption": -42.107, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9728340644245961, "volume": 128.24800000000002, "diffusion": 5.858, "adsorption": -39.701, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0070610879020836, "volume": 139.90400000000002, "diffusion": 5.927, "adsorption": -41.134, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 1.0080119801148277, "volume": 136.25600000000003, "diffusion": 6.033, "adsorption": -40.92, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.0192557162413765, "volume": 152.10400000000004, "diffusion": 4.95, "adsorption": -39.637, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 1.025178669684852, "volume": 146.72800000000004, "diffusion": 6.853, "adsorption": -44.988, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 1.0374195647999036, "volume": 161.37600000000003, "diffusion": 5.897, "adsorption": -56.668, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1(S(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0401237302876334, "volume": 154.84000000000003, "diffusion": 6.361, "adsorption": -44.315, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0582535404661058, "volume": 134.90400000000002, "diffusion": 6.538, "adsorption": -41.574, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])N1[H]", "score": 1.0695630967722436, "volume": 162.21600000000004, "diffusion": 6.376, "adsorption": -55.465, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0788673204364954, "volume": 141.83200000000002, "diffusion": 6.402, "adsorption": -41.825, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 110}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.187705819699061, "volume": 147.56000000000003, "diffusion": 5.6, "adsorption": -38.173, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 111}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.2172691299263403, "volume": 122.99200000000003, "diffusion": 5.957, "adsorption": -65.318, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 112}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2338525305036359, "volume": 136.46400000000003, "diffusion": 5.364, "adsorption": -34.917, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 113}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.2624213567871052, "volume": 155.88800000000003, "diffusion": 6.338, "adsorption": -40.675, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 114}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2768778144107822, "volume": 146.06400000000002, "diffusion": 6.824, "adsorption": -40.575, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 115}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.289643171738926, "volume": 132.42400000000004, "diffusion": 5.915, "adsorption": -35.224, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 116}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.3670507308607664, "volume": 157.32000000000005, "diffusion": 5.409, "adsorption": -36.012, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 117}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.4201628129873138, "volume": 156.14400000000003, "diffusion": 6.075, "adsorption": -37.187, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 118}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.4647289912386878, "volume": 145.75200000000004, "diffusion": 6.128, "adsorption": -35.038, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 119}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.06679538340634371, "volume": 110.29600000000002, "diffusion": 4.946, "adsorption": -49.236, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2143779398423117, "volume": 122.68800000000003, "diffusion": 4.907, "adsorption": -51.678, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2145798832196225, "volume": 126.89600000000003, "diffusion": 5.247, "adsorption": -50.327, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2685147143225093, "volume": 131.88000000000002, "diffusion": 5.189, "adsorption": -50.667, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3290305038702023, "volume": 122.80800000000004, "diffusion": 4.838, "adsorption": -53.347, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.416994894779279, "volume": 126.39200000000002, "diffusion": 4.959, "adsorption": -54.682, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.48352087238329916, "volume": 122.04000000000003, "diffusion": 5.483, "adsorption": -54.774, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5784079607962387, "volume": 134.74400000000003, "diffusion": 5.452, "adsorption": -54.534, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1412502310465949, "volume": 110.49600000000002, "diffusion": 5.336, "adsorption": -51.295, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.17760625525393342, "volume": 122.50400000000003, "diffusion": 4.678, "adsorption": -50.319, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.22410116630613558, "volume": 127.20000000000003, "diffusion": 5.242, "adsorption": -50.457, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.23661319797535135, "volume": 132.29600000000002, "diffusion": 5.033, "adsorption": -50.595, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2368430527147179, "volume": 122.88800000000003, "diffusion": 4.772, "adsorption": -51.569, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.4012075002584413, "volume": 126.93600000000004, "diffusion": 4.924, "adsorption": -54.217, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4913811722736215, "volume": 134.92800000000003, "diffusion": 5.267, "adsorption": -53.672, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.4987194297305478, "volume": 122.70400000000004, "diffusion": 5.419, "adsorption": -55.143, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.010927204527003654, "volume": 110.48000000000003, "diffusion": 4.958, "adsorption": -50.159, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.1964986716189734, "volume": 122.20800000000003, "diffusion": 4.897, "adsorption": -51.418, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2145336862208927, "volume": 127.30400000000003, "diffusion": 5.079, "adsorption": -49.63, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2731781392655893, "volume": 134.28000000000003, "diffusion": 4.919, "adsorption": -50.939, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28422669765771524, "volume": 132.03200000000004, "diffusion": 5.216, "adsorption": -50.816, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.28594957817224903, "volume": 126.75200000000002, "diffusion": 4.622, "adsorption": -51.298, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3436380831078907, "volume": 122.88000000000002, "diffusion": 4.801, "adsorption": -53.456, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.35413314137849583, "volume": 123.22400000000003, "diffusion": 4.838, "adsorption": -53.704, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.12509700024272935, "volume": 110.73600000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2143779398423117, "volume": 122.68800000000003, "diffusion": 4.907, "adsorption": -51.678, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2255760649505383, "volume": 126.90400000000002, "diffusion": 5.244, "adsorption": -50.52, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.27037046887066, "volume": 134.19200000000004, "diffusion": 4.736, "adsorption": -50.171, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.36994820721762234, "volume": 122.14400000000003, "diffusion": 4.748, "adsorption": -53.83, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.432903326874386, "volume": 126.81600000000003, "diffusion": 4.954, "adsorption": -54.867, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.45252850459258065, "volume": 132.46400000000003, "diffusion": 5.279, "adsorption": -53.356, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.5210222587764858, "volume": 122.38400000000003, "diffusion": 5.604, "adsorption": -54.942, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.11144599852948982, "volume": 110.52800000000002, "diffusion": 5.081, "adsorption": -48.81, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.18759518862995575, "volume": 122.60800000000003, "diffusion": 4.685, "adsorption": -50.494, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.23401707219007695, "volume": 127.04800000000003, "diffusion": 5.256, "adsorption": -50.599, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2752887616016788, "volume": 132.40800000000004, "diffusion": 5.325, "adsorption": -50.218, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3596947749955781, "volume": 126.74400000000003, "diffusion": 4.91, "adsorption": -53.504, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.36965738273020043, "volume": 122.11200000000002, "diffusion": 4.748, "adsorption": -53.83, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.5115425053690947, "volume": 122.48000000000003, "diffusion": 5.432, "adsorption": -55.348, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.538707764947606, "volume": 134.67200000000003, "diffusion": 5.306, "adsorption": -54.372, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1391193079999735, "volume": 110.38400000000003, "diffusion": 5.356, "adsorption": -51.209, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.23021894301169313, "volume": 127.30400000000003, "diffusion": 5.116, "adsorption": -49.492, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.2452839294984607, "volume": 122.08800000000002, "diffusion": 4.818, "adsorption": -51.987, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.25114871486183604, "volume": 124.88800000000003, "diffusion": 4.432, "adsorption": -50.359, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.26039080720890995, "volume": 122.89600000000003, "diffusion": 4.522, "adsorption": -49.34, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.28658557329184525, "volume": 133.86400000000003, "diffusion": 4.667, "adsorption": -50.082, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 6}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4654266380875982, "volume": 122.46400000000003, "diffusion": 5.498, "adsorption": -54.356, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 7}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5516952317392387, "volume": 132.30400000000003, "diffusion": 5.558, "adsorption": -54.101, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 8}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.03200000000002, "diffusion": 4.971, "adsorption": -50.23}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.1391193079999735, "volume": 110.38400000000003, "diffusion": 5.356, "adsorption": -51.209, "is_selected": false, "rank": 1}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.4654266380875982, "volume": 122.46400000000003, "diffusion": 5.498, "adsorption": -54.356, "is_selected": false, "rank": 7}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6, "score": 0.23021894301169313, "volume": 127.30400000000003, "diffusion": 5.116, "adsorption": -49.492, "is_selected": false, "rank": 2}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.5516952317392387, "volume": 132.30400000000003, "diffusion": 5.558, "adsorption": -54.101, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 6, "score": 0.2452839294984607, "volume": 122.08800000000002, "diffusion": 4.818, "adsorption": -51.987, "is_selected": false, "rank": 3}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.28658557329184525, "volume": 133.86400000000003, "diffusion": 4.667, "adsorption": -50.082, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 6, "score": 0.25114871486183604, "volume": 124.88800000000003, "diffusion": 4.432, "adsorption": -50.359, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.26039080720890995, "volume": 122.89600000000003, "diffusion": 4.522, "adsorption": -49.34, "is_selected": false, "rank": 5}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.270731214135044, "volume": 119.15200000000003, "diffusion": 5.387, "adsorption": -48.486, "is_selected": false, "rank": 9}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3085017389547287, "volume": 138.94400000000005, "diffusion": 4.86, "adsorption": -49.838, "is_selected": false, "rank": 10}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.31687749008774213, "volume": 126.35200000000003, "diffusion": 5.713, "adsorption": -49.907, "is_selected": false, "rank": 11}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.325693946544662, "volume": 132.66400000000004, "diffusion": 5.344, "adsorption": -49.477, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.3277070082436039, "volume": 133.72800000000004, "diffusion": 4.881, "adsorption": -51.808, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.32894075976225123, "volume": 122.68800000000003, "diffusion": 4.692, "adsorption": -52.872, "is_selected": false, "rank": 14}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.33082470748215836, "volume": 139.26400000000004, "diffusion": 5.256, "adsorption": -50.361, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3527466621279054, "volume": 138.23200000000003, "diffusion": 5.354, "adsorption": -49.905, "is_selected": false, "rank": 16}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.35531997088690986, "volume": 118.85600000000002, "diffusion": 5.76, "adsorption": -48.281, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3568483945454918, "volume": 127.21600000000004, "diffusion": 4.876, "adsorption": -47.19, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.3571072733854518, "volume": 127.67200000000003, "diffusion": 4.629, "adsorption": -52.373, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.35726921310622867, "volume": 129.25600000000003, "diffusion": 4.839, "adsorption": -47.618, "is_selected": false, "rank": 20}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3695331902496629, "volume": 127.52000000000002, "diffusion": 4.012, "adsorption": -50.526, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.40648871348985205, "volume": 126.58400000000003, "diffusion": 5.553, "adsorption": -47.903, "is_selected": false, "rank": 22}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4089605853977681, "volume": 129.56000000000003, "diffusion": 5.75, "adsorption": -51.482, "is_selected": false, "rank": 23}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.4441769236268774, "volume": 128.69600000000003, "diffusion": 4.998, "adsorption": -54.736, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.45601861057855764, "volume": 134.45600000000005, "diffusion": 5.03, "adsorption": -53.95, "is_selected": false, "rank": 25}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4570964441590398, "volume": 138.08000000000004, "diffusion": 5.219, "adsorption": -52.78, "is_selected": false, "rank": 26}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.4638942240852537, "volume": 146.75200000000004, "diffusion": 4.516, "adsorption": -49.583, "is_selected": false, "rank": 27}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4679786181063581, "volume": 133.05600000000004, "diffusion": 5.65, "adsorption": -48.185, "is_selected": false, "rank": 28}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.47511945437306397, "volume": 146.68800000000005, "diffusion": 5.656, "adsorption": -50.16, "is_selected": false, "rank": 29}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4833896891612978, "volume": 150.50400000000005, "diffusion": 5.373, "adsorption": -50.811, "is_selected": false, "rank": 30}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4950563632237167, "volume": 135.70400000000004, "diffusion": 4.098, "adsorption": -48.788, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4954238741391419, "volume": 139.36800000000002, "diffusion": 5.46, "adsorption": -52.414, "is_selected": false, "rank": 32}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5006663682747869, "volume": 145.21600000000004, "diffusion": 5.199, "adsorption": -52.491, "is_selected": false, "rank": 33}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5058267943999978, "volume": 154.90400000000002, "diffusion": 4.938, "adsorption": -48.7, "is_selected": false, "rank": 34}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5058353821116297, "volume": 135.40000000000003, "diffusion": 5.368, "adsorption": -46.958, "is_selected": false, "rank": 35}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5228991917609764, "volume": 138.97600000000003, "diffusion": 5.728, "adsorption": -48.429, "is_selected": false, "rank": 36}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5238626575717059, "volume": 144.86400000000003, "diffusion": 5.685, "adsorption": -51.296, "is_selected": false, "rank": 37}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5299498151195656, "volume": 131.80800000000002, "diffusion": 5.244, "adsorption": -54.87, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5354102074583135, "volume": 128.23200000000003, "diffusion": 5.251, "adsorption": -55.482, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5354764050046928, "volume": 126.35200000000003, "diffusion": 4.81, "adsorption": -56.17, "is_selected": false, "rank": 40}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5542531414747897, "volume": 127.64800000000002, "diffusion": 5.051, "adsorption": -43.9, "is_selected": false, "rank": 41}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.559002034515947, "volume": 123.40000000000003, "diffusion": 4.291, "adsorption": -45.195, "is_selected": false, "rank": 42}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5668106957613191, "volume": 128.72800000000004, "diffusion": 6.406, "adsorption": -52.042, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5671429742887638, "volume": 129.47200000000004, "diffusion": 5.766, "adsorption": -46.37, "is_selected": false, "rank": 44}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5713335422383761, "volume": 128.68000000000004, "diffusion": 5.059, "adsorption": -56.662, "is_selected": false, "rank": 45}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5769079816976171, "volume": 143.82400000000004, "diffusion": 5.735, "adsorption": -48.286, "is_selected": false, "rank": 46}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5890055974985976, "volume": 119.20800000000003, "diffusion": 5.964, "adsorption": -45.109, "is_selected": false, "rank": 47}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5937685542279276, "volume": 129.12000000000003, "diffusion": 4.156, "adsorption": -45.938, "is_selected": false, "rank": 48}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5967778673393653, "volume": 120.09600000000003, "diffusion": 6.165, "adsorption": -45.791, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5967933973095472, "volume": 143.26400000000004, "diffusion": 4.994, "adsorption": -55.088, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.5973769653498262, "volume": 129.64000000000004, "diffusion": 6.387, "adsorption": -47.981, "is_selected": false, "rank": 51}, "[H]N1C([H])(SS(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SS(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6062328712892293, "volume": 154.68800000000005, "diffusion": 5.482, "adsorption": -48.596, "is_selected": false, "rank": 52}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.6119845349814321, "volume": 130.52000000000004, "diffusion": 5.001, "adsorption": -57.258, "is_selected": false, "rank": 53}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6230749149302774, "volume": 131.30400000000003, "diffusion": 5.863, "adsorption": -46.039, "is_selected": false, "rank": 54}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6498344686409177, "volume": 126.85600000000004, "diffusion": 4.283, "adsorption": -44.227, "is_selected": false, "rank": 55}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6499146285949121, "volume": 132.41600000000003, "diffusion": 4.16, "adsorption": -45.486, "is_selected": false, "rank": 56}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6627383157906817, "volume": 143.85600000000002, "diffusion": 4.986, "adsorption": -44.331, "is_selected": false, "rank": 57}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6641753592299215, "volume": 146.84000000000003, "diffusion": 5.263, "adsorption": -45.694, "is_selected": false, "rank": 58}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6763000833919373, "volume": 127.11200000000002, "diffusion": 5.855, "adsorption": -44.483, "is_selected": false, "rank": 59}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6885508904313432, "volume": 138.84800000000004, "diffusion": 6.752, "adsorption": -49.085, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.6909305332321585, "volume": 145.86400000000003, "diffusion": 6.166, "adsorption": -48.139, "is_selected": false, "rank": 61}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.7018826737476701, "volume": 138.17600000000004, "diffusion": 5.542, "adsorption": -44.684, "is_selected": false, "rank": 62}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7105056611026833, "volume": 155.85600000000002, "diffusion": 5.314, "adsorption": -53.998, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7170652477240053, "volume": 146.40800000000004, "diffusion": 5.864, "adsorption": -53.693, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7184657868669229, "volume": 156.07200000000003, "diffusion": 5.842, "adsorption": -48.14, "is_selected": false, "rank": 65}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7273911748388945, "volume": 138.50400000000002, "diffusion": 7.096, "adsorption": -49.541, "is_selected": false, "rank": 66}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7281300831524984, "volume": 134.10400000000004, "diffusion": 5.822, "adsorption": -44.568, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7351022634847926, "volume": 136.22400000000005, "diffusion": 5.415, "adsorption": -43.403, "is_selected": false, "rank": 68}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7398082143419972, "volume": 149.56000000000003, "diffusion": 6.576, "adsorption": -49.264, "is_selected": false, "rank": 69}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.740351794048888, "volume": 154.84000000000003, "diffusion": 5.266, "adsorption": -45.646, "is_selected": false, "rank": 70}, "[H]N1C([H])(SS(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SS(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7420808024957315, "volume": 161.27200000000005, "diffusion": 5.769, "adsorption": -48.29, "is_selected": false, "rank": 71}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.7491730356136437, "volume": 137.35200000000003, "diffusion": 5.901, "adsorption": -44.976, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.7492240702319126, "volume": 142.86400000000003, "diffusion": 6.382, "adsorption": -53.026, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.7529642847968545, "volume": 155.17600000000004, "diffusion": 5.774, "adsorption": -47.197, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)C([H])([H])[H]", "layer": 0, "score": 0.7597167608722948, "volume": 153.71200000000005, "diffusion": 5.817, "adsorption": -47.006, "is_selected": false, "rank": 75}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7707856341071637, "volume": 145.59200000000004, "diffusion": 5.643, "adsorption": -55.461, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.7746487480410358, "volume": 126.58400000000003, "diffusion": 5.722, "adsorption": -58.152, "is_selected": false, "rank": 77}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7765340279508787, "volume": 139.41600000000003, "diffusion": 7.403, "adsorption": -49.891, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7904393064225599, "volume": 154.59200000000004, "diffusion": 6.223, "adsorption": -52.467, "is_selected": false, "rank": 79}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)(=O)C([H])([H])[H]", "layer": 0, "score": 0.802451837020629, "volume": 161.56800000000004, "diffusion": 6.336, "adsorption": -51.226, "is_selected": false, "rank": 80}, "[H]SC1(S(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8065854241042159, "volume": 162.12000000000003, "diffusion": 6.323, "adsorption": -49.205, "is_selected": false, "rank": 81}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8131179390423287, "volume": 149.91200000000003, "diffusion": 5.894, "adsorption": -45.793, "is_selected": false, "rank": 82}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.81536442588976, "volume": 121.59200000000003, "diffusion": 5.139, "adsorption": -61.557, "is_selected": false, "rank": 83}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.8182575299138442, "volume": 140.44800000000004, "diffusion": 4.827, "adsorption": -41.643, "is_selected": false, "rank": 84}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8276637941745864, "volume": 130.67200000000003, "diffusion": 6.169, "adsorption": -43.548, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8296302166175903, "volume": 146.20000000000005, "diffusion": 5.645, "adsorption": -56.347, "is_selected": false, "rank": 86}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.8322503907974157, "volume": 129.52800000000002, "diffusion": 6.5, "adsorption": -44.412, "is_selected": false, "rank": 87}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.8421150822578232, "volume": 135.57600000000002, "diffusion": 5.712, "adsorption": -57.947, "is_selected": false, "rank": 88}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.8429388984481926, "volume": 118.71200000000003, "diffusion": 7.374, "adsorption": -45.531, "is_selected": false, "rank": 89}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8432993972098185, "volume": 154.42400000000004, "diffusion": 5.562, "adsorption": -44.856, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8552615925625766, "volume": 154.23200000000003, "diffusion": 6.389, "adsorption": -53.048, "is_selected": false, "rank": 91}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.8574632737448717, "volume": 145.84800000000004, "diffusion": 5.394, "adsorption": -57.712, "is_selected": false, "rank": 92}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8646138067946042, "volume": 138.29600000000002, "diffusion": 5.391, "adsorption": -41.469, "is_selected": false, "rank": 93}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8716607714251746, "volume": 133.60800000000003, "diffusion": 6.185, "adsorption": -43.312, "is_selected": false, "rank": 94}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8885659405055906, "volume": 128.84800000000004, "diffusion": 5.947, "adsorption": -58.957, "is_selected": false, "rank": 95}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.9020000850461187, "volume": 141.69600000000003, "diffusion": 6.737, "adsorption": -45.894, "is_selected": false, "rank": 96}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9094578420458118, "volume": 155.94400000000005, "diffusion": 4.846, "adsorption": -42.41, "is_selected": false, "rank": 97}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9237356461660391, "volume": 137.09600000000003, "diffusion": 5.52, "adsorption": -40.731, "is_selected": false, "rank": 98}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9344853301122626, "volume": 129.68000000000004, "diffusion": 7.015, "adsorption": -44.458, "is_selected": false, "rank": 99}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.9554464409200372, "volume": 151.95200000000003, "diffusion": 5.415, "adsorption": -42.107, "is_selected": false, "rank": 100}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9728340644245961, "volume": 128.24800000000002, "diffusion": 5.858, "adsorption": -39.701, "is_selected": false, "rank": 101}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0070610879020836, "volume": 139.90400000000002, "diffusion": 5.927, "adsorption": -41.134, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 1.0080119801148277, "volume": 136.25600000000003, "diffusion": 6.033, "adsorption": -40.92, "is_selected": false, "rank": 103}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.0192557162413765, "volume": 152.10400000000004, "diffusion": 4.95, "adsorption": -39.637, "is_selected": false, "rank": 104}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 1.025178669684852, "volume": 146.72800000000004, "diffusion": 6.853, "adsorption": -44.988, "is_selected": false, "rank": 105}, "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 1.0374195647999036, "volume": 161.37600000000003, "diffusion": 5.897, "adsorption": -56.668, "is_selected": false, "rank": 106}, "[H]SC1(S(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0401237302876334, "volume": 154.84000000000003, "diffusion": 6.361, "adsorption": -44.315, "is_selected": false, "rank": 107}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0582535404661058, "volume": 134.90400000000002, "diffusion": 6.538, "adsorption": -41.574, "is_selected": false, "rank": 108}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 1.0695630967722436, "volume": 162.21600000000004, "diffusion": 6.376, "adsorption": -55.465, "is_selected": false, "rank": 109}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0788673204364954, "volume": 141.83200000000002, "diffusion": 6.402, "adsorption": -41.825, "is_selected": false, "rank": 110}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.187705819699061, "volume": 147.56000000000003, "diffusion": 5.6, "adsorption": -38.173, "is_selected": false, "rank": 111}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2172691299263403, "volume": 122.99200000000003, "diffusion": 5.957, "adsorption": -65.318, "is_selected": false, "rank": 112}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2338525305036359, "volume": 136.46400000000003, "diffusion": 5.364, "adsorption": -34.917, "is_selected": false, "rank": 113}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.2624213567871052, "volume": 155.88800000000003, "diffusion": 6.338, "adsorption": -40.675, "is_selected": false, "rank": 114}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2768778144107822, "volume": 146.06400000000002, "diffusion": 6.824, "adsorption": -40.575, "is_selected": false, "rank": 115}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.289643171738926, "volume": 132.42400000000004, "diffusion": 5.915, "adsorption": -35.224, "is_selected": false, "rank": 116}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.3670507308607664, "volume": 157.32000000000005, "diffusion": 5.409, "adsorption": -36.012, "is_selected": false, "rank": 117}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.4201628129873138, "volume": 156.14400000000003, "diffusion": 6.075, "adsorption": -37.187, "is_selected": false, "rank": 118}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.4647289912386878, "volume": 145.75200000000004, "diffusion": 6.128, "adsorption": -35.038, "is_selected": false, "rank": 119}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SS(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SS(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1S(=O)(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S(=O)(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S(=O)(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770945759.4615636}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 5039b8dd-9e3c-4b31-a74d-eaa740b20965", "timestamp": 1770945759.475379}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/5039b8dd-9e3c-4b31-a74d-eaa740b20965.log", "timestamp": 1770945759.4911718}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945759.7105072}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770945759.732065}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770945759.750991}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945759.7687087}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770945759.785096}, {"type": "log", "level": "info", "message": "远程日志已启动: 5039b8dd-9e3c-4b31-a74d-eaa740b20965", "timestamp": 1770945759.7890034}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770945759.8280356}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770945759.84781}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 8, 最终保留数: 15", "timestamp": 1770945759.8699057}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945759.889784}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945759.9214244}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770945759.9422712}, {"type": "log", "level": "info", "message": "配置: {'top_num': 8, 'top_num_last': 15}", "timestamp": 1770945759.9622679}, {"type": "log", "level": "info", "message": "远程日志已启动: 5039b8dd-9e3c-4b31-a74d-eaa740b20965", "timestamp": 1770945759.9810348}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770945759.984191}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770945760.037191}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770945760.054666}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945760.0708032}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945760.0868654}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770945760.101876}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945760.1188357}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770945760.137265}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.02 秒", "timestamp": 1770945760.171653}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770945760.1876204}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.02s, 速度: 9520.94 mol/s", "timestamp": 1770945760.2032633}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.018065452575683594, "speed": 9520.935015573035, "timestamp": 1770945760.2199123}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 172 个分子 (过滤掉 0 个)", "timestamp": 1770945760.2518141}, {"type": "log", "level": "info", "message": "筛选: 剩余 172 个分子 （移除 0 个）", "timestamp": 1770945760.2683961}, {"type": "filtering", "layer": 0, "filtered_count": 172, "removed": 0, "timestamp": 1770945760.2857196}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 172 个分子的性质...", "timestamp": 1770945760.304401}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 119 个分子的性质预测", "timestamp": 1770945770.613035}, {"type": "log", "level": "info", "message": "评分: 完成 119 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1205", "timestamp": 1770945770.6361575}, {"type": "scoring", "layer": 0, "scored_count": 119, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12051651456583529, "timestamp": 1770945770.6550038}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945770.675644}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945770.6960046}, {"type": "selection", "layer": 0, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]"], "timestamp": 1770945770.7157028}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945770.7360024}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1205", "timestamp": 1770945770.7597876}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.23", "timestamp": 1770945770.7849708}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770945770.8054473}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770945770.8224602}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.71s) ==========", "timestamp": 1770945770.8467405}, {"type": "layer_end", "layer": 0, "duration": 10.709161520004272, "timestamp": 1770945770.8644354}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945770.8833976}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945770.899815}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770945770.9203126}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945770.9379392}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770945770.9561367}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 48 个分子，去重后 48 个，耗时 0.00 秒", "timestamp": 1770945770.9813466}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 48 个新分子", "timestamp": 1770945771.000248}, {"type": "log", "level": "info", "message": "生成: 48 个分子, 去重后 48 个, 耗时 0.00s, 速度: 21277.38 mol/s", "timestamp": 1770945771.019095}, {"type": "generation", "layer": 1, "total_generated": 48, "unique_mols": 48, "elapsed": 0.0022559165954589844, "speed": 21277.38237159163, "timestamp": 1770945771.039831}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 48 个分子 (过滤掉 0 个)", "timestamp": 1770945771.0626657}, {"type": "log", "level": "info", "message": "筛选: 剩余 48 个分子 （移除 0 个）", "timestamp": 1770945771.087113}, {"type": "filtering", "layer": 1, "filtered_count": 48, "removed": 0, "timestamp": 1770945771.1167262}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 48 个分子的性质...", "timestamp": 1770945771.1356053}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945774.0491333}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0668", "timestamp": 1770945774.083397}, {"type": "scoring", "layer": 1, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.06679538340634371, "timestamp": 1770945774.1065052}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945774.1285052}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945774.1486635}, {"type": "selection", "layer": 1, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]"], "timestamp": 1770945774.1700716}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945774.1902554}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0668", "timestamp": 1770945774.2079613}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.30", "timestamp": 1770945774.2304437}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9460", "timestamp": 1770945774.2529676}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.2360", "timestamp": 1770945774.2729313}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 3.34s) ==========", "timestamp": 1770945774.2982614}, {"type": "layer_end", "layer": 1, "duration": 3.341625690460205, "timestamp": 1770945774.3178558}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945774.3405175}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945774.3606303}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770945774.3827648}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945774.4022195}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770945774.4215422}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 40 个分子，去重后 40 个，耗时 0.00 秒", "timestamp": 1770945774.4540393}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 40 个新分子", "timestamp": 1770945774.4818585}, {"type": "log", "level": "info", "message": "生成: 40 个分子, 去重后 40 个, 耗时 0.00s, 速度: 12564.38 mol/s", "timestamp": 1770945774.503622}, {"type": "generation", "layer": 2, "total_generated": 40, "unique_mols": 40, "elapsed": 0.003183603286743164, "speed": 12564.379540178237, "timestamp": 1770945774.5289974}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 40 个分子 (过滤掉 0 个)", "timestamp": 1770945774.5545254}, {"type": "log", "level": "info", "message": "筛选: 剩余 40 个分子 （移除 0 个）", "timestamp": 1770945774.5783834}, {"type": "filtering", "layer": 2, "filtered_count": 40, "removed": 0, "timestamp": 1770945774.5995514}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 40 个分子的性质...", "timestamp": 1770945774.6215625}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945777.11943}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1413", "timestamp": 1770945777.1520236}, {"type": "scoring", "layer": 2, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1412502310465949, "timestamp": 1770945777.1797965}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945777.2037115}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945777.2241814}, {"type": "selection", "layer": 2, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770945777.2447703}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945777.266763}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1413", "timestamp": 1770945777.2932715}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.50", "timestamp": 1770945777.321516}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.3360", "timestamp": 1770945777.3492298}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.2950", "timestamp": 1770945777.3724716}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.98s) ==========", "timestamp": 1770945777.3982096}, {"type": "layer_end", "layer": 2, "duration": 2.976130723953247, "timestamp": 1770945777.4196012}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945777.439015}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945777.4613616}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770945777.4804194}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945777.5019248}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770945777.5217035}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 32 个分子，去重后 32 个，耗时 0.00 秒", "timestamp": 1770945777.5444772}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 32 个新分子", "timestamp": 1770945777.5663862}, {"type": "log", "level": "info", "message": "生成: 32 个分子, 去重后 32 个, 耗时 0.00s, 速度: 24685.99 mol/s", "timestamp": 1770945777.586036}, {"type": "generation", "layer": 3, "total_generated": 32, "unique_mols": 32, "elapsed": 0.0012962818145751953, "speed": 24685.990068052233, "timestamp": 1770945777.6070583}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 32 个分子 (过滤掉 0 个)", "timestamp": 1770945777.626979}, {"type": "log", "level": "info", "message": "筛选: 剩余 32 个分子 （移除 0 个）", "timestamp": 1770945777.6475098}, {"type": "filtering", "layer": 3, "filtered_count": 32, "removed": 0, "timestamp": 1770945777.6689742}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 32 个分子的性质...", "timestamp": 1770945777.6864524}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945779.708777}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0109", "timestamp": 1770945779.7323637}, {"type": "scoring", "layer": 3, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.010927204527003654, "timestamp": 1770945779.7560697}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945779.7745988}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945779.7968981}, {"type": "selection", "layer": 3, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770945779.818638}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945779.8382032}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0109", "timestamp": 1770945779.8577569}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.48", "timestamp": 1770945779.8795125}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9580", "timestamp": 1770945779.9038458}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.1590", "timestamp": 1770945779.9267504}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.43s) ==========", "timestamp": 1770945779.9522064}, {"type": "layer_end", "layer": 3, "duration": 2.429908275604248, "timestamp": 1770945779.9750464}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945779.9964619}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945780.022272}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770945780.0475667}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945780.0679643}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770945780.0895202}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 24 个分子，去重后 24 个，耗时 0.00 秒", "timestamp": 1770945780.1125352}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 24 个新分子", "timestamp": 1770945780.1377757}, {"type": "log", "level": "info", "message": "生成: 24 个分子, 去重后 24 个, 耗时 0.00s, 速度: 19463.13 mol/s", "timestamp": 1770945780.159925}, {"type": "generation", "layer": 4, "total_generated": 24, "unique_mols": 24, "elapsed": 0.0012331008911132812, "speed": 19463.127610208816, "timestamp": 1770945780.1806304}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 24 个分子 (过滤掉 0 个)", "timestamp": 1770945780.205577}, {"type": "log", "level": "info", "message": "筛选: 剩余 24 个分子 （移除 0 个）", "timestamp": 1770945780.232929}, {"type": "filtering", "layer": 4, "filtered_count": 24, "removed": 0, "timestamp": 1770945780.258027}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 24 个分子的性质...", "timestamp": 1770945780.2794056}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945781.8811798}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1251", "timestamp": 1770945781.9124238}, {"type": "scoring", "layer": 4, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12509700024272935, "timestamp": 1770945781.9327338}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945781.9565217}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945781.979548}, {"type": "selection", "layer": 4, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770945782.0020492}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945782.0229902}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1251", "timestamp": 1770945782.0432484}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.74", "timestamp": 1770945782.064098}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770945782.085271}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770945782.1091852}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 2.05s) ==========", "timestamp": 1770945782.138446}, {"type": "layer_end", "layer": 4, "duration": 2.0482349395751953, "timestamp": 1770945782.1592407}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945782.1763234}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945782.1973145}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770945782.2215483}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945782.2438748}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770945782.2660146}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 16 个分子，去重后 16 个，耗时 0.00 秒", "timestamp": 1770945782.2922208}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 16 个新分子", "timestamp": 1770945782.3245306}, {"type": "log", "level": "info", "message": "生成: 16 个分子, 去重后 16 个, 耗时 0.00s, 速度: 12804.59 mol/s", "timestamp": 1770945782.3493993}, {"type": "generation", "layer": 5, "total_generated": 16, "unique_mols": 16, "elapsed": 0.001249551773071289, "speed": 12804.59149017363, "timestamp": 1770945782.3746936}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 16 个分子 (过滤掉 0 个)", "timestamp": 1770945782.39885}, {"type": "log", "level": "info", "message": "筛选: 剩余 16 个分子 （移除 0 个）", "timestamp": 1770945782.4268138}, {"type": "filtering", "layer": 5, "filtered_count": 16, "removed": 0, "timestamp": 1770945782.4499094}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 16 个分子的性质...", "timestamp": 1770945782.472544}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945783.5063725}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1114", "timestamp": 1770945783.5369081}, {"type": "scoring", "layer": 5, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.11144599852948982, "timestamp": 1770945783.5586026}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945783.5797207}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945783.6003873}, {"type": "selection", "layer": 5, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]"], "timestamp": 1770945783.6229582}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945783.642559}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1114", "timestamp": 1770945783.6722598}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.53", "timestamp": 1770945783.6986394}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0810", "timestamp": 1770945783.722489}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.8100", "timestamp": 1770945783.7473059}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.51s) ==========", "timestamp": 1770945783.7735736}, {"type": "layer_end", "layer": 5, "duration": 1.5067856311798096, "timestamp": 1770945783.7942307}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945783.8123157}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945783.8344903}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770945783.8577685}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945783.877217}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770945783.8994193}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 8 个分子，去重后 8 个，耗时 0.00 秒", "timestamp": 1770945783.9230113}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 8 个新分子", "timestamp": 1770945783.9432201}, {"type": "log", "level": "info", "message": "生成: 8 个分子, 去重后 8 个, 耗时 0.00s, 速度: 24654.25 mol/s", "timestamp": 1770945783.9781103}, {"type": "generation", "layer": 6, "total_generated": 8, "unique_mols": 8, "elapsed": 0.00032448768615722656, "speed": 24654.24834680382, "timestamp": 1770945784.0006824}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 8 个分子 (过滤掉 0 个)", "timestamp": 1770945784.0205963}, {"type": "log", "level": "info", "message": "筛选: 剩余 8 个分子 （移除 0 个）", "timestamp": 1770945784.040428}, {"type": "filtering", "layer": 6, "filtered_count": 8, "removed": 0, "timestamp": 1770945784.0635283}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 8 个分子的性质...", "timestamp": 1770945784.0859861}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 8 个分子的性质预测", "timestamp": 1770945784.6517808}, {"type": "log", "level": "info", "message": "评分: 完成 8 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1391", "timestamp": 1770945784.6741772}, {"type": "scoring", "layer": 6, "scored_count": 8, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1391193079999735, "timestamp": 1770945784.693907}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 8 个候选分子进入下一层", "timestamp": 1770945784.7139716}, {"type": "log", "level": "info", "message": "选择: Top 8 个候选分子", "timestamp": 1770945784.7342126}, {"type": "selection", "layer": 6, "top_n": 8, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770945784.7557135}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945784.7744813}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1391", "timestamp": 1770945784.7937908}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.38", "timestamp": 1770945784.8137772}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.3560", "timestamp": 1770945784.833049}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.2090", "timestamp": 1770945784.8571484}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 0.98s) ==========", "timestamp": 1770945784.8833163}, {"type": "layer_end", "layer": 6, "duration": 0.983483076095581, "timestamp": 1770945784.9055355}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945785.5330691}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945785.5598388}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770945785.5824282}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 24.86 秒", "timestamp": 1770945785.6043742}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 8 个分子", "timestamp": 1770945785.6274164}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945785.6481934}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770945785.666785}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945785.6881719}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.0036, 体积: 110.42, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770945785.708197}, {"type": "log", "level": "info", "message": "INFO -   2. [H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770945785.7275794}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2670, 体积: 132.20, 扩散: 4.9760, 吸附: -51.3100", "timestamp": 1770945785.7526712}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770945785.7797415}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2969, 体积: 126.59, 扩散: 5.0410, 吸附: -48.0140", "timestamp": 1770945785.80225}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945785.8239303}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3290, 体积: 121.40, 扩散: 4.5690, 吸附: -52.6550", "timestamp": 1770945785.843516}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "timestamp": 1770945785.8658834}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3446, 体积: 126.95, 扩散: 4.9710, 吸附: -53.4250", "timestamp": 1770945785.886923}, {"type": "log", "level": "success", "message": "生成完成！共 8 个结果，总耗时 26.44s", "timestamp": 1770945785.9053893}, {"type": "finish", "total_results": 8, "duration": 26.444079160690308, "timestamp": 1770945785.9302535}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/5039b8dd-9e3c-4b31-a74d-eaa740b20965.json", "timestamp": 1770945785.9622614}, {"type": "log", "level": "info", "message": "远程日志已上传: 5039b8dd-9e3c-4b31-a74d-eaa740b20965", "timestamp": 1770945786.017843}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770945786.0476496}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770945786.0725977}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945786.0982203}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770945786.1224113}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770945786.1450531}, {"type": "log", "level": "success", "message": "生成完成！共 8 个结果，总耗时 26.71s", "timestamp": 1770945786.1727827}, {"type": "finish", "total_results": 8, "duration": 26.711411952972412, "timestamp": 1770945786.194731}]}