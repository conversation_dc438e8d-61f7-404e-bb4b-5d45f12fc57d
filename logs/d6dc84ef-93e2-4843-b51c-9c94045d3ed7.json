{"metadata": {"log_name": "d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "start_time": 1770892407.5790968, "end_time": 1770892444.480155, "duration": 36.901058197021484, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.39200000000002, "diffusion": 4.783, "adsorption": -51.019}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770892409.2318826, "end_time": 1770892420.5406375, "duration": 11.308754920959473, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.11788262409207326}, {"layer_num": 1, "start_time": 1770892420.926295, "end_time": 1770892424.2166553, "duration": 3.29036021232605, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "best_score": 0.18335381131544046}, {"layer_num": 2, "start_time": 1770892424.5687451, "end_time": 1770892427.2346404, "duration": 2.6658952236175537, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1868578223223941}, {"layer_num": 3, "start_time": 1770892427.608161, "end_time": 1770892429.8592386, "duration": 2.251077651977539, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.16811182861801158}, {"layer_num": 4, "start_time": 1770892431.0251467, "end_time": 1770892434.2064505, "duration": 3.1813037395477295, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.16134703337242445}, {"layer_num": 5, "start_time": 1770892434.8803525, "end_time": 1770892437.5449355, "duration": 2.6645829677581787, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.19639556791686008}, {"layer_num": 6, "start_time": 1770892438.2487197, "end_time": 1770892440.657944, "duration": 2.409224271774292, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08736714290989306}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.11788262409207326, "volume": 110.62400000000002, "diffusion": 5.242, "adsorption": -51.356, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.16524979361091285, "volume": 122.98400000000002, "diffusion": 4.791, "adsorption": -51.861, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.16926372065018672, "volume": 122.75200000000002, "diffusion": 4.908, "adsorption": -51.549, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.17000393244504325, "volume": 126.69600000000003, "diffusion": 4.872, "adsorption": -51.082, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1768145840078258, "volume": 121.16000000000003, "diffusion": 4.664, "adsorption": -50.094, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.26204304052165767, "volume": 126.97600000000003, "diffusion": 4.629, "adsorption": -52.373, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.28276929700510467, "volume": 129.61600000000004, "diffusion": 4.75, "adsorption": -49.289, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.3005626101345046, "volume": 139.40000000000003, "diffusion": 4.708, "adsorption": -50.643, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.30414016874933547, "volume": 137.09600000000003, "diffusion": 5.025, "adsorption": -51.217, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.316712878514073, "volume": 134.77600000000004, "diffusion": 4.591, "adsorption": -50.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.33166973782203424, "volume": 131.40000000000003, "diffusion": 5.118, "adsorption": -49.806, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3409519921264283, "volume": 135.64000000000004, "diffusion": 4.391, "adsorption": -50.504, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3427530593181829, "volume": 127.33600000000003, "diffusion": 4.159, "adsorption": -50.019, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.3562933337496488, "volume": 127.20800000000003, "diffusion": 4.948, "adsorption": -48.137, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3593523586396048, "volume": 128.61600000000004, "diffusion": 5.103, "adsorption": -53.185, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3623150679343872, "volume": 131.88000000000002, "diffusion": 4.969, "adsorption": -48.829, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3645204958192295, "volume": 126.88000000000002, "diffusion": 4.911, "adsorption": -54.223, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.37720161978772443, "volume": 132.96000000000004, "diffusion": 5.379, "adsorption": -50.2, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.39483394140380446, "volume": 118.48800000000003, "diffusion": 5.286, "adsorption": -47.34, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.4075311767336152, "volume": 138.80800000000002, "diffusion": 5.278, "adsorption": -51.812, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.410091413793251, "volume": 129.73600000000002, "diffusion": 5.595, "adsorption": -49.912, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.42404976950410234, "volume": 127.16800000000003, "diffusion": 5.217, "adsorption": -47.935, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.43922049610176983, "volume": 126.30400000000003, "diffusion": 4.779, "adsorption": -56.023, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.44781257252944784, "volume": 119.32800000000003, "diffusion": 5.773, "adsorption": -48.3, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.4756504243021313, "volume": 128.98400000000004, "diffusion": 5.132, "adsorption": -55.003, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.476465693079414, "volume": 123.65600000000003, "diffusion": 4.521, "adsorption": -45.891, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4787775424011269, "volume": 139.48000000000005, "diffusion": 5.451, "adsorption": -49.733, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4795226556859535, "volume": 132.44000000000003, "diffusion": 4.552, "adsorption": -47.082, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4972351311769294, "volume": 129.02400000000003, "diffusion": 6.237, "adsorption": -51.435, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5027270791334814, "volume": 131.00800000000004, "diffusion": 5.243, "adsorption": -54.757, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5054540913984117, "volume": 133.30400000000003, "diffusion": 5.318, "adsorption": -47.855, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5221705394806058, "volume": 126.03200000000002, "diffusion": 5.787, "adsorption": -48.118, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.5255855299008307, "volume": 142.64000000000004, "diffusion": 5.419, "adsorption": -52.728, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5284663760026225, "volume": 126.52000000000002, "diffusion": 5.866, "adsorption": -48.367, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5295530310133489, "volume": 134.22400000000002, "diffusion": 5.05, "adsorption": -55.404, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.5355670240029424, "volume": 125.89600000000003, "diffusion": 5.729, "adsorption": -47.663, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5362435219379682, "volume": 119.84800000000003, "diffusion": 5.787, "adsorption": -46.926, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.5419851978918391, "volume": 137.94400000000005, "diffusion": 5.533, "adsorption": -53.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.5461474382396083, "volume": 147.01600000000005, "diffusion": 4.722, "adsorption": -47.59, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5507209861936833, "volume": 150.75200000000004, "diffusion": 5.325, "adsorption": -49.798, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.5524313013657681, "volume": 128.62400000000002, "diffusion": 5.064, "adsorption": -56.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.563996925109012, "volume": 129.24800000000002, "diffusion": 5.218, "adsorption": -45.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.576417573411116, "volume": 127.60000000000004, "diffusion": 5.202, "adsorption": -45.357, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5809655455518111, "volume": 119.40800000000003, "diffusion": 6.135, "adsorption": -47.335, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.5902202087625523, "volume": 122.95200000000003, "diffusion": 5.099, "adsorption": -57.998, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5910969431133086, "volume": 138.20000000000005, "diffusion": 5.322, "adsorption": -47.167, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5939431117395085, "volume": 128.76000000000002, "diffusion": 5.595, "adsorption": -46.635, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6103629611462087, "volume": 119.09600000000003, "diffusion": 6.441, "adsorption": -47.875, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.6120158183622566, "volume": 131.65600000000003, "diffusion": 5.068, "adsorption": -57.138, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6189975807864856, "volume": 147.12000000000003, "diffusion": 4.58, "adsorption": -55.166, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.6356113914875658, "volume": 135.68800000000005, "diffusion": 5.808, "adsorption": -47.751, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.6430399206473463, "volume": 155.81600000000003, "diffusion": 4.81, "adsorption": -47.177, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6541385767663913, "volume": 145.16000000000003, "diffusion": 5.41, "adsorption": -47.48, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.6548338336487654, "volume": 121.00000000000003, "diffusion": 5.046, "adsorption": -59.586, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.6554493406374352, "volume": 133.45600000000005, "diffusion": 5.686, "adsorption": -55.402, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.661071620059131, "volume": 142.64000000000004, "diffusion": 5.871, "adsorption": -53.425, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.6638347909253353, "volume": 146.55200000000002, "diffusion": 5.814, "adsorption": -53.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6667383200807273, "volume": 145.54400000000004, "diffusion": 5.736, "adsorption": -53.554, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.681299730591901, "volume": 146.47200000000004, "diffusion": 6.054, "adsorption": -49.51, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.682154178472302, "volume": 155.52800000000005, "diffusion": 4.901, "adsorption": -55.247, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6970081127389529, "volume": 143.72800000000004, "diffusion": 5.08, "adsorption": -45.357, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7103088246860088, "volume": 153.88800000000003, "diffusion": 5.395, "adsorption": -47.816, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7186329477792581, "volume": 147.19200000000004, "diffusion": 5.289, "adsorption": -46.266, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.7276158477187284, "volume": 134.38400000000004, "diffusion": 5.35, "adsorption": -57.681, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.736182797877923, "volume": 142.91200000000003, "diffusion": 6.671, "adsorption": -50.222, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7393558456502346, "volume": 140.36000000000004, "diffusion": 6.529, "adsorption": -49.27, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.7522116329452331, "volume": 138.96000000000004, "diffusion": 5.329, "adsorption": -44.569, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7536216891581685, "volume": 139.35200000000003, "diffusion": 6.842, "adsorption": -49.985, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7592002391322336, "volume": 136.75200000000004, "diffusion": 5.41, "adsorption": -44.398, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7649345574349167, "volume": 130.88800000000003, "diffusion": 5.57, "adsorption": -43.966, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7763565163695019, "volume": 133.96000000000004, "diffusion": 5.21, "adsorption": -42.965, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.7771804805963343, "volume": 147.09600000000003, "diffusion": 6.305, "adsorption": -48.868, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7887575888021356, "volume": 138.06400000000002, "diffusion": 4.88, "adsorption": -42.213, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7995606748923587, "volume": 145.49600000000004, "diffusion": 5.461, "adsorption": -56.798, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8005498316680595, "volume": 134.73600000000002, "diffusion": 5.415, "adsorption": -43.402, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.8031081609610269, "volume": 145.74400000000003, "diffusion": 6.03, "adsorption": -47.241, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.8188369403934992, "volume": 145.87200000000004, "diffusion": 5.693, "adsorption": -56.243, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.8373235713077294, "volume": 140.18400000000003, "diffusion": 4.626, "adsorption": -41.927, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.8652779786865472, "volume": 128.81600000000003, "diffusion": 5.87, "adsorption": -59.031, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8669937848382703, "volume": 128.52000000000004, "diffusion": 5.947, "adsorption": -58.832, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.8676885737811094, "volume": 155.45600000000005, "diffusion": 5.69, "adsorption": -46.43, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.8701735962640182, "volume": 129.26400000000004, "diffusion": 6.648, "adsorption": -45.759, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.8749817030507169, "volume": 138.93600000000004, "diffusion": 5.78, "adsorption": -44.081, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.880153679419529, "volume": 150.47200000000004, "diffusion": 6.262, "adsorption": -47.484, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.8871574172246599, "volume": 155.44000000000003, "diffusion": 5.817, "adsorption": -46.548, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8914705200725683, "volume": 148.66400000000004, "diffusion": 5.731, "adsorption": -45.125, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9074899514845602, "volume": 130.18400000000003, "diffusion": 6.466, "adsorption": -44.619, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.924354445469055, "volume": 138.37600000000003, "diffusion": 7.101, "adsorption": -47.852, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.9371202224433022, "volume": 151.11200000000002, "diffusion": 5.195, "adsorption": -42.82, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9371683263638113, "volume": 133.97600000000003, "diffusion": 6.116, "adsorption": -43.454, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9453607666001116, "volume": 156.88800000000003, "diffusion": 4.716, "adsorption": -42.343, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.954477785910468, "volume": 139.56000000000003, "diffusion": 7.045, "adsorption": -47.323, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9652481004537882, "volume": 142.40800000000004, "diffusion": 5.892, "adsorption": -43.479, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.9671024216853719, "volume": 155.62400000000002, "diffusion": 5.956, "adsorption": -45.711, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.9769580215733817, "volume": 130.40800000000004, "diffusion": 6.473, "adsorption": -43.497, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 1.0087907227667632, "volume": 146.64000000000004, "diffusion": 5.38, "adsorption": -60.468, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0237905035442316, "volume": 133.39200000000002, "diffusion": 5.344, "adsorption": -39.146, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0432176232700021, "volume": 128.11200000000002, "diffusion": 5.844, "adsorption": -39.78, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.050778828200169, "volume": 151.04800000000003, "diffusion": 5.101, "adsorption": -40.543, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 1.0889168149774893, "volume": 135.21600000000004, "diffusion": 5.759, "adsorption": -39.795, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1095023772768542, "volume": 135.01600000000002, "diffusion": 6.054, "adsorption": -40.463, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.2027367536704656, "volume": 123.28000000000003, "diffusion": 5.947, "adsorption": -65.349, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2150296853264109, "volume": 145.85600000000002, "diffusion": 6.921, "adsorption": -43.421, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2932171081334745, "volume": 135.71200000000005, "diffusion": 5.246, "adsorption": -34.573, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.306432076040762, "volume": 154.72800000000004, "diffusion": 6.294, "adsorption": -41.004, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.356676744867048, "volume": 146.40000000000003, "diffusion": 6.086, "adsorption": -38.127, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.3751093275156503, "volume": 147.20800000000003, "diffusion": 5.87, "adsorption": -37.17, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.4337814070483679, "volume": 156.80800000000005, "diffusion": 5.409, "adsorption": -36.012, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.4774990479461114, "volume": 154.80800000000005, "diffusion": 6.67, "adsorption": -39.444, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.18335381131544046, "volume": 122.54400000000003, "diffusion": 4.794, "adsorption": -52.226, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1875349096989784, "volume": 110.26400000000002, "diffusion": 5.072, "adsorption": -48.877, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24112410915064914, "volume": 122.64000000000003, "diffusion": 4.553, "adsorption": -49.623, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4893316560441669, "volume": 122.44800000000004, "diffusion": 5.529, "adsorption": -54.831, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6540753865405122, "volume": 126.64800000000002, "diffusion": 4.331, "adsorption": -44.007, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1868578223223941, "volume": 110.54400000000003, "diffusion": 5.509, "adsorption": -51.592, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2314222656085954, "volume": 122.38400000000003, "diffusion": 5.039, "adsorption": -52.197, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23763965831149708, "volume": 126.18400000000003, "diffusion": 4.883, "adsorption": -52.272, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2899595593864762, "volume": 121.56000000000003, "diffusion": 4.492, "adsorption": -53.195, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.4347191877286676, "volume": 122.29600000000003, "diffusion": 5.172, "adsorption": -55.195, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.16811182861801158, "volume": 122.46400000000003, "diffusion": 4.922, "adsorption": -51.524, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.21174629633107317, "volume": 110.38400000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.24036509908864218, "volume": 122.80000000000003, "diffusion": 4.599, "adsorption": -52.541, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.271117218087555, "volume": 123.04800000000003, "diffusion": 4.864, "adsorption": -53.392, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2855070492991266, "volume": 126.48000000000003, "diffusion": 4.495, "adsorption": -49.666, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.16134703337242445, "volume": 122.95200000000003, "diffusion": 4.9, "adsorption": -51.412, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1894427910047347, "volume": 110.68800000000003, "diffusion": 5.478, "adsorption": -51.724, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23946686049909843, "volume": 126.41600000000003, "diffusion": 4.889, "adsorption": -52.246, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24600620872904136, "volume": 122.40000000000003, "diffusion": 4.571, "adsorption": -52.599, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.4927639464110003, "volume": 122.39200000000002, "diffusion": 5.43, "adsorption": -55.25, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.19639556791686008, "volume": 110.52800000000002, "diffusion": 5.521, "adsorption": -51.714, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24515109492321957, "volume": 122.06400000000004, "diffusion": 4.675, "adsorption": -49.032, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2773266783174993, "volume": 121.95200000000003, "diffusion": 4.748, "adsorption": -53.83, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.4826850483233718, "volume": 122.77600000000002, "diffusion": 5.159, "adsorption": -55.983, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6498350196677829, "volume": 126.47200000000004, "diffusion": 4.313, "adsorption": -44.116, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.08736714290989306, "volume": 110.57600000000002, "diffusion": 4.971, "adsorption": -50.23, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.1488073367123677, "volume": 122.84000000000003, "diffusion": 4.891, "adsorption": -51.248, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.18190060020520937, "volume": 122.76800000000003, "diffusion": 4.808, "adsorption": -52.117, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2242757440671174, "volume": 122.57600000000004, "diffusion": 4.673, "adsorption": -49.473, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24095956581165967, "volume": 126.54400000000003, "diffusion": 4.883, "adsorption": -52.273, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.39200000000002, "diffusion": 4.783, "adsorption": -51.019}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.08736714290989306, "volume": 110.57600000000002, "diffusion": 4.971, "adsorption": -50.23, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 6, "score": 0.18190060020520937, "volume": 122.76800000000003, "diffusion": 4.808, "adsorption": -52.117, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.1488073367123677, "volume": 122.84000000000003, "diffusion": 4.891, "adsorption": -51.248, "is_selected": false, "rank": 2}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.24095956581165967, "volume": 126.54400000000003, "diffusion": 4.883, "adsorption": -52.273, "is_selected": false, "rank": 5}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.2242757440671174, "volume": 122.57600000000004, "diffusion": 4.673, "adsorption": -49.473, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.26204304052165767, "volume": 126.97600000000003, "diffusion": 4.629, "adsorption": -52.373, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.28276929700510467, "volume": 129.61600000000004, "diffusion": 4.75, "adsorption": -49.289, "is_selected": false, "rank": 7}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3005626101345046, "volume": 139.40000000000003, "diffusion": 4.708, "adsorption": -50.643, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.30414016874933547, "volume": 137.09600000000003, "diffusion": 5.025, "adsorption": -51.217, "is_selected": false, "rank": 9}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.316712878514073, "volume": 134.77600000000004, "diffusion": 4.591, "adsorption": -50.072, "is_selected": false, "rank": 10}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.33166973782203424, "volume": 131.40000000000003, "diffusion": 5.118, "adsorption": -49.806, "is_selected": false, "rank": 11}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3409519921264283, "volume": 135.64000000000004, "diffusion": 4.391, "adsorption": -50.504, "is_selected": false, "rank": 12}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3427530593181829, "volume": 127.33600000000003, "diffusion": 4.159, "adsorption": -50.019, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3562933337496488, "volume": 127.20800000000003, "diffusion": 4.948, "adsorption": -48.137, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.3593523586396048, "volume": 128.61600000000004, "diffusion": 5.103, "adsorption": -53.185, "is_selected": false, "rank": 15}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3623150679343872, "volume": 131.88000000000002, "diffusion": 4.969, "adsorption": -48.829, "is_selected": false, "rank": 16}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.3645204958192295, "volume": 126.88000000000002, "diffusion": 4.911, "adsorption": -54.223, "is_selected": false, "rank": 17}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.37720161978772443, "volume": 132.96000000000004, "diffusion": 5.379, "adsorption": -50.2, "is_selected": false, "rank": 18}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.39483394140380446, "volume": 118.48800000000003, "diffusion": 5.286, "adsorption": -47.34, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4075311767336152, "volume": 138.80800000000002, "diffusion": 5.278, "adsorption": -51.812, "is_selected": false, "rank": 20}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.410091413793251, "volume": 129.73600000000002, "diffusion": 5.595, "adsorption": -49.912, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.42404976950410234, "volume": 127.16800000000003, "diffusion": 5.217, "adsorption": -47.935, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.43922049610176983, "volume": 126.30400000000003, "diffusion": 4.779, "adsorption": -56.023, "is_selected": false, "rank": 23}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.44781257252944784, "volume": 119.32800000000003, "diffusion": 5.773, "adsorption": -48.3, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4756504243021313, "volume": 128.98400000000004, "diffusion": 5.132, "adsorption": -55.003, "is_selected": false, "rank": 25}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.476465693079414, "volume": 123.65600000000003, "diffusion": 4.521, "adsorption": -45.891, "is_selected": false, "rank": 26}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4787775424011269, "volume": 139.48000000000005, "diffusion": 5.451, "adsorption": -49.733, "is_selected": false, "rank": 27}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4795226556859535, "volume": 132.44000000000003, "diffusion": 4.552, "adsorption": -47.082, "is_selected": false, "rank": 28}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4972351311769294, "volume": 129.02400000000003, "diffusion": 6.237, "adsorption": -51.435, "is_selected": false, "rank": 29}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5027270791334814, "volume": 131.00800000000004, "diffusion": 5.243, "adsorption": -54.757, "is_selected": false, "rank": 30}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5054540913984117, "volume": 133.30400000000003, "diffusion": 5.318, "adsorption": -47.855, "is_selected": false, "rank": 31}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5221705394806058, "volume": 126.03200000000002, "diffusion": 5.787, "adsorption": -48.118, "is_selected": false, "rank": 32}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5255855299008307, "volume": 142.64000000000004, "diffusion": 5.419, "adsorption": -52.728, "is_selected": false, "rank": 33}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5284663760026225, "volume": 126.52000000000002, "diffusion": 5.866, "adsorption": -48.367, "is_selected": false, "rank": 34}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5295530310133489, "volume": 134.22400000000002, "diffusion": 5.05, "adsorption": -55.404, "is_selected": false, "rank": 35}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.5355670240029424, "volume": 125.89600000000003, "diffusion": 5.729, "adsorption": -47.663, "is_selected": false, "rank": 36}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5362435219379682, "volume": 119.84800000000003, "diffusion": 5.787, "adsorption": -46.926, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.5419851978918391, "volume": 137.94400000000005, "diffusion": 5.533, "adsorption": -53.325, "is_selected": false, "rank": 38}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.5461474382396083, "volume": 147.01600000000005, "diffusion": 4.722, "adsorption": -47.59, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5507209861936833, "volume": 150.75200000000004, "diffusion": 5.325, "adsorption": -49.798, "is_selected": false, "rank": 40}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5524313013657681, "volume": 128.62400000000002, "diffusion": 5.064, "adsorption": -56.606, "is_selected": false, "rank": 41}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.563996925109012, "volume": 129.24800000000002, "diffusion": 5.218, "adsorption": -45.879, "is_selected": false, "rank": 42}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.576417573411116, "volume": 127.60000000000004, "diffusion": 5.202, "adsorption": -45.357, "is_selected": false, "rank": 43}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5809655455518111, "volume": 119.40800000000003, "diffusion": 6.135, "adsorption": -47.335, "is_selected": false, "rank": 44}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5902202087625523, "volume": 122.95200000000003, "diffusion": 5.099, "adsorption": -57.998, "is_selected": false, "rank": 45}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5910969431133086, "volume": 138.20000000000005, "diffusion": 5.322, "adsorption": -47.167, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5939431117395085, "volume": 128.76000000000002, "diffusion": 5.595, "adsorption": -46.635, "is_selected": false, "rank": 47}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6103629611462087, "volume": 119.09600000000003, "diffusion": 6.441, "adsorption": -47.875, "is_selected": false, "rank": 48}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.6120158183622566, "volume": 131.65600000000003, "diffusion": 5.068, "adsorption": -57.138, "is_selected": false, "rank": 49}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6189975807864856, "volume": 147.12000000000003, "diffusion": 4.58, "adsorption": -55.166, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6356113914875658, "volume": 135.68800000000005, "diffusion": 5.808, "adsorption": -47.751, "is_selected": false, "rank": 51}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6430399206473463, "volume": 155.81600000000003, "diffusion": 4.81, "adsorption": -47.177, "is_selected": false, "rank": 52}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6541385767663913, "volume": 145.16000000000003, "diffusion": 5.41, "adsorption": -47.48, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.6548338336487654, "volume": 121.00000000000003, "diffusion": 5.046, "adsorption": -59.586, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.6554493406374352, "volume": 133.45600000000005, "diffusion": 5.686, "adsorption": -55.402, "is_selected": false, "rank": 55}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.661071620059131, "volume": 142.64000000000004, "diffusion": 5.871, "adsorption": -53.425, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6638347909253353, "volume": 146.55200000000002, "diffusion": 5.814, "adsorption": -53.072, "is_selected": false, "rank": 57}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6667383200807273, "volume": 145.54400000000004, "diffusion": 5.736, "adsorption": -53.554, "is_selected": false, "rank": 58}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.681299730591901, "volume": 146.47200000000004, "diffusion": 6.054, "adsorption": -49.51, "is_selected": false, "rank": 59}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.682154178472302, "volume": 155.52800000000005, "diffusion": 4.901, "adsorption": -55.247, "is_selected": false, "rank": 60}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6970081127389529, "volume": 143.72800000000004, "diffusion": 5.08, "adsorption": -45.357, "is_selected": false, "rank": 61}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7103088246860088, "volume": 153.88800000000003, "diffusion": 5.395, "adsorption": -47.816, "is_selected": false, "rank": 62}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7186329477792581, "volume": 147.19200000000004, "diffusion": 5.289, "adsorption": -46.266, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7276158477187284, "volume": 134.38400000000004, "diffusion": 5.35, "adsorption": -57.681, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.736182797877923, "volume": 142.91200000000003, "diffusion": 6.671, "adsorption": -50.222, "is_selected": false, "rank": 65}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7393558456502346, "volume": 140.36000000000004, "diffusion": 6.529, "adsorption": -49.27, "is_selected": false, "rank": 66}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.7522116329452331, "volume": 138.96000000000004, "diffusion": 5.329, "adsorption": -44.569, "is_selected": false, "rank": 67}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7536216891581685, "volume": 139.35200000000003, "diffusion": 6.842, "adsorption": -49.985, "is_selected": false, "rank": 68}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7592002391322336, "volume": 136.75200000000004, "diffusion": 5.41, "adsorption": -44.398, "is_selected": false, "rank": 69}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7649345574349167, "volume": 130.88800000000003, "diffusion": 5.57, "adsorption": -43.966, "is_selected": false, "rank": 70}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7763565163695019, "volume": 133.96000000000004, "diffusion": 5.21, "adsorption": -42.965, "is_selected": false, "rank": 71}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7771804805963343, "volume": 147.09600000000003, "diffusion": 6.305, "adsorption": -48.868, "is_selected": false, "rank": 72}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7887575888021356, "volume": 138.06400000000002, "diffusion": 4.88, "adsorption": -42.213, "is_selected": false, "rank": 73}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7995606748923587, "volume": 145.49600000000004, "diffusion": 5.461, "adsorption": -56.798, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8005498316680595, "volume": 134.73600000000002, "diffusion": 5.415, "adsorption": -43.402, "is_selected": false, "rank": 75}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.8031081609610269, "volume": 145.74400000000003, "diffusion": 6.03, "adsorption": -47.241, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8188369403934992, "volume": 145.87200000000004, "diffusion": 5.693, "adsorption": -56.243, "is_selected": false, "rank": 77}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.8373235713077294, "volume": 140.18400000000003, "diffusion": 4.626, "adsorption": -41.927, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.8652779786865472, "volume": 128.81600000000003, "diffusion": 5.87, "adsorption": -59.031, "is_selected": false, "rank": 79}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8669937848382703, "volume": 128.52000000000004, "diffusion": 5.947, "adsorption": -58.832, "is_selected": false, "rank": 80}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8676885737811094, "volume": 155.45600000000005, "diffusion": 5.69, "adsorption": -46.43, "is_selected": false, "rank": 81}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.8701735962640182, "volume": 129.26400000000004, "diffusion": 6.648, "adsorption": -45.759, "is_selected": false, "rank": 82}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.8749817030507169, "volume": 138.93600000000004, "diffusion": 5.78, "adsorption": -44.081, "is_selected": false, "rank": 83}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.880153679419529, "volume": 150.47200000000004, "diffusion": 6.262, "adsorption": -47.484, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.8871574172246599, "volume": 155.44000000000003, "diffusion": 5.817, "adsorption": -46.548, "is_selected": false, "rank": 85}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8914705200725683, "volume": 148.66400000000004, "diffusion": 5.731, "adsorption": -45.125, "is_selected": false, "rank": 86}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9074899514845602, "volume": 130.18400000000003, "diffusion": 6.466, "adsorption": -44.619, "is_selected": false, "rank": 87}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.924354445469055, "volume": 138.37600000000003, "diffusion": 7.101, "adsorption": -47.852, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.9371202224433022, "volume": 151.11200000000002, "diffusion": 5.195, "adsorption": -42.82, "is_selected": false, "rank": 89}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9371683263638113, "volume": 133.97600000000003, "diffusion": 6.116, "adsorption": -43.454, "is_selected": false, "rank": 90}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9453607666001116, "volume": 156.88800000000003, "diffusion": 4.716, "adsorption": -42.343, "is_selected": false, "rank": 91}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.954477785910468, "volume": 139.56000000000003, "diffusion": 7.045, "adsorption": -47.323, "is_selected": false, "rank": 92}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9652481004537882, "volume": 142.40800000000004, "diffusion": 5.892, "adsorption": -43.479, "is_selected": false, "rank": 93}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.9671024216853719, "volume": 155.62400000000002, "diffusion": 5.956, "adsorption": -45.711, "is_selected": false, "rank": 94}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.9769580215733817, "volume": 130.40800000000004, "diffusion": 6.473, "adsorption": -43.497, "is_selected": false, "rank": 95}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 1.0087907227667632, "volume": 146.64000000000004, "diffusion": 5.38, "adsorption": -60.468, "is_selected": false, "rank": 96}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0237905035442316, "volume": 133.39200000000002, "diffusion": 5.344, "adsorption": -39.146, "is_selected": false, "rank": 97}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0432176232700021, "volume": 128.11200000000002, "diffusion": 5.844, "adsorption": -39.78, "is_selected": false, "rank": 98}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.050778828200169, "volume": 151.04800000000003, "diffusion": 5.101, "adsorption": -40.543, "is_selected": false, "rank": 99}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 1.0889168149774893, "volume": 135.21600000000004, "diffusion": 5.759, "adsorption": -39.795, "is_selected": false, "rank": 100}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1095023772768542, "volume": 135.01600000000002, "diffusion": 6.054, "adsorption": -40.463, "is_selected": false, "rank": 101}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2027367536704656, "volume": 123.28000000000003, "diffusion": 5.947, "adsorption": -65.349, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2150296853264109, "volume": 145.85600000000002, "diffusion": 6.921, "adsorption": -43.421, "is_selected": false, "rank": 103}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2932171081334745, "volume": 135.71200000000005, "diffusion": 5.246, "adsorption": -34.573, "is_selected": false, "rank": 104}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.306432076040762, "volume": 154.72800000000004, "diffusion": 6.294, "adsorption": -41.004, "is_selected": false, "rank": 105}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.356676744867048, "volume": 146.40000000000003, "diffusion": 6.086, "adsorption": -38.127, "is_selected": false, "rank": 106}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.3751093275156503, "volume": 147.20800000000003, "diffusion": 5.87, "adsorption": -37.17, "is_selected": false, "rank": 107}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.4337814070483679, "volume": 156.80800000000005, "diffusion": 5.409, "adsorption": -36.012, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.4774990479461114, "volume": 154.80800000000005, "diffusion": 6.67, "adsorption": -39.444, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770892407.581018}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "timestamp": 1770892407.6544092}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/d6dc84ef-93e2-4843-b51c-9c94045d3ed7.log", "timestamp": 1770892407.7178302}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892407.9937527}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.3920, 扩散: 4.7830, 吸附: -51.0190", "timestamp": 1770892408.0110867}, {"type": "log", "level": "info", "message": "远程日志已启动: d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "timestamp": 1770892408.0439157}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770892408.067774}, {"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770892408.085486}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892408.136148}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892408.1526408}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770892408.2015202}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770892408.2207637}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770892408.281557}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770892408.2992015}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.39, 扩散: 4.7830, 吸附: -51.0190", "timestamp": 1770892408.3857882}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.39, 扩散: 4.7830, 吸附: -51.0190", "timestamp": 1770892408.4106102}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770892408.481294}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770892408.5002272}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892408.559492}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892408.5783093}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892408.6004477}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.3920, 扩散: 4.7830, 吸附: -51.0190", "timestamp": 1770892408.6219046}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770892408.6456995}, {"type": "log", "level": "info", "message": "远程日志已启动: d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "timestamp": 1770892408.6717458}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770892408.7209923}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770892408.7420506}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770892408.8105004}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770892408.8287096}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770892408.8520145}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892408.9230516}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892408.9437733}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892409.0244353}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892409.0435069}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770892409.107903}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770892409.126439}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892409.1889288}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892409.20926}, {"type": "log", "level": "info", "message": "========== 第 0 层开始 ==========", "timestamp": 1770892409.232641}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770892409.324397}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770892409.34304}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770892409.3995287}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770892409.42115}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.03s, 速度: 6003.98 mol/s", "timestamp": 1770892409.4410272}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.028647661209106445, "speed": 6003.9805254791645, "timestamp": 1770892409.4602165}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770892409.5386586}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770892409.560996}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770892409.6372473}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770892409.6591625}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770892409.728989}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770892409.7513146}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770892409.7721512}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770892409.7888303}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770892409.84835}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770892409.8664186}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770892419.8508475}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770892419.8738651}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1179", "timestamp": 1770892419.8954532}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.11788262409207326, "timestamp": 1770892419.917766}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892419.9929729}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892420.0121226}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892420.0318224}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770892420.0512195}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892420.118032}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892420.1500082}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1179", "timestamp": 1770892420.2278655}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1179", "timestamp": 1770892420.2479591}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.62", "timestamp": 1770892420.3128483}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.62", "timestamp": 1770892420.330946}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770892420.3899174}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770892420.4108055}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770892420.4910107}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770892420.5151877}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 11.31s) ==========", "timestamp": 1770892420.541194}, {"type": "layer_end", "layer": 0, "duration": 11.308754920959473, "timestamp": 1770892420.563688}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892420.6416223}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892420.6623702}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892420.7250316}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892420.7442613}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770892420.8097162}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770892420.8295515}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892420.890982}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892420.9088666}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770892420.926518}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770892420.993314}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770892421.0138562}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770892421.0820687}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770892421.1005924}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 32025.74 mol/s", "timestamp": 1770892421.1211402}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0009367465972900391, "speed": 32025.736828709596, "timestamp": 1770892421.1465352}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770892421.2340293}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770892421.2567143}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770892421.2790468}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770892421.3014023}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770892421.3693225}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770892421.3875978}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892423.289272}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892423.3622465}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H], Score: 0.1834", "timestamp": 1770892423.4300034}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "best_score": 0.18335381131544046, "timestamp": 1770892423.5118475}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892423.7403374}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892423.761222}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892423.7802367}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770892423.8003623}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "timestamp": 1770892423.8674743}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "timestamp": 1770892423.889389}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1834", "timestamp": 1770892423.9520586}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1834", "timestamp": 1770892423.970324}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.54", "timestamp": 1770892424.0317273}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.54", "timestamp": 1770892424.0494862}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.7940", "timestamp": 1770892424.1079948}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.7940", "timestamp": 1770892424.1242783}, {"type": "log", "level": "info", "message": "INFO -     吸附: -52.2260", "timestamp": 1770892424.1835294}, {"type": "log", "level": "info", "message": "INFO -     吸附: -52.2260", "timestamp": 1770892424.199016}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 3.29s) ==========", "timestamp": 1770892424.2173333}, {"type": "layer_end", "layer": 1, "duration": 3.29036021232605, "timestamp": 1770892424.2336943}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892424.2879798}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892424.309145}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892424.372501}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892424.3916335}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770892424.4578216}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770892424.4761899}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892424.5367742}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892424.5527256}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770892424.5689535}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770892424.6296954}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770892424.6500301}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770892424.707982}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770892424.7258556}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 21718.64 mol/s", "timestamp": 1770892424.7460942}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0011510848999023438, "speed": 21718.64125932063, "timestamp": 1770892424.7648733}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770892424.8342948}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770892424.8603153}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770892424.8804348}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770892424.9022498}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770892424.9683635}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770892424.9871323}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892426.6292455}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892426.6501503}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1869", "timestamp": 1770892426.6714838}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1868578223223941, "timestamp": 1770892426.6916564}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892426.7546837}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892426.7709548}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892426.7866127}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]"], "timestamp": 1770892426.80256}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892426.8544242}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892426.8736935}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1869", "timestamp": 1770892426.94622}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1869", "timestamp": 1770892426.965266}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.54", "timestamp": 1770892427.0283635}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.54", "timestamp": 1770892427.0495398}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5090", "timestamp": 1770892427.113907}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5090", "timestamp": 1770892427.1328995}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5920", "timestamp": 1770892427.1935043}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5920", "timestamp": 1770892427.2136142}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.67s) ==========", "timestamp": 1770892427.2351825}, {"type": "layer_end", "layer": 2, "duration": 2.6658952236175537, "timestamp": 1770892427.2550187}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892427.3213413}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892427.3400974}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892427.4057705}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892427.4290795}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770892427.489836}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770892427.5088444}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892427.5685217}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892427.585891}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770892427.6085799}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770892427.6800878}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770892427.695407}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770892427.7461154}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770892427.7655647}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 6021.97 mol/s", "timestamp": 1770892427.7834194}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0033211708068847656, "speed": 6021.97272074659, "timestamp": 1770892427.7985177}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770892427.8445928}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770892427.8611162}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770892427.88098}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770892427.8991647}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770892427.9655666}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770892427.9850876}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892429.2796824}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892429.2964814}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H], Score: 0.1681", "timestamp": 1770892429.313882}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.16811182861801158, "timestamp": 1770892429.3314373}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892429.385244}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892429.401484}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892429.421213}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770892429.43999}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892429.4881434}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892429.506055}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1681", "timestamp": 1770892429.5502012}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1681", "timestamp": 1770892429.5686605}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.46", "timestamp": 1770892429.6366813}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.46", "timestamp": 1770892429.652645}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9220", "timestamp": 1770892429.7060328}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9220", "timestamp": 1770892429.7271907}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5240", "timestamp": 1770892429.7977922}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5240", "timestamp": 1770892429.8206725}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.25s) ==========", "timestamp": 1770892429.8597963}, {"type": "layer_end", "layer": 3, "duration": 2.251077651977539, "timestamp": 1770892429.9527292}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892430.229945}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892430.2989624}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892430.5261014}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892430.600254}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770892430.777396}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770892430.8226824}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892430.9501405}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892430.990041}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770892431.0254667}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770892431.1407006}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770892431.1773324}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770892431.3170965}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770892431.3564787}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 30615.36 mol/s", "timestamp": 1770892431.3939526}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0004899501800537109, "speed": 30615.357664233576, "timestamp": 1770892431.431336}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770892431.5603523}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770892431.5947564}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770892431.6316557}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770892431.6676033}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770892431.812795}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770892431.8490558}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892433.040101}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892433.0895243}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H], Score: 0.1613", "timestamp": 1770892433.1280084}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.16134703337242445, "timestamp": 1770892433.1664135}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892433.2924178}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892433.3311768}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892433.368994}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]"], "timestamp": 1770892433.402992}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892433.5196726}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892433.5556583}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1613", "timestamp": 1770892433.6782331}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1613", "timestamp": 1770892433.713226}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.95", "timestamp": 1770892433.8325374}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.95", "timestamp": 1770892433.8660731}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9000", "timestamp": 1770892433.9839306}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9000", "timestamp": 1770892434.0191326}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.4120", "timestamp": 1770892434.1339943}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.4120", "timestamp": 1770892434.1684113}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 3.18s) ==========", "timestamp": 1770892434.2072735}, {"type": "layer_end", "layer": 4, "duration": 3.1813037395477295, "timestamp": 1770892434.2425416}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892434.359336}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892434.3980148}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892434.5139806}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892434.5472512}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770892434.6536324}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770892434.6870959}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892434.7988548}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892434.842378}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770892434.880697}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770892435.0019689}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770892435.040562}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770892435.1511283}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770892435.1849098}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 20420.18 mol/s", "timestamp": 1770892435.2204878}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0004897117614746094, "speed": 20420.175267770206, "timestamp": 1770892435.2547853}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770892435.376027}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770892435.411988}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770892435.4497492}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770892435.4864664}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770892435.6015413}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770892435.637811}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892436.3898323}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892436.4249501}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1964", "timestamp": 1770892436.4604013}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.19639556791686008, "timestamp": 1770892436.4996142}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892436.613078}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892436.6504328}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892436.6856086}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770892436.7217524}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892436.8426132}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892436.8766537}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1964", "timestamp": 1770892436.9908366}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1964", "timestamp": 1770892437.0270436}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.53", "timestamp": 1770892437.1440032}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.53", "timestamp": 1770892437.178568}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5210", "timestamp": 1770892437.3021667}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5210", "timestamp": 1770892437.336316}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.7140", "timestamp": 1770892437.4580278}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.7140", "timestamp": 1770892437.5026085}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 2.66s) ==========", "timestamp": 1770892437.545713}, {"type": "layer_end", "layer": 5, "duration": 2.6645829677581787, "timestamp": 1770892437.5847456}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892437.7062845}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892437.7444232}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892437.8593228}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892437.8967373}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770892438.015522}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770892438.0526073}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892438.1709378}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892438.2107418}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770892438.248963}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770892438.3631012}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770892438.4018974}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770892438.5193548}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770892438.5550284}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 16777.22 mol/s", "timestamp": 1770892438.5899034}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0002980232238769531, "speed": 16777.216, "timestamp": 1770892438.6320305}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770892438.745488}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770892438.7798333}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770892438.8153536}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770892438.8512614}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770892438.9650238}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770892439.005828}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892439.5075638}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892439.557489}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0874", "timestamp": 1770892439.5979853}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08736714290989306, "timestamp": 1770892439.6366608}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892439.7507913}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892439.7859154}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892439.8215604}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770892439.8582726}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892439.9721034}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892440.011073}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0874", "timestamp": 1770892440.1296554}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0874", "timestamp": 1770892440.169624}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.58", "timestamp": 1770892440.2915316}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.58", "timestamp": 1770892440.3283443}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9710", "timestamp": 1770892440.441131}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9710", "timestamp": 1770892440.4734828}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.2300", "timestamp": 1770892440.5821197}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.2300", "timestamp": 1770892440.6197207}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 2.41s) ==========", "timestamp": 1770892440.6587505}, {"type": "layer_end", "layer": 6, "duration": 2.409224271774292, "timestamp": 1770892440.7040565}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892441.2131922}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892441.2484808}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892441.3485196}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892441.3791413}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770892441.49068}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770892441.5226536}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 31.87 秒", "timestamp": 1770892441.6385305}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 31.87 秒", "timestamp": 1770892441.6744313}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770892441.78594}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770892441.8207164}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892441.9233146}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892441.9556534}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770892442.0682783}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770892442.1104248}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.2179496}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.2510984}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1623, 体积: 125.85, 扩散: 4.8720, 吸附: -51.0820", "timestamp": 1770892442.355615}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1623, 体积: 125.85, 扩散: 4.8720, 吸附: -51.0820", "timestamp": 1770892442.3900688}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.492862}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.5259693}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1958, 体积: 110.70, 扩散: 5.0770, 吸附: -48.7830", "timestamp": 1770892442.6326084}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1958, 体积: 110.70, 扩散: 5.0770, 吸附: -48.7830", "timestamp": 1770892442.667862}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.7746782}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892442.8092763}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3119, 体积: 122.86, 扩散: 4.4410, 吸附: -53.1860", "timestamp": 1770892442.9207914}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3119, 体积: 122.86, 扩散: 4.4410, 吸附: -53.1860", "timestamp": 1770892442.9579935}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892443.0619743}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892443.0977747}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3660, 体积: 122.48, 扩散: 4.6240, 吸附: -47.2230", "timestamp": 1770892443.210974}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3660, 体积: 122.48, 扩散: 4.6240, 吸附: -47.2230", "timestamp": 1770892443.2515464}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "timestamp": 1770892443.3700116}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "timestamp": 1770892443.4023921}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.5001, 体积: 122.87, 扩散: 5.4180, 吸附: -55.3440", "timestamp": 1770892443.5069072}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.5001, 体积: 122.87, 扩散: 5.4180, 吸附: -55.3440", "timestamp": 1770892443.544842}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 36.00s", "timestamp": 1770892443.5818546}, {"type": "finish", "total_results": 5, "duration": 36.002450466156006, "timestamp": 1770892443.6207547}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/d6dc84ef-93e2-4843-b51c-9c94045d3ed7.json", "timestamp": 1770892443.667601}, {"type": "log", "level": "info", "message": "远程日志已上传: d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "timestamp": 1770892443.789521}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770892443.9120953}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770892443.9588351}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770892444.0853028}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770892444.1243527}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892444.1770463}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.3920, 扩散: 4.7830, 吸附: -51.0190", "timestamp": 1770892444.2174828}, {"type": "log", "level": "info", "message": "远程日志已启动: d6dc84ef-93e2-4843-b51c-9c94045d3ed7", "timestamp": 1770892444.3108974}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770892444.3959007}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770892444.4308906}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 36.90s", "timestamp": 1770892444.4804797}, {"type": "finish", "total_results": 5, "duration": 36.901058197021484, "timestamp": 1770892444.5223637}]}