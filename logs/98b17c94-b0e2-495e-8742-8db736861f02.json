{"metadata": {"log_name": "98b17c94-b0e2-495e-8742-8db736861f02", "start_time": 1770953268.2070096, "end_time": 1770953295.958803, "duration": 27.751793384552002, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.03200000000002, "diffusion": 5.546, "adsorption": -51.626}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770953270.065695, "end_time": 1770953281.0672166, "duration": 11.001521587371826, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0653592036287792}, {"layer_num": 1, "start_time": 1770953281.299845, "end_time": 1770953283.7481596, "duration": 2.448314666748047, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07297605742684013}, {"layer_num": 2, "start_time": 1770953283.9746888, "end_time": 1770953286.1958003, "duration": 2.221111536026001, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20444421913006566}, {"layer_num": 3, "start_time": 1770953286.3982756, "end_time": 1770953288.3362136, "duration": 1.9379379749298096, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "best_score": 0.2348158244365576}, {"layer_num": 4, "start_time": 1770953288.549138, "end_time": 1770953290.1969922, "duration": 1.6478540897369385, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07268523293941818}, {"layer_num": 5, "start_time": 1770953290.4064329, "end_time": 1770953291.7761087, "duration": 1.369675874710083, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "best_score": 0.20603198431981037}, {"layer_num": 6, "start_time": 1770953291.9786549, "end_time": 1770953293.0858858, "duration": 1.1072309017181396, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20761057632044555}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0653592036287792, "volume": 110.19200000000002, "diffusion": 5.228, "adsorption": -51.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24142420779199678, "volume": 129.56000000000003, "diffusion": 5.57, "adsorption": -50.6, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.27782732601755156, "volume": 127.09600000000003, "diffusion": 5.242, "adsorption": -50.457, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28145017163254055, "volume": 126.69600000000003, "diffusion": 5.713, "adsorption": -49.907, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.30721116913690094, "volume": 128.68000000000004, "diffusion": 6.238, "adsorption": -51.403, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.31323443655149485, "volume": 118.87200000000003, "diffusion": 5.787, "adsorption": -48.366, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.31387704669130656, "volume": 126.32800000000003, "diffusion": 4.8, "adsorption": -51.088, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3164736848086853, "volume": 133.21600000000004, "diffusion": 5.379, "adsorption": -50.324, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3220074423416374, "volume": 137.92000000000004, "diffusion": 5.864, "adsorption": -51.433, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3255608354780683, "volume": 131.51200000000003, "diffusion": 5.283, "adsorption": -53.053, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.3280557003021909, "volume": 137.48000000000005, "diffusion": 5.229, "adsorption": -51.995, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.33263844464190734, "volume": 126.76800000000003, "diffusion": 5.13, "adsorption": -49.81, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.34497857684488153, "volume": 122.44800000000004, "diffusion": 4.615, "adsorption": -52.732, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.34572252282506616, "volume": 131.92000000000004, "diffusion": 4.922, "adsorption": -51.036, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3618931290129109, "volume": 119.52000000000002, "diffusion": 5.441, "adsorption": -47.208, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.36491006852025654, "volume": 127.64800000000002, "diffusion": 5.59, "adsorption": -48.238, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.386637290968807, "volume": 121.48800000000003, "diffusion": 4.673, "adsorption": -49.473, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.3911114402930981, "volume": 127.79200000000003, "diffusion": 4.546, "adsorption": -52.476, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3936961745918261, "volume": 144.82400000000004, "diffusion": 5.727, "adsorption": -50.854, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.40354278840233004, "volume": 127.11200000000002, "diffusion": 5.606, "adsorption": -47.539, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.4090307859405782, "volume": 126.88000000000002, "diffusion": 4.925, "adsorption": -54.103, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.4250115507584884, "volume": 142.06400000000002, "diffusion": 6.228, "adsorption": -51.438, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.4275164012196234, "volume": 122.58400000000003, "diffusion": 4.789, "adsorption": -54.671, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4289916997032217, "volume": 118.08800000000002, "diffusion": 5.871, "adsorption": -46.512, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.4308168582883769, "volume": 128.60800000000003, "diffusion": 5.113, "adsorption": -54.791, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.44769948691902045, "volume": 128.32800000000003, "diffusion": 5.003, "adsorption": -54.784, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.44872956862029173, "volume": 150.77600000000004, "diffusion": 5.373, "adsorption": -50.813, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.46643594284455425, "volume": 133.95200000000003, "diffusion": 4.674, "adsorption": -50.046, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4674465284543534, "volume": 133.33600000000004, "diffusion": 5.842, "adsorption": -48.145, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4708327077204321, "volume": 120.09600000000003, "diffusion": 6.59, "adsorption": -48.337, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.4852459774188464, "volume": 145.64000000000004, "diffusion": 5.91, "adsorption": -53.278, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.48605497197964836, "volume": 139.35200000000003, "diffusion": 4.683, "adsorption": -52.727, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.48665248663416444, "volume": 134.69600000000003, "diffusion": 4.591, "adsorption": -50.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4956274774738386, "volume": 124.45600000000003, "diffusion": 5.587, "adsorption": -45.48, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.49744052832745683, "volume": 146.48000000000005, "diffusion": 5.362, "adsorption": -53.915, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5034438171079731, "volume": 139.18400000000003, "diffusion": 6.103, "adsorption": -49.25, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.5052113467450552, "volume": 134.96800000000005, "diffusion": 5.477, "adsorption": -56.206, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5105732733718753, "volume": 135.96000000000004, "diffusion": 5.762, "adsorption": -47.565, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5126467239854753, "volume": 144.13600000000002, "diffusion": 5.544, "adsorption": -48.144, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5175264942892996, "volume": 155.32000000000005, "diffusion": 5.05, "adsorption": -51.342, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.5189435569830475, "volume": 138.95200000000003, "diffusion": 4.639, "adsorption": -53.219, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5205782564616626, "volume": 130.12000000000003, "diffusion": 5.738, "adsorption": -46.405, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5309249675295187, "volume": 138.11200000000002, "diffusion": 5.275, "adsorption": -47.722, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.5398477007973532, "volume": 129.67200000000003, "diffusion": 4.813, "adsorption": -47.682, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.5487893212498834, "volume": 128.83200000000002, "diffusion": 5.073, "adsorption": -56.662, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.554609555986627, "volume": 123.14400000000003, "diffusion": 4.609, "adsorption": -47.04, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5580140752510601, "volume": 131.12800000000004, "diffusion": 5.763, "adsorption": -45.996, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5602556523064552, "volume": 129.91200000000003, "diffusion": 5.069, "adsorption": -56.678, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5625854544242191, "volume": 146.43200000000004, "diffusion": 4.822, "adsorption": -53.368, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.5877679864215761, "volume": 130.17600000000004, "diffusion": 6.355, "adsorption": -47.172, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5894997791919339, "volume": 123.40000000000003, "diffusion": 4.479, "adsorption": -46.883, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5958384282154516, "volume": 127.50400000000003, "diffusion": 3.952, "adsorption": -49.051, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6019286480520816, "volume": 139.28800000000004, "diffusion": 7.148, "adsorption": -50.814, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.6042037128308944, "volume": 142.19200000000004, "diffusion": 5.023, "adsorption": -55.371, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.6050231097554131, "volume": 134.96800000000005, "diffusion": 5.364, "adsorption": -57.573, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.6106416812697412, "volume": 120.54400000000003, "diffusion": 4.999, "adsorption": -58.793, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.6238107052245316, "volume": 146.27200000000005, "diffusion": 5.693, "adsorption": -56.237, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6405122832715371, "volume": 146.84000000000003, "diffusion": 5.582, "adsorption": -46.472, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6569863856406466, "volume": 130.67200000000003, "diffusion": 5.797, "adsorption": -44.327, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6600669392972915, "volume": 128.03200000000004, "diffusion": 5.728, "adsorption": -59.605, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.6611340250529539, "volume": 147.64000000000004, "diffusion": 6.464, "adsorption": -48.979, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6683253676271144, "volume": 132.41600000000003, "diffusion": 4.407, "adsorption": -47.16, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.6763805079981877, "volume": 156.04000000000005, "diffusion": 5.683, "adsorption": -47.607, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.6787547236769713, "volume": 145.74400000000003, "diffusion": 5.966, "adsorption": -46.834, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6791164755901142, "volume": 135.70400000000004, "diffusion": 4.004, "adsorption": -48.739, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6911216181185114, "volume": 138.38400000000004, "diffusion": 5.586, "adsorption": -44.291, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.6940096683868981, "volume": 146.47200000000004, "diffusion": 6.332, "adsorption": -47.821, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.7026137616823673, "volume": 128.65600000000003, "diffusion": 5.86, "adsorption": -59.83, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7046910882202918, "volume": 118.97600000000003, "diffusion": 6.333, "adsorption": -43.34, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.7217140606984697, "volume": 127.03200000000002, "diffusion": 4.963, "adsorption": -59.578, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.7285024281144276, "volume": 154.38400000000004, "diffusion": 5.663, "adsorption": -46.389, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7294814607115879, "volume": 130.26400000000004, "diffusion": 6.782, "adsorption": -46.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.732166797047485, "volume": 146.75200000000004, "diffusion": 4.849, "adsorption": -46.932, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.7323904847143845, "volume": 141.76800000000003, "diffusion": 6.392, "adsorption": -46.611, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.7344968685639204, "volume": 155.08800000000005, "diffusion": 4.962, "adsorption": -55.407, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7481224226983664, "volume": 134.10400000000004, "diffusion": 5.754, "adsorption": -43.162, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.7610255070533856, "volume": 150.88800000000003, "diffusion": 6.299, "adsorption": -47.256, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7654590787675425, "volume": 129.17600000000004, "diffusion": 4.142, "adsorption": -45.804, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7834789655453631, "volume": 136.56800000000004, "diffusion": 5.677, "adsorption": -42.7, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7840208750369031, "volume": 138.31200000000004, "diffusion": 6.771, "adsorption": -46.358, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8008249377497788, "volume": 143.85600000000002, "diffusion": 4.835, "adsorption": -45.341, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8047318691027869, "volume": 133.52800000000002, "diffusion": 5.912, "adsorption": -42.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8103785672385104, "volume": 154.20000000000005, "diffusion": 5.237, "adsorption": -45.547, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.8164065362238602, "volume": 145.96800000000005, "diffusion": 5.274, "adsorption": -59.211, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8196685895157616, "volume": 149.20800000000003, "diffusion": 6.201, "adsorption": -45.68, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.8286343756519716, "volume": 130.84000000000003, "diffusion": 6.473, "adsorption": -43.497, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8525555051948801, "volume": 139.90400000000002, "diffusion": 5.91, "adsorption": -42.756, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.8658057058020564, "volume": 138.24000000000004, "diffusion": 5.471, "adsorption": -41.371, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8765034039961348, "volume": 151.72800000000004, "diffusion": 5.329, "adsorption": -43.737, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8784589071687043, "volume": 128.16000000000003, "diffusion": 5.837, "adsorption": -40.247, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.9107106938151817, "volume": 156.08800000000005, "diffusion": 5.477, "adsorption": -43.371, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9236168709730734, "volume": 138.29600000000002, "diffusion": 4.955, "adsorption": -41.986, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.9351956001418416, "volume": 140.44800000000004, "diffusion": 4.89, "adsorption": -42.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9360056403086241, "volume": 138.45600000000005, "diffusion": 7.266, "adsorption": -45.301, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.936346283360395, "volume": 134.94400000000005, "diffusion": 6.033, "adsorption": -40.92, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9592072242275743, "volume": 147.56000000000003, "diffusion": 6.172, "adsorption": -42.931, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9593214105716745, "volume": 132.36800000000002, "diffusion": 5.387, "adsorption": -39.104, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 0.9868909702224867, "volume": 122.68800000000003, "diffusion": 5.932, "adsorption": -65.432, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0437774733352665, "volume": 137.42400000000004, "diffusion": 5.682, "adsorption": -38.37, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.057237634775781, "volume": 140.78400000000002, "diffusion": 6.558, "adsorption": -41.382, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0736059300264365, "volume": 134.16800000000003, "diffusion": 6.523, "adsorption": -39.957, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.0779097545597456, "volume": 156.16800000000003, "diffusion": 6.361, "adsorption": -42.821, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.1464280916623253, "volume": 152.10400000000004, "diffusion": 5.074, "adsorption": -39.942, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1506732596831568, "volume": 155.94400000000005, "diffusion": 4.836, "adsorption": -41.208, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.1984089052899476, "volume": 145.88000000000002, "diffusion": 6.824, "adsorption": -40.575, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.218902269553163, "volume": 157.32000000000005, "diffusion": 5.206, "adsorption": -39.101, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.2293040101731822, "volume": 146.35200000000003, "diffusion": 6.356, "adsorption": -38.665, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.285206759875864, "volume": 136.46400000000003, "diffusion": 5.266, "adsorption": -34.512, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3384678588683967, "volume": 155.20000000000005, "diffusion": 6.033, "adsorption": -37.168, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07297605742684013, "volume": 110.30400000000003, "diffusion": 5.242, "adsorption": -51.356, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23909761189262205, "volume": 129.30400000000003, "diffusion": 5.57, "adsorption": -50.6, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2502304045418289, "volume": 126.56800000000003, "diffusion": 5.633, "adsorption": -50.176, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28724935325579604, "volume": 127.67200000000003, "diffusion": 6.249, "adsorption": -51.629, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3535882215870024, "volume": 126.90400000000002, "diffusion": 5.079, "adsorption": -49.629, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.20444421913006566, "volume": 110.00000000000003, "diffusion": 4.922, "adsorption": -50.049, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.24841275149544234, "volume": 126.36800000000002, "diffusion": 5.633, "adsorption": -50.176, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3405076646296691, "volume": 129.41600000000003, "diffusion": 5.955, "adsorption": -50.067, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3633740675583659, "volume": 126.60000000000004, "diffusion": 4.856, "adsorption": -50.105, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.48950644808238425, "volume": 129.03200000000004, "diffusion": 6.413, "adsorption": -48.864, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2348158244365576, "volume": 126.20000000000003, "diffusion": 5.733, "adsorption": -50.694, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.25766774545076443, "volume": 110.73600000000003, "diffusion": 4.885, "adsorption": -49.353, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2580357688972078, "volume": 126.10400000000003, "diffusion": 5.256, "adsorption": -50.599, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3235138641289951, "volume": 128.49600000000004, "diffusion": 6.212, "adsorption": -51.013, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3392410195755406, "volume": 129.69600000000003, "diffusion": 5.959, "adsorption": -50.145, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07268523293941818, "volume": 110.27200000000002, "diffusion": 5.242, "adsorption": -51.356, "layer": 4, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2710766879604899, "volume": 126.84000000000003, "diffusion": 5.253, "adsorption": -50.499, "layer": 4, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2906096187635785, "volume": 127.08000000000003, "diffusion": 5.563, "adsorption": -49.344, "layer": 4, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3057318481332926, "volume": 129.68000000000004, "diffusion": 5.598, "adsorption": -49.599, "layer": 4, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.35287321462939025, "volume": 128.71200000000002, "diffusion": 6.265, "adsorption": -50.706, "layer": 4, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.20603198431981037, "volume": 126.67200000000003, "diffusion": 5.819, "adsorption": -51.722, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.22466582546315833, "volume": 129.26400000000004, "diffusion": 5.744, "adsorption": -51.382, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.253903963821879, "volume": 110.78400000000002, "diffusion": 4.946, "adsorption": -49.236, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.27652964709964933, "volume": 127.44000000000003, "diffusion": 5.253, "adsorption": -50.499, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.39875525147169494, "volume": 127.81600000000003, "diffusion": 6.058, "adsorption": -49.134, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.20761057632044555, "volume": 110.68800000000003, "diffusion": 4.923, "adsorption": -50.089, "layer": 6, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.22193324545002835, "volume": 126.11200000000002, "diffusion": 5.792, "adsorption": -51.085, "layer": 6, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2705427128532194, "volume": 128.88000000000002, "diffusion": 5.584, "adsorption": -50.036, "layer": 6, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3714476943061098, "volume": 127.16800000000003, "diffusion": 4.855, "adsorption": -50.058, "layer": 6, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.42184229966747666, "volume": 127.64000000000003, "diffusion": 6.066, "adsorption": -48.734, "layer": 6, "parent_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.03200000000002, "diffusion": 5.546, "adsorption": -51.626}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.20761057632044555, "volume": 110.68800000000003, "diffusion": 4.923, "adsorption": -50.089, "is_selected": false, "rank": 1}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.2705427128532194, "volume": 128.88000000000002, "diffusion": 5.584, "adsorption": -50.036, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6, "score": 0.3714476943061098, "volume": 127.16800000000003, "diffusion": 4.855, "adsorption": -50.058, "is_selected": false, "rank": 4}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.22193324545002835, "volume": 126.11200000000002, "diffusion": 5.792, "adsorption": -51.085, "is_selected": false, "rank": 2}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.42184229966747666, "volume": 127.64000000000003, "diffusion": 6.066, "adsorption": -48.734, "is_selected": false, "rank": 5}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.31323443655149485, "volume": 118.87200000000003, "diffusion": 5.787, "adsorption": -48.366, "is_selected": false, "rank": 6}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.31387704669130656, "volume": 126.32800000000003, "diffusion": 4.8, "adsorption": -51.088, "is_selected": false, "rank": 7}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.3164736848086853, "volume": 133.21600000000004, "diffusion": 5.379, "adsorption": -50.324, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3220074423416374, "volume": 137.92000000000004, "diffusion": 5.864, "adsorption": -51.433, "is_selected": false, "rank": 9}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3255608354780683, "volume": 131.51200000000003, "diffusion": 5.283, "adsorption": -53.053, "is_selected": false, "rank": 10}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3280557003021909, "volume": 137.48000000000005, "diffusion": 5.229, "adsorption": -51.995, "is_selected": false, "rank": 11}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.33263844464190734, "volume": 126.76800000000003, "diffusion": 5.13, "adsorption": -49.81, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.34497857684488153, "volume": 122.44800000000004, "diffusion": 4.615, "adsorption": -52.732, "is_selected": false, "rank": 13}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.34572252282506616, "volume": 131.92000000000004, "diffusion": 4.922, "adsorption": -51.036, "is_selected": false, "rank": 14}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3618931290129109, "volume": 119.52000000000002, "diffusion": 5.441, "adsorption": -47.208, "is_selected": false, "rank": 15}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.36491006852025654, "volume": 127.64800000000002, "diffusion": 5.59, "adsorption": -48.238, "is_selected": false, "rank": 16}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.386637290968807, "volume": 121.48800000000003, "diffusion": 4.673, "adsorption": -49.473, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.3911114402930981, "volume": 127.79200000000003, "diffusion": 4.546, "adsorption": -52.476, "is_selected": false, "rank": 18}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3936961745918261, "volume": 144.82400000000004, "diffusion": 5.727, "adsorption": -50.854, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.40354278840233004, "volume": 127.11200000000002, "diffusion": 5.606, "adsorption": -47.539, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.4090307859405782, "volume": 126.88000000000002, "diffusion": 4.925, "adsorption": -54.103, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.4250115507584884, "volume": 142.06400000000002, "diffusion": 6.228, "adsorption": -51.438, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4275164012196234, "volume": 122.58400000000003, "diffusion": 4.789, "adsorption": -54.671, "is_selected": false, "rank": 23}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4289916997032217, "volume": 118.08800000000002, "diffusion": 5.871, "adsorption": -46.512, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4308168582883769, "volume": 128.60800000000003, "diffusion": 5.113, "adsorption": -54.791, "is_selected": false, "rank": 25}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.44769948691902045, "volume": 128.32800000000003, "diffusion": 5.003, "adsorption": -54.784, "is_selected": false, "rank": 26}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.44872956862029173, "volume": 150.77600000000004, "diffusion": 5.373, "adsorption": -50.813, "is_selected": false, "rank": 27}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.46643594284455425, "volume": 133.95200000000003, "diffusion": 4.674, "adsorption": -50.046, "is_selected": false, "rank": 28}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4674465284543534, "volume": 133.33600000000004, "diffusion": 5.842, "adsorption": -48.145, "is_selected": false, "rank": 29}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4708327077204321, "volume": 120.09600000000003, "diffusion": 6.59, "adsorption": -48.337, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4852459774188464, "volume": 145.64000000000004, "diffusion": 5.91, "adsorption": -53.278, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.48605497197964836, "volume": 139.35200000000003, "diffusion": 4.683, "adsorption": -52.727, "is_selected": false, "rank": 32}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.48665248663416444, "volume": 134.69600000000003, "diffusion": 4.591, "adsorption": -50.072, "is_selected": false, "rank": 33}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4956274774738386, "volume": 124.45600000000003, "diffusion": 5.587, "adsorption": -45.48, "is_selected": false, "rank": 34}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.49744052832745683, "volume": 146.48000000000005, "diffusion": 5.362, "adsorption": -53.915, "is_selected": false, "rank": 35}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5034438171079731, "volume": 139.18400000000003, "diffusion": 6.103, "adsorption": -49.25, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.5052113467450552, "volume": 134.96800000000005, "diffusion": 5.477, "adsorption": -56.206, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5105732733718753, "volume": 135.96000000000004, "diffusion": 5.762, "adsorption": -47.565, "is_selected": false, "rank": 38}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5126467239854753, "volume": 144.13600000000002, "diffusion": 5.544, "adsorption": -48.144, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5175264942892996, "volume": 155.32000000000005, "diffusion": 5.05, "adsorption": -51.342, "is_selected": false, "rank": 40}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5189435569830475, "volume": 138.95200000000003, "diffusion": 4.639, "adsorption": -53.219, "is_selected": false, "rank": 41}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5205782564616626, "volume": 130.12000000000003, "diffusion": 5.738, "adsorption": -46.405, "is_selected": false, "rank": 42}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5309249675295187, "volume": 138.11200000000002, "diffusion": 5.275, "adsorption": -47.722, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.5398477007973532, "volume": 129.67200000000003, "diffusion": 4.813, "adsorption": -47.682, "is_selected": false, "rank": 44}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5487893212498834, "volume": 128.83200000000002, "diffusion": 5.073, "adsorption": -56.662, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0, "score": 0.554609555986627, "volume": 123.14400000000003, "diffusion": 4.609, "adsorption": -47.04, "is_selected": false, "rank": 46}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5580140752510601, "volume": 131.12800000000004, "diffusion": 5.763, "adsorption": -45.996, "is_selected": false, "rank": 47}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5602556523064552, "volume": 129.91200000000003, "diffusion": 5.069, "adsorption": -56.678, "is_selected": false, "rank": 48}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5625854544242191, "volume": 146.43200000000004, "diffusion": 4.822, "adsorption": -53.368, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.5877679864215761, "volume": 130.17600000000004, "diffusion": 6.355, "adsorption": -47.172, "is_selected": false, "rank": 50}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5894997791919339, "volume": 123.40000000000003, "diffusion": 4.479, "adsorption": -46.883, "is_selected": false, "rank": 51}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5958384282154516, "volume": 127.50400000000003, "diffusion": 3.952, "adsorption": -49.051, "is_selected": false, "rank": 52}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6019286480520816, "volume": 139.28800000000004, "diffusion": 7.148, "adsorption": -50.814, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6042037128308944, "volume": 142.19200000000004, "diffusion": 5.023, "adsorption": -55.371, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6050231097554131, "volume": 134.96800000000005, "diffusion": 5.364, "adsorption": -57.573, "is_selected": false, "rank": 55}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.6106416812697412, "volume": 120.54400000000003, "diffusion": 4.999, "adsorption": -58.793, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6238107052245316, "volume": 146.27200000000005, "diffusion": 5.693, "adsorption": -56.237, "is_selected": false, "rank": 57}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6405122832715371, "volume": 146.84000000000003, "diffusion": 5.582, "adsorption": -46.472, "is_selected": false, "rank": 58}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6569863856406466, "volume": 130.67200000000003, "diffusion": 5.797, "adsorption": -44.327, "is_selected": false, "rank": 59}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6600669392972915, "volume": 128.03200000000004, "diffusion": 5.728, "adsorption": -59.605, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6611340250529539, "volume": 147.64000000000004, "diffusion": 6.464, "adsorption": -48.979, "is_selected": false, "rank": 61}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6683253676271144, "volume": 132.41600000000003, "diffusion": 4.407, "adsorption": -47.16, "is_selected": false, "rank": 62}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6763805079981877, "volume": 156.04000000000005, "diffusion": 5.683, "adsorption": -47.607, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.6787547236769713, "volume": 145.74400000000003, "diffusion": 5.966, "adsorption": -46.834, "is_selected": false, "rank": 64}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6791164755901142, "volume": 135.70400000000004, "diffusion": 4.004, "adsorption": -48.739, "is_selected": false, "rank": 65}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6911216181185114, "volume": 138.38400000000004, "diffusion": 5.586, "adsorption": -44.291, "is_selected": false, "rank": 66}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.6940096683868981, "volume": 146.47200000000004, "diffusion": 6.332, "adsorption": -47.821, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.7026137616823673, "volume": 128.65600000000003, "diffusion": 5.86, "adsorption": -59.83, "is_selected": false, "rank": 68}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7046910882202918, "volume": 118.97600000000003, "diffusion": 6.333, "adsorption": -43.34, "is_selected": false, "rank": 69}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.7217140606984697, "volume": 127.03200000000002, "diffusion": 4.963, "adsorption": -59.578, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.7285024281144276, "volume": 154.38400000000004, "diffusion": 5.663, "adsorption": -46.389, "is_selected": false, "rank": 71}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7294814607115879, "volume": 130.26400000000004, "diffusion": 6.782, "adsorption": -46.072, "is_selected": false, "rank": 72}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.732166797047485, "volume": 146.75200000000004, "diffusion": 4.849, "adsorption": -46.932, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.7323904847143845, "volume": 141.76800000000003, "diffusion": 6.392, "adsorption": -46.611, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7344968685639204, "volume": 155.08800000000005, "diffusion": 4.962, "adsorption": -55.407, "is_selected": false, "rank": 75}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7481224226983664, "volume": 134.10400000000004, "diffusion": 5.754, "adsorption": -43.162, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7610255070533856, "volume": 150.88800000000003, "diffusion": 6.299, "adsorption": -47.256, "is_selected": false, "rank": 77}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7654590787675425, "volume": 129.17600000000004, "diffusion": 4.142, "adsorption": -45.804, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7834789655453631, "volume": 136.56800000000004, "diffusion": 5.677, "adsorption": -42.7, "is_selected": false, "rank": 79}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7840208750369031, "volume": 138.31200000000004, "diffusion": 6.771, "adsorption": -46.358, "is_selected": false, "rank": 80}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8008249377497788, "volume": 143.85600000000002, "diffusion": 4.835, "adsorption": -45.341, "is_selected": false, "rank": 81}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8047318691027869, "volume": 133.52800000000002, "diffusion": 5.912, "adsorption": -42.588, "is_selected": false, "rank": 82}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8103785672385104, "volume": 154.20000000000005, "diffusion": 5.237, "adsorption": -45.547, "is_selected": false, "rank": 83}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.8164065362238602, "volume": 145.96800000000005, "diffusion": 5.274, "adsorption": -59.211, "is_selected": false, "rank": 84}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8196685895157616, "volume": 149.20800000000003, "diffusion": 6.201, "adsorption": -45.68, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.8286343756519716, "volume": 130.84000000000003, "diffusion": 6.473, "adsorption": -43.497, "is_selected": false, "rank": 86}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8525555051948801, "volume": 139.90400000000002, "diffusion": 5.91, "adsorption": -42.756, "is_selected": false, "rank": 87}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.8658057058020564, "volume": 138.24000000000004, "diffusion": 5.471, "adsorption": -41.371, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8765034039961348, "volume": 151.72800000000004, "diffusion": 5.329, "adsorption": -43.737, "is_selected": false, "rank": 89}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8784589071687043, "volume": 128.16000000000003, "diffusion": 5.837, "adsorption": -40.247, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.9107106938151817, "volume": 156.08800000000005, "diffusion": 5.477, "adsorption": -43.371, "is_selected": false, "rank": 91}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9236168709730734, "volume": 138.29600000000002, "diffusion": 4.955, "adsorption": -41.986, "is_selected": false, "rank": 92}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.9351956001418416, "volume": 140.44800000000004, "diffusion": 4.89, "adsorption": -42.325, "is_selected": false, "rank": 93}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9360056403086241, "volume": 138.45600000000005, "diffusion": 7.266, "adsorption": -45.301, "is_selected": false, "rank": 94}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.936346283360395, "volume": 134.94400000000005, "diffusion": 6.033, "adsorption": -40.92, "is_selected": false, "rank": 95}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9592072242275743, "volume": 147.56000000000003, "diffusion": 6.172, "adsorption": -42.931, "is_selected": false, "rank": 96}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9593214105716745, "volume": 132.36800000000002, "diffusion": 5.387, "adsorption": -39.104, "is_selected": false, "rank": 97}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9868909702224867, "volume": 122.68800000000003, "diffusion": 5.932, "adsorption": -65.432, "is_selected": false, "rank": 98}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0437774733352665, "volume": 137.42400000000004, "diffusion": 5.682, "adsorption": -38.37, "is_selected": false, "rank": 99}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.057237634775781, "volume": 140.78400000000002, "diffusion": 6.558, "adsorption": -41.382, "is_selected": false, "rank": 100}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0736059300264365, "volume": 134.16800000000003, "diffusion": 6.523, "adsorption": -39.957, "is_selected": false, "rank": 101}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.0779097545597456, "volume": 156.16800000000003, "diffusion": 6.361, "adsorption": -42.821, "is_selected": false, "rank": 102}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.1464280916623253, "volume": 152.10400000000004, "diffusion": 5.074, "adsorption": -39.942, "is_selected": false, "rank": 103}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1506732596831568, "volume": 155.94400000000005, "diffusion": 4.836, "adsorption": -41.208, "is_selected": false, "rank": 104}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.1984089052899476, "volume": 145.88000000000002, "diffusion": 6.824, "adsorption": -40.575, "is_selected": false, "rank": 105}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.218902269553163, "volume": 157.32000000000005, "diffusion": 5.206, "adsorption": -39.101, "is_selected": false, "rank": 106}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2293040101731822, "volume": 146.35200000000003, "diffusion": 6.356, "adsorption": -38.665, "is_selected": false, "rank": 107}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.285206759875864, "volume": 136.46400000000003, "diffusion": 5.266, "adsorption": -34.512, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3384678588683967, "volume": 155.20000000000005, "diffusion": 6.033, "adsorption": -37.168, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 4}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6}, {"from": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770953268.2076504}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 98b17c94-b0e2-495e-8742-8db736861f02", "timestamp": 1770953268.257192}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/98b17c94-b0e2-495e-8742-8db736861f02.log", "timestamp": 1770953268.3004475}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770953268.8292856}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770953268.8815882}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770953268.94564}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953269.034181}, {"type": "log", "level": "info", "message": "远程日志已启动: 98b17c94-b0e2-495e-8742-8db736861f02", "timestamp": 1770953269.0402074}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770953269.1581655}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770953269.2069442}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770953269.2564614}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770953269.3030117}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953269.3550029}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770953269.4152617}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770953269.4652126}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770953269.506106}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770953269.5589972}, {"type": "log", "level": "info", "message": "远程日志已启动: 98b17c94-b0e2-495e-8742-8db736861f02", "timestamp": 1770953269.573212}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770953269.6798105}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770953269.7519226}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953269.7951343}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953269.8394105}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770953269.892703}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953269.959203}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770953270.0659413}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.02 秒", "timestamp": 1770953270.1319091}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770953270.1700292}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.02s, 速度: 7058.98 mol/s", "timestamp": 1770953270.2126203}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.024366140365600586, "speed": 7058.9759978081975, "timestamp": 1770953270.2519438}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770953270.3080773}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770953270.347251}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770953270.388196}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770953270.4340448}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770953270.4737132}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770953270.5171204}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770953280.6729333}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0654", "timestamp": 1770953280.708265}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0653592036287792, "timestamp": 1770953280.7444546}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953280.778407}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953280.8130996}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770953280.8475075}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953280.8811226}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0654", "timestamp": 1770953280.9144669}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.19", "timestamp": 1770953280.9505556}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2280", "timestamp": 1770953280.9873219}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5130", "timestamp": 1770953281.022385}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 11.00s) ==========", "timestamp": 1770953281.0679739}, {"type": "layer_end", "layer": 0, "duration": 11.001521587371826, "timestamp": 1770953281.103854}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953281.143489}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953281.1829805}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770953281.2225962}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953281.258741}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770953281.3001482}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770953281.3374825}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770953281.3754063}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 28036.79 mol/s", "timestamp": 1770953281.417971}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0010700225830078125, "speed": 28036.791443850267, "timestamp": 1770953281.457579}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770953281.497353}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770953281.530247}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770953281.5642176}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770953281.6006675}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953283.3520546}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0730", "timestamp": 1770953283.3905487}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07297605742684013, "timestamp": 1770953283.4276886}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953283.463065}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953283.4994886}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]"], "timestamp": 1770953283.5334628}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953283.57025}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0730", "timestamp": 1770953283.6048055}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.30", "timestamp": 1770953283.640594}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770953283.6743863}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770953283.709337}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 2.45s) ==========", "timestamp": 1770953283.7493048}, {"type": "layer_end", "layer": 1, "duration": 2.448314666748047, "timestamp": 1770953283.78761}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953283.8264654}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953283.8655283}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770953283.903039}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953283.9392662}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770953283.974971}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770953284.0104537}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770953284.1194143}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 29814.50 mol/s", "timestamp": 1770953284.157669}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0008385181427001953, "speed": 29814.500995166334, "timestamp": 1770953284.1945128}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770953284.2302656}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770953284.276458}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770953284.3181567}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770953284.3542957}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953285.8234198}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2044", "timestamp": 1770953285.8585572}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20444421913006566, "timestamp": 1770953285.8956306}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953285.9288738}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953285.9629376}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770953285.9956117}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953286.0279758}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2044", "timestamp": 1770953286.0608516}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.00", "timestamp": 1770953286.0953283}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9220", "timestamp": 1770953286.125838}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.0490", "timestamp": 1770953286.157945}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.22s) ==========", "timestamp": 1770953286.1965098}, {"type": "layer_end", "layer": 2, "duration": 2.221111536026001, "timestamp": 1770953286.2296607}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953286.2601993}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953286.294183}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770953286.3281257}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953286.3618567}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770953286.3986897}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770953286.4382348}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770953286.4829655}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 28168.60 mol/s", "timestamp": 1770953286.523128}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0007100105285644531, "speed": 28168.59637340497, "timestamp": 1770953286.556295}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770953286.5902662}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770953286.6234193}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770953286.6569364}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770953286.6885638}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953287.9672992}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H], Score: 0.2348", "timestamp": 1770953288.0039172}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "best_score": 0.2348158244365576, "timestamp": 1770953288.0355735}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953288.0649638}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953288.106741}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770953288.1374426}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770953288.1680956}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2348", "timestamp": 1770953288.201458}, {"type": "log", "level": "info", "message": "INFO -     体积: 126.20", "timestamp": 1770953288.2365143}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.7330", "timestamp": 1770953288.2693179}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.6940", "timestamp": 1770953288.301372}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 1.94s) ==========", "timestamp": 1770953288.336594}, {"type": "layer_end", "layer": 3, "duration": 1.9379379749298096, "timestamp": 1770953288.3687015}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953288.401899}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953288.436598}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770953288.473994}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953288.5125456}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770953288.549384}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770953288.586081}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770953288.6253552}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 23598.86 mol/s", "timestamp": 1770953288.6645424}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0006356239318847656, "speed": 23598.85971492873, "timestamp": 1770953288.6977115}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770953288.7333279}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770953288.7697644}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770953288.804706}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770953288.8398778}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953289.7701201}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0727", "timestamp": 1770953289.8074036}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07268523293941818, "timestamp": 1770953289.843264}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953289.8866537}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953289.9260116}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770953289.968649}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953290.0107691}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0727", "timestamp": 1770953290.0492682}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.27", "timestamp": 1770953290.0855672}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770953290.1220984}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770953290.1573398}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 1.65s) ==========", "timestamp": 1770953290.1974964}, {"type": "layer_end", "layer": 4, "duration": 1.6478540897369385, "timestamp": 1770953290.231662}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953290.2662358}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953290.2994199}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770953290.3339887}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953290.3694563}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770953290.406619}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770953290.4409397}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770953290.479754}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 30109.86 mol/s", "timestamp": 1770953290.519133}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.00033211708068847656, "speed": 30109.86360373295, "timestamp": 1770953290.554438}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770953290.588174}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770953290.6239822}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770953290.6633673}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770953290.6961782}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953291.3948958}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H], Score: 0.2060", "timestamp": 1770953291.4297626}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "best_score": 0.20603198431981037, "timestamp": 1770953291.462056}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953291.504835}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953291.5434082}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770953291.5799308}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770953291.6107857}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2060", "timestamp": 1770953291.6444063}, {"type": "log", "level": "info", "message": "INFO -     体积: 126.67", "timestamp": 1770953291.6766505}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.8190", "timestamp": 1770953291.7095368}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.7220", "timestamp": 1770953291.7412858}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.37s) ==========", "timestamp": 1770953291.7769477}, {"type": "layer_end", "layer": 5, "duration": 1.369675874710083, "timestamp": 1770953291.8113573}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953291.8440855}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953291.876027}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770953291.9073327}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953291.943227}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770953291.9788868}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770953292.0159025}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770953292.058746}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 17549.39 mol/s", "timestamp": 1770953292.0995636}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0002849102020263672, "speed": 17549.38912133891, "timestamp": 1770953292.1457124}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770953292.191252}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770953292.230372}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770953292.2662838}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770953292.3028262}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770953292.70922}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2076", "timestamp": 1770953292.7444453}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20761057632044555, "timestamp": 1770953292.7774577}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770953292.8126886}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770953292.8447351}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770953292.876193}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953292.9071531}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2076", "timestamp": 1770953292.9415295}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770953292.974863}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9230", "timestamp": 1770953293.0080678}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.0890", "timestamp": 1770953293.041443}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.11s) ==========", "timestamp": 1770953293.0870597}, {"type": "layer_end", "layer": 6, "duration": 1.1072309017181396, "timestamp": 1770953293.130641}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770953293.544974}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953293.590761}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770953293.6310375}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 23.37 秒", "timestamp": 1770953293.6703644}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770953293.707469}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770953293.7436287}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770953293.7782848}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953293.813735}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2336, 体积: 129.29, 扩散: 5.5850, 吸附: -50.7380", "timestamp": 1770953293.8507853}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770953293.8862073}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2505, 体积: 110.35, 扩散: 4.9450, 吸附: -49.2300", "timestamp": 1770953293.9223013}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770953293.957061}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2697, 体积: 127.38, 扩散: 5.2560, 吸附: -50.5990", "timestamp": 1770953294.018368}, {"type": "log", "level": "info", "message": "INFO -   4. [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770953294.1778517}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2781, 体积: 126.41, 扩散: 5.5600, 吸附: -49.4450", "timestamp": 1770953294.3494086}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770953294.530671}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3118, 体积: 128.86, 扩散: 6.2450, 吸附: -51.3750", "timestamp": 1770953294.7057703}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.67s", "timestamp": 1770953294.8782136}, {"type": "finish", "total_results": 5, "duration": 26.670941591262817, "timestamp": 1770953295.0352867}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/98b17c94-b0e2-495e-8742-8db736861f02.json", "timestamp": 1770953295.1944492}, {"type": "log", "level": "info", "message": "远程日志已上传: 98b17c94-b0e2-495e-8742-8db736861f02", "timestamp": 1770953295.4614801}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770953295.5678735}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770953295.665775}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770953295.8035958}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770953295.8431554}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770953295.8842347}, {"type": "log", "level": "info", "message": "远程日志已启动: 98b17c94-b0e2-495e-8742-8db736861f02", "timestamp": 1770953295.9325778}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 27.75s", "timestamp": 1770953295.9590724}, {"type": "finish", "total_results": 5, "duration": 27.751793384552002, "timestamp": 1770953296.036182}]}