{"metadata": {"log_name": "2446319d-0888-41d8-b98e-d93eebdd2931", "start_time": 1770966075.7549708, "end_time": 1770966105.1603677, "duration": 29.405396938323975, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.20000000000003, "diffusion": 5.546, "adsorption": -51.626}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770966077.6072037, "end_time": 1770966088.5864854, "duration": 10.979281663894653, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07244818947852123}, {"layer_num": 1, "start_time": 1770966088.8842788, "end_time": 1770966091.556441, "duration": 2.6721622943878174, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.2505714542814555}, {"layer_num": 2, "start_time": 1770966091.8392205, "end_time": 1770966094.1648588, "duration": 2.3256382942199707, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07637746288655255}, {"layer_num": 3, "start_time": 1770966094.4701986, "end_time": 1770966096.4783928, "duration": 2.0081942081451416, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.24723812639728943}, {"layer_num": 4, "start_time": 1770966096.7203863, "end_time": 1770966099.0913565, "duration": 2.3709702491760254, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20858170680235347}, {"layer_num": 5, "start_time": 1770966099.541819, "end_time": 1770966101.4191, "duration": 1.8772809505462646, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07790196379399363}, {"layer_num": 6, "start_time": 1770966101.7829194, "end_time": 1770966103.3101065, "duration": 1.5271871089935303, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08080577504626216}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07244818947852123, "volume": 110.67200000000003, "diffusion": 5.225, "adsorption": -51.449, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.21662119831737828, "volume": 129.09600000000003, "diffusion": 5.75, "adsorption": -51.482, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2577884036344578, "volume": 126.84000000000003, "diffusion": 5.621, "adsorption": -50.021, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2913596290078574, "volume": 122.58400000000003, "diffusion": 4.875, "adsorption": -50.628, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.2997351041677723, "volume": 121.12800000000003, "diffusion": 5.152, "adsorption": -53.855, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3093829672504373, "volume": 138.20000000000005, "diffusion": 5.501, "adsorption": -52.438, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3094342878649154, "volume": 119.25600000000003, "diffusion": 6.195, "adsorption": -49.729, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.3156182001405961, "volume": 137.68000000000004, "diffusion": 5.224, "adsorption": -51.767, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.31910129248128416, "volume": 132.95200000000003, "diffusion": 5.377, "adsorption": -50.212, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.33324319816412956, "volume": 122.66400000000003, "diffusion": 4.61, "adsorption": -52.51, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3386852055575017, "volume": 128.11200000000002, "diffusion": 5.301, "adsorption": -49.355, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.34311243611272224, "volume": 129.25600000000003, "diffusion": 4.907, "adsorption": -50.68, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.35413695133064904, "volume": 127.38400000000003, "diffusion": 4.615, "adsorption": -52.148, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3542697395476234, "volume": 122.88800000000003, "diffusion": 5.418, "adsorption": -55.344, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.358636097476199, "volume": 126.77600000000002, "diffusion": 4.971, "adsorption": -53.425, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3624016010775824, "volume": 128.91200000000003, "diffusion": 6.24, "adsorption": -50.465, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3655784518727554, "volume": 132.62400000000002, "diffusion": 5.056, "adsorption": -50.357, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3660385058053507, "volume": 121.67200000000003, "diffusion": 4.792, "adsorption": -53.794, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.37941364939063665, "volume": 118.89600000000003, "diffusion": 5.184, "adsorption": -47.578, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.3852721615395489, "volume": 134.20800000000003, "diffusion": 4.798, "adsorption": -51.066, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3891029615342425, "volume": 127.18400000000003, "diffusion": 5.15, "adsorption": -48.811, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.39886359285045114, "volume": 126.89600000000003, "diffusion": 5.611, "adsorption": -47.571, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3997492679483289, "volume": 133.86400000000003, "diffusion": 5.402, "adsorption": -54.363, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4213107984988995, "volume": 133.06400000000002, "diffusion": 5.991, "adsorption": -49.327, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.42330353889262545, "volume": 119.48000000000003, "diffusion": 5.87, "adsorption": -46.796, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4237989506017709, "volume": 145.83200000000002, "diffusion": 5.451, "adsorption": -50.192, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.43065285172521395, "volume": 125.68800000000003, "diffusion": 4.794, "adsorption": -54.285, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.4366038450657664, "volume": 150.24800000000005, "diffusion": 5.327, "adsorption": -51.046, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.442726791543041, "volume": 150.76000000000005, "diffusion": 5.391, "adsorption": -50.822, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.445412292167542, "volume": 132.28000000000003, "diffusion": 5.245, "adsorption": -54.909, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4459148706854882, "volume": 127.64800000000002, "diffusion": 4.011, "adsorption": -51.44, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.44994619458219487, "volume": 142.00000000000003, "diffusion": 6.179, "adsorption": -52.439, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.45407461098254553, "volume": 128.81600000000003, "diffusion": 5.245, "adsorption": -55.599, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.464606937005254, "volume": 146.22400000000005, "diffusion": 6.137, "adsorption": -52.162, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.46688866324645945, "volume": 119.00000000000003, "diffusion": 6.249, "adsorption": -47.147, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4777505816119929, "volume": 144.48800000000003, "diffusion": 5.205, "adsorption": -53.435, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.48018201277261297, "volume": 128.98400000000004, "diffusion": 5.128, "adsorption": -55.659, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4951571857116891, "volume": 139.41600000000003, "diffusion": 4.694, "adsorption": -50.311, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.4960538637740395, "volume": 146.59200000000004, "diffusion": 5.755, "adsorption": -53.831, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4994704258390916, "volume": 146.25600000000003, "diffusion": 5.315, "adsorption": -53.874, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.5062448386088452, "volume": 142.64000000000004, "diffusion": 5.264, "adsorption": -54.397, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5088172572383093, "volume": 145.28800000000004, "diffusion": 4.829, "adsorption": -52.678, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5092010543397, "volume": 153.93600000000004, "diffusion": 5.469, "adsorption": -49.932, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.509297990522939, "volume": 140.66400000000004, "diffusion": 6.004, "adsorption": -49.04, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5151671180254496, "volume": 132.28000000000003, "diffusion": 4.697, "adsorption": -48.843, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.5263445554642805, "volume": 135.77600000000004, "diffusion": 5.573, "adsorption": -56.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.5295833417959916, "volume": 128.35200000000003, "diffusion": 5.12, "adsorption": -56.583, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5319873141292686, "volume": 130.89600000000004, "diffusion": 5.662, "adsorption": -46.063, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5352998171776323, "volume": 139.13600000000002, "diffusion": 5.82, "adsorption": -47.783, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.5393118191514323, "volume": 144.58400000000003, "diffusion": 5.92, "adsorption": -48.875, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5596982148563161, "volume": 127.00800000000002, "diffusion": 4.701, "adsorption": -47.241, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5683701878049812, "volume": 129.90400000000002, "diffusion": 5.769, "adsorption": -45.614, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.568428766045068, "volume": 120.00800000000002, "diffusion": 6.443, "adsorption": -46.159, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5737242899269775, "volume": 131.59200000000004, "diffusion": 4.929, "adsorption": -56.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5770308834026585, "volume": 135.96800000000005, "diffusion": 5.974, "adsorption": -47.048, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.608496479644562, "volume": 135.00000000000003, "diffusion": 5.345, "adsorption": -57.601, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.6089108314851697, "volume": 128.77600000000004, "diffusion": 5.699, "adsorption": -58.729, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6094109097899072, "volume": 127.06400000000004, "diffusion": 5.854, "adsorption": -44.728, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6149881208422507, "volume": 129.57600000000002, "diffusion": 4.58, "adsorption": -47.066, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6202750367449318, "volume": 138.97600000000003, "diffusion": 6.785, "adsorption": -49.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.6336774992604688, "volume": 146.16800000000003, "diffusion": 5.731, "adsorption": -56.34, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6416832248954949, "volume": 123.10400000000003, "diffusion": 4.599, "adsorption": -45.537, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.6517042571296915, "volume": 130.08000000000004, "diffusion": 6.523, "adsorption": -46.547, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6595277366717318, "volume": 127.87200000000003, "diffusion": 4.973, "adsorption": -44.814, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.6612720871936197, "volume": 156.32800000000003, "diffusion": 5.695, "adsorption": -47.912, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.668659964289415, "volume": 139.09600000000003, "diffusion": 7.027, "adsorption": -49.227, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.6692974478041257, "volume": 139.39200000000002, "diffusion": 4.67, "adsorption": -55.867, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6747735653565239, "volume": 129.12000000000003, "diffusion": 5.696, "adsorption": -59.818, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6778338824105661, "volume": 138.22400000000005, "diffusion": 5.328, "adsorption": -45.014, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.6852910460963528, "volume": 155.44000000000003, "diffusion": 5.465, "adsorption": -56.103, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7058954530796817, "volume": 130.64800000000002, "diffusion": 6.026, "adsorption": -44.161, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7060545706440851, "volume": 138.70400000000004, "diffusion": 5.506, "adsorption": -44.051, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.7267329596094496, "volume": 138.64800000000002, "diffusion": 5.825, "adsorption": -44.428, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.729274827202511, "volume": 133.96000000000004, "diffusion": 5.366, "adsorption": -43.345, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7353112644346304, "volume": 139.08000000000004, "diffusion": 6.847, "adsorption": -47.519, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7402710735597244, "volume": 154.82400000000004, "diffusion": 4.869, "adsorption": -47.956, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.7488344543454779, "volume": 147.30400000000003, "diffusion": 4.532, "adsorption": -47.68, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.7544691468448852, "volume": 143.38400000000004, "diffusion": 6.804, "adsorption": -47.728, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7568511592301328, "volume": 146.91200000000003, "diffusion": 5.687, "adsorption": -44.772, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.764358763474192, "volume": 143.01600000000005, "diffusion": 5.098, "adsorption": -44.987, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7647393100334542, "volume": 135.32000000000002, "diffusion": 3.909, "adsorption": -47.468, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7738917067192224, "volume": 135.72000000000003, "diffusion": 5.677, "adsorption": -42.7, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7996150418448793, "volume": 133.16800000000003, "diffusion": 5.912, "adsorption": -42.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8050934489851875, "volume": 126.20000000000003, "diffusion": 4.321, "adsorption": -44.071, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.8063666259211704, "volume": 145.64800000000002, "diffusion": 5.274, "adsorption": -59.123, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8232955377630998, "volume": 130.11200000000002, "diffusion": 7.027, "adsorption": -45.163, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8276568839970821, "volume": 151.82400000000004, "diffusion": 5.294, "adsorption": -44.665, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.8284621410028569, "volume": 155.64800000000002, "diffusion": 5.766, "adsorption": -45.149, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8400581862778237, "volume": 141.79200000000003, "diffusion": 6.313, "adsorption": -44.483, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8591664391307146, "volume": 150.28800000000004, "diffusion": 5.935, "adsorption": -44.308, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 0.8665118771314317, "volume": 122.98400000000002, "diffusion": 5.713, "adsorption": -64.023, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.8908713197038057, "volume": 146.28800000000004, "diffusion": 6.326, "adsorption": -44.351, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.9274648140505287, "volume": 156.20000000000005, "diffusion": 5.428, "adsorption": -43.215, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.9500581652052027, "volume": 131.33600000000004, "diffusion": 6.398, "adsorption": -41.221, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9568297331287676, "volume": 138.82400000000004, "diffusion": 4.896, "adsorption": -41.647, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9570823624085222, "volume": 132.20000000000005, "diffusion": 5.365, "adsorption": -39.153, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.0038089048494547, "volume": 140.39200000000002, "diffusion": 4.657, "adsorption": -41.825, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0287972342990166, "volume": 134.74400000000003, "diffusion": 6.124, "adsorption": -39.548, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 1.0394008868882927, "volume": 135.75200000000004, "diffusion": 5.94, "adsorption": -38.952, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1218290479912005, "volume": 136.56000000000003, "diffusion": 6.079, "adsorption": -38.091, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.1260875612004098, "volume": 155.85600000000002, "diffusion": 6.303, "adsorption": -41.726, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.148079913661763, "volume": 154.20800000000003, "diffusion": 5.65, "adsorption": -39.064, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.1740276946855785, "volume": 151.10400000000004, "diffusion": 4.944, "adsorption": -39.678, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2123951004246107, "volume": 155.59200000000004, "diffusion": 4.629, "adsorption": -40.696, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2129476355341398, "volume": 156.90400000000002, "diffusion": 5.206, "adsorption": -39.101, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.213209831463889, "volume": 147.41600000000003, "diffusion": 5.625, "adsorption": -36.805, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2233055091455518, "volume": 145.57600000000002, "diffusion": 6.821, "adsorption": -40.055, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.3049679016768998, "volume": 136.80800000000002, "diffusion": 5.238, "adsorption": -34.28, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.385395558432886, "volume": 145.79200000000003, "diffusion": 6.691, "adsorption": -36.896, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2505714542814555, "volume": 110.11200000000002, "diffusion": 5.077, "adsorption": -48.783, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.26297857777495154, "volume": 122.60800000000003, "diffusion": 4.857, "adsorption": -52.076, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.33896857350672494, "volume": 129.86400000000003, "diffusion": 5.959, "adsorption": -50.145, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.34178037255747507, "volume": 121.61600000000003, "diffusion": 5.139, "adsorption": -54.462, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4059086587107664, "volume": 126.64000000000003, "diffusion": 5.788, "adsorption": -47.959, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07637746288655255, "volume": 110.52000000000002, "diffusion": 5.241, "adsorption": -51.308, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.22301727405193883, "volume": 129.32800000000003, "diffusion": 5.729, "adsorption": -51.909, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2335768853737945, "volume": 122.88800000000003, "diffusion": 4.922, "adsorption": -51.524, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.292185194911874, "volume": 120.29600000000003, "diffusion": 5.152, "adsorption": -53.855, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.29860918982805623, "volume": 126.28000000000003, "diffusion": 5.766, "adsorption": -49.681, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24723812639728943, "volume": 129.50400000000002, "diffusion": 5.703, "adsorption": -52.379, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.25303969384588376, "volume": 110.56000000000003, "diffusion": 5.077, "adsorption": -48.783, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.27990585771756654, "volume": 126.38400000000003, "diffusion": 5.697, "adsorption": -49.805, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.4439997482772201, "volume": 122.67200000000003, "diffusion": 4.671, "adsorption": -48.648, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.8754995851483309, "volume": 121.20800000000003, "diffusion": 5.136, "adsorption": -63.701, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.20858170680235347, "volume": 110.68800000000003, "diffusion": 4.922, "adsorption": -50.049, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24486956273574356, "volume": 123.00000000000003, "diffusion": 5.011, "adsorption": -52.181, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2785332653611558, "volume": 126.56800000000003, "diffusion": 5.713, "adsorption": -49.907, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3399123121637121, "volume": 129.96800000000002, "diffusion": 5.959, "adsorption": -50.145, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.7678077196342321, "volume": 121.36800000000002, "diffusion": 5.177, "adsorption": -61.95, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07790196379399363, "volume": 110.68800000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.236684953259016, "volume": 129.89600000000004, "diffusion": 5.627, "adsorption": -50.88, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2951768409588319, "volume": 123.20000000000003, "diffusion": 4.773, "adsorption": -50.975, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.45935170702250294, "volume": 126.33600000000003, "diffusion": 6.256, "adsorption": -48.444, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.6302586440654423, "volume": 121.04000000000003, "diffusion": 5.01, "adsorption": -59.116, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.08080577504626216, "volume": 111.00800000000002, "diffusion": 5.241, "adsorption": -51.308, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.24456286646978334, "volume": 126.40000000000003, "diffusion": 5.661, "adsorption": -50.304, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24822469106753572, "volume": 123.24800000000003, "diffusion": 4.904, "adsorption": -51.384, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2645007560465395, "volume": 129.45600000000002, "diffusion": 5.621, "adsorption": -50.314, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.63185574025419, "volume": 121.21600000000002, "diffusion": 5.01, "adsorption": -59.116, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.20000000000003, "diffusion": 5.546, "adsorption": -51.626}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.08080577504626216, "volume": 111.00800000000002, "diffusion": 5.241, "adsorption": -51.308, "is_selected": false, "rank": 1}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.2645007560465395, "volume": 129.45600000000002, "diffusion": 5.621, "adsorption": -50.314, "is_selected": false, "rank": 4}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.24456286646978334, "volume": 126.40000000000003, "diffusion": 5.661, "adsorption": -50.304, "is_selected": false, "rank": 2}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.24822469106753572, "volume": 123.24800000000003, "diffusion": 4.904, "adsorption": -51.384, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 6, "score": 0.63185574025419, "volume": 121.21600000000002, "diffusion": 5.01, "adsorption": -59.116, "is_selected": false, "rank": 5}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3093829672504373, "volume": 138.20000000000005, "diffusion": 5.501, "adsorption": -52.438, "is_selected": false, "rank": 6}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3094342878649154, "volume": 119.25600000000003, "diffusion": 6.195, "adsorption": -49.729, "is_selected": false, "rank": 7}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3156182001405961, "volume": 137.68000000000004, "diffusion": 5.224, "adsorption": -51.767, "is_selected": false, "rank": 8}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.31910129248128416, "volume": 132.95200000000003, "diffusion": 5.377, "adsorption": -50.212, "is_selected": false, "rank": 9}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.33324319816412956, "volume": 122.66400000000003, "diffusion": 4.61, "adsorption": -52.51, "is_selected": false, "rank": 10}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3386852055575017, "volume": 128.11200000000002, "diffusion": 5.301, "adsorption": -49.355, "is_selected": false, "rank": 11}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.34311243611272224, "volume": 129.25600000000003, "diffusion": 4.907, "adsorption": -50.68, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.35413695133064904, "volume": 127.38400000000003, "diffusion": 4.615, "adsorption": -52.148, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.3542697395476234, "volume": 122.88800000000003, "diffusion": 5.418, "adsorption": -55.344, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.358636097476199, "volume": 126.77600000000002, "diffusion": 4.971, "adsorption": -53.425, "is_selected": false, "rank": 15}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3624016010775824, "volume": 128.91200000000003, "diffusion": 6.24, "adsorption": -50.465, "is_selected": false, "rank": 16}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3655784518727554, "volume": 132.62400000000002, "diffusion": 5.056, "adsorption": -50.357, "is_selected": false, "rank": 17}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3660385058053507, "volume": 121.67200000000003, "diffusion": 4.792, "adsorption": -53.794, "is_selected": false, "rank": 18}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.37941364939063665, "volume": 118.89600000000003, "diffusion": 5.184, "adsorption": -47.578, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.3852721615395489, "volume": 134.20800000000003, "diffusion": 4.798, "adsorption": -51.066, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.3891029615342425, "volume": 127.18400000000003, "diffusion": 5.15, "adsorption": -48.811, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.39886359285045114, "volume": 126.89600000000003, "diffusion": 5.611, "adsorption": -47.571, "is_selected": false, "rank": 22}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3997492679483289, "volume": 133.86400000000003, "diffusion": 5.402, "adsorption": -54.363, "is_selected": false, "rank": 23}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4213107984988995, "volume": 133.06400000000002, "diffusion": 5.991, "adsorption": -49.327, "is_selected": false, "rank": 24}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.42330353889262545, "volume": 119.48000000000003, "diffusion": 5.87, "adsorption": -46.796, "is_selected": false, "rank": 25}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4237989506017709, "volume": 145.83200000000002, "diffusion": 5.451, "adsorption": -50.192, "is_selected": false, "rank": 26}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.43065285172521395, "volume": 125.68800000000003, "diffusion": 4.794, "adsorption": -54.285, "is_selected": false, "rank": 27}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4366038450657664, "volume": 150.24800000000005, "diffusion": 5.327, "adsorption": -51.046, "is_selected": false, "rank": 28}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.442726791543041, "volume": 150.76000000000005, "diffusion": 5.391, "adsorption": -50.822, "is_selected": false, "rank": 29}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.445412292167542, "volume": 132.28000000000003, "diffusion": 5.245, "adsorption": -54.909, "is_selected": false, "rank": 30}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4459148706854882, "volume": 127.64800000000002, "diffusion": 4.011, "adsorption": -51.44, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.44994619458219487, "volume": 142.00000000000003, "diffusion": 6.179, "adsorption": -52.439, "is_selected": false, "rank": 32}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.45407461098254553, "volume": 128.81600000000003, "diffusion": 5.245, "adsorption": -55.599, "is_selected": false, "rank": 33}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.464606937005254, "volume": 146.22400000000005, "diffusion": 6.137, "adsorption": -52.162, "is_selected": false, "rank": 34}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.46688866324645945, "volume": 119.00000000000003, "diffusion": 6.249, "adsorption": -47.147, "is_selected": false, "rank": 35}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4777505816119929, "volume": 144.48800000000003, "diffusion": 5.205, "adsorption": -53.435, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.48018201277261297, "volume": 128.98400000000004, "diffusion": 5.128, "adsorption": -55.659, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4951571857116891, "volume": 139.41600000000003, "diffusion": 4.694, "adsorption": -50.311, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4960538637740395, "volume": 146.59200000000004, "diffusion": 5.755, "adsorption": -53.831, "is_selected": false, "rank": 39}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4994704258390916, "volume": 146.25600000000003, "diffusion": 5.315, "adsorption": -53.874, "is_selected": false, "rank": 40}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5062448386088452, "volume": 142.64000000000004, "diffusion": 5.264, "adsorption": -54.397, "is_selected": false, "rank": 41}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5088172572383093, "volume": 145.28800000000004, "diffusion": 4.829, "adsorption": -52.678, "is_selected": false, "rank": 42}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5092010543397, "volume": 153.93600000000004, "diffusion": 5.469, "adsorption": -49.932, "is_selected": false, "rank": 43}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.509297990522939, "volume": 140.66400000000004, "diffusion": 6.004, "adsorption": -49.04, "is_selected": false, "rank": 44}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5151671180254496, "volume": 132.28000000000003, "diffusion": 4.697, "adsorption": -48.843, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.5263445554642805, "volume": 135.77600000000004, "diffusion": 5.573, "adsorption": -56.606, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.5295833417959916, "volume": 128.35200000000003, "diffusion": 5.12, "adsorption": -56.583, "is_selected": false, "rank": 47}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5319873141292686, "volume": 130.89600000000004, "diffusion": 5.662, "adsorption": -46.063, "is_selected": false, "rank": 48}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5352998171776323, "volume": 139.13600000000002, "diffusion": 5.82, "adsorption": -47.783, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.5393118191514323, "volume": 144.58400000000003, "diffusion": 5.92, "adsorption": -48.875, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5596982148563161, "volume": 127.00800000000002, "diffusion": 4.701, "adsorption": -47.241, "is_selected": false, "rank": 51}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5683701878049812, "volume": 129.90400000000002, "diffusion": 5.769, "adsorption": -45.614, "is_selected": false, "rank": 52}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.568428766045068, "volume": 120.00800000000002, "diffusion": 6.443, "adsorption": -46.159, "is_selected": false, "rank": 53}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5737242899269775, "volume": 131.59200000000004, "diffusion": 4.929, "adsorption": -56.244, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5770308834026585, "volume": 135.96800000000005, "diffusion": 5.974, "adsorption": -47.048, "is_selected": false, "rank": 55}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.608496479644562, "volume": 135.00000000000003, "diffusion": 5.345, "adsorption": -57.601, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.6089108314851697, "volume": 128.77600000000004, "diffusion": 5.699, "adsorption": -58.729, "is_selected": false, "rank": 57}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6094109097899072, "volume": 127.06400000000004, "diffusion": 5.854, "adsorption": -44.728, "is_selected": false, "rank": 58}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6149881208422507, "volume": 129.57600000000002, "diffusion": 4.58, "adsorption": -47.066, "is_selected": false, "rank": 59}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6202750367449318, "volume": 138.97600000000003, "diffusion": 6.785, "adsorption": -49.29, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6336774992604688, "volume": 146.16800000000003, "diffusion": 5.731, "adsorption": -56.34, "is_selected": false, "rank": 61}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6416832248954949, "volume": 123.10400000000003, "diffusion": 4.599, "adsorption": -45.537, "is_selected": false, "rank": 62}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.6517042571296915, "volume": 130.08000000000004, "diffusion": 6.523, "adsorption": -46.547, "is_selected": false, "rank": 63}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6595277366717318, "volume": 127.87200000000003, "diffusion": 4.973, "adsorption": -44.814, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6612720871936197, "volume": 156.32800000000003, "diffusion": 5.695, "adsorption": -47.912, "is_selected": false, "rank": 65}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.668659964289415, "volume": 139.09600000000003, "diffusion": 7.027, "adsorption": -49.227, "is_selected": false, "rank": 66}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.6692974478041257, "volume": 139.39200000000002, "diffusion": 4.67, "adsorption": -55.867, "is_selected": false, "rank": 67}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6747735653565239, "volume": 129.12000000000003, "diffusion": 5.696, "adsorption": -59.818, "is_selected": false, "rank": 68}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6778338824105661, "volume": 138.22400000000005, "diffusion": 5.328, "adsorption": -45.014, "is_selected": false, "rank": 69}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.6852910460963528, "volume": 155.44000000000003, "diffusion": 5.465, "adsorption": -56.103, "is_selected": false, "rank": 70}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7058954530796817, "volume": 130.64800000000002, "diffusion": 6.026, "adsorption": -44.161, "is_selected": false, "rank": 71}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7060545706440851, "volume": 138.70400000000004, "diffusion": 5.506, "adsorption": -44.051, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.7267329596094496, "volume": 138.64800000000002, "diffusion": 5.825, "adsorption": -44.428, "is_selected": false, "rank": 73}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.729274827202511, "volume": 133.96000000000004, "diffusion": 5.366, "adsorption": -43.345, "is_selected": false, "rank": 74}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7353112644346304, "volume": 139.08000000000004, "diffusion": 6.847, "adsorption": -47.519, "is_selected": false, "rank": 75}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7402710735597244, "volume": 154.82400000000004, "diffusion": 4.869, "adsorption": -47.956, "is_selected": false, "rank": 76}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.7488344543454779, "volume": 147.30400000000003, "diffusion": 4.532, "adsorption": -47.68, "is_selected": false, "rank": 77}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.7544691468448852, "volume": 143.38400000000004, "diffusion": 6.804, "adsorption": -47.728, "is_selected": false, "rank": 78}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7568511592301328, "volume": 146.91200000000003, "diffusion": 5.687, "adsorption": -44.772, "is_selected": false, "rank": 79}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.764358763474192, "volume": 143.01600000000005, "diffusion": 5.098, "adsorption": -44.987, "is_selected": false, "rank": 80}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7647393100334542, "volume": 135.32000000000002, "diffusion": 3.909, "adsorption": -47.468, "is_selected": false, "rank": 81}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7738917067192224, "volume": 135.72000000000003, "diffusion": 5.677, "adsorption": -42.7, "is_selected": false, "rank": 82}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7996150418448793, "volume": 133.16800000000003, "diffusion": 5.912, "adsorption": -42.588, "is_selected": false, "rank": 83}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8050934489851875, "volume": 126.20000000000003, "diffusion": 4.321, "adsorption": -44.071, "is_selected": false, "rank": 84}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.8063666259211704, "volume": 145.64800000000002, "diffusion": 5.274, "adsorption": -59.123, "is_selected": false, "rank": 85}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8232955377630998, "volume": 130.11200000000002, "diffusion": 7.027, "adsorption": -45.163, "is_selected": false, "rank": 86}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8276568839970821, "volume": 151.82400000000004, "diffusion": 5.294, "adsorption": -44.665, "is_selected": false, "rank": 87}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.8284621410028569, "volume": 155.64800000000002, "diffusion": 5.766, "adsorption": -45.149, "is_selected": false, "rank": 88}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8400581862778237, "volume": 141.79200000000003, "diffusion": 6.313, "adsorption": -44.483, "is_selected": false, "rank": 89}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8591664391307146, "volume": 150.28800000000004, "diffusion": 5.935, "adsorption": -44.308, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8665118771314317, "volume": 122.98400000000002, "diffusion": 5.713, "adsorption": -64.023, "is_selected": false, "rank": 91}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8908713197038057, "volume": 146.28800000000004, "diffusion": 6.326, "adsorption": -44.351, "is_selected": false, "rank": 92}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.9274648140505287, "volume": 156.20000000000005, "diffusion": 5.428, "adsorption": -43.215, "is_selected": false, "rank": 93}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.9500581652052027, "volume": 131.33600000000004, "diffusion": 6.398, "adsorption": -41.221, "is_selected": false, "rank": 94}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9568297331287676, "volume": 138.82400000000004, "diffusion": 4.896, "adsorption": -41.647, "is_selected": false, "rank": 95}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9570823624085222, "volume": 132.20000000000005, "diffusion": 5.365, "adsorption": -39.153, "is_selected": false, "rank": 96}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.0038089048494547, "volume": 140.39200000000002, "diffusion": 4.657, "adsorption": -41.825, "is_selected": false, "rank": 97}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0287972342990166, "volume": 134.74400000000003, "diffusion": 6.124, "adsorption": -39.548, "is_selected": false, "rank": 98}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 1.0394008868882927, "volume": 135.75200000000004, "diffusion": 5.94, "adsorption": -38.952, "is_selected": false, "rank": 99}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1218290479912005, "volume": 136.56000000000003, "diffusion": 6.079, "adsorption": -38.091, "is_selected": false, "rank": 100}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.1260875612004098, "volume": 155.85600000000002, "diffusion": 6.303, "adsorption": -41.726, "is_selected": false, "rank": 101}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.148079913661763, "volume": 154.20800000000003, "diffusion": 5.65, "adsorption": -39.064, "is_selected": false, "rank": 102}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.1740276946855785, "volume": 151.10400000000004, "diffusion": 4.944, "adsorption": -39.678, "is_selected": false, "rank": 103}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2123951004246107, "volume": 155.59200000000004, "diffusion": 4.629, "adsorption": -40.696, "is_selected": false, "rank": 104}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2129476355341398, "volume": 156.90400000000002, "diffusion": 5.206, "adsorption": -39.101, "is_selected": false, "rank": 105}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.213209831463889, "volume": 147.41600000000003, "diffusion": 5.625, "adsorption": -36.805, "is_selected": false, "rank": 106}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2233055091455518, "volume": 145.57600000000002, "diffusion": 6.821, "adsorption": -40.055, "is_selected": false, "rank": 107}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.3049679016768998, "volume": 136.80800000000002, "diffusion": 5.238, "adsorption": -34.28, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.385395558432886, "volume": 145.79200000000003, "diffusion": 6.691, "adsorption": -36.896, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770966075.7619014}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 2446319d-0888-41d8-b98e-d93eebdd2931", "timestamp": 1770966075.8571424}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/2446319d-0888-41d8-b98e-d93eebdd2931.log", "timestamp": 1770966075.9112687}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966076.419728}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.2000, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770966076.447265}, {"type": "log", "level": "info", "message": "远程日志已启动: 2446319d-0888-41d8-b98e-d93eebdd2931", "timestamp": 1770966076.4970148}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770966076.5337634}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966076.6045835}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770966076.6665}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770966076.741566}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.20, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770966076.8197415}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770966076.8995261}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966076.9793954}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966077.0157495}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.2000, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770966077.0383377}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770966077.0616713}, {"type": "log", "level": "info", "message": "远程日志已启动: 2446319d-0888-41d8-b98e-d93eebdd2931", "timestamp": 1770966077.0876093}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770966077.1501706}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770966077.2232234}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770966077.2508643}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966077.32963}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966077.4084728}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770966077.5022151}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966077.5801988}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770966077.608918}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.07 秒", "timestamp": 1770966077.772395}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770966077.8477418}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.07s, 速度: 2393.22 mol/s", "timestamp": 1770966077.870183}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.0718696117401123, "speed": 2393.2228912265336, "timestamp": 1770966077.8883154}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770966077.9861438}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770966078.103575}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770966078.168717}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770966078.1918116}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770966078.2139919}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770966078.2773492}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770966088.0955038}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0724", "timestamp": 1770966088.1170208}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07244818947852123, "timestamp": 1770966088.1357741}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966088.2031627}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966088.227538}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]"], "timestamp": 1770966088.2497687}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966088.319492}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0724", "timestamp": 1770966088.388358}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.67", "timestamp": 1770966088.450096}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2250", "timestamp": 1770966088.511772}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.4490", "timestamp": 1770966088.5666585}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.98s) ==========", "timestamp": 1770966088.5872526}, {"type": "layer_end", "layer": 0, "duration": 10.979281663894653, "timestamp": 1770966088.609301}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966088.6763737}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966088.7385375}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770966088.8029766}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966088.8632112}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770966088.8845313}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770966088.953122}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770966089.0135016}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 13516.93 mol/s", "timestamp": 1770966089.030252}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0022194385528564453, "speed": 13516.932001289075, "timestamp": 1770966089.0476015}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770966089.1080256}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770966089.1262665}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770966089.1453235}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770966089.2121234}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966091.0355525}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2506", "timestamp": 1770966091.0569835}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.2505714542814555, "timestamp": 1770966091.0769048}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966091.1361885}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966091.1577098}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770966091.1799076}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966091.2485356}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2506", "timestamp": 1770966091.3212872}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.11", "timestamp": 1770966091.3858888}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0770", "timestamp": 1770966091.447551}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.7830", "timestamp": 1770966091.5312614}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 2.67s) ==========", "timestamp": 1770966091.557268}, {"type": "layer_end", "layer": 1, "duration": 2.6721622943878174, "timestamp": 1770966091.5782824}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966091.643472}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966091.703872}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770966091.7598734}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966091.8187954}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770966091.8394616}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770966091.913153}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770966091.9769452}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 26032.17 mol/s", "timestamp": 1770966091.9946842}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0009603500366210938, "speed": 26032.17477656405, "timestamp": 1770966092.0159457}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770966092.0880811}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770966092.1099575}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770966092.128365}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770966092.1877427}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966093.683985}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0764", "timestamp": 1770966093.703507}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07637746288655255, "timestamp": 1770966093.7210455}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966093.7761328}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966093.794168}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770966093.8160505}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966093.8800282}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0764", "timestamp": 1770966093.943733}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.52", "timestamp": 1770966094.0066097}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770966094.0784066}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770966094.1430979}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.33s) ==========", "timestamp": 1770966094.1654825}, {"type": "layer_end", "layer": 2, "duration": 2.3256382942199707, "timestamp": 1770966094.183817}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966094.2434933}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966094.3004873}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770966094.3909383}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966094.4526162}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770966094.4704306}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770966094.5288746}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770966094.5896354}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 30885.89 mol/s", "timestamp": 1770966094.6067576}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0006475448608398438, "speed": 30885.891016200294, "timestamp": 1770966094.6489553}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770966094.718266}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770966094.7385616}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770966094.8009179}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770966094.8551104}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966096.0636823}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2472", "timestamp": 1770966096.0808022}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.24723812639728943, "timestamp": 1770966096.0973215}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966096.1488545}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966096.1668894}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]"], "timestamp": 1770966096.1844616}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966096.2390535}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2472", "timestamp": 1770966096.2943704}, {"type": "log", "level": "info", "message": "INFO -     体积: 129.50", "timestamp": 1770966096.3485498}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.7030", "timestamp": 1770966096.4066641}, {"type": "log", "level": "info", "message": "INFO -     吸附: -52.3790", "timestamp": 1770966096.4615788}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.01s) ==========", "timestamp": 1770966096.4791355}, {"type": "layer_end", "layer": 3, "duration": 2.0081942081451416, "timestamp": 1770966096.4981625}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966096.5495958}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966096.601125}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770966096.6528}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966096.7029138}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770966096.7206352}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770966096.7743142}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770966096.8344305}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 29100.17 mol/s", "timestamp": 1770966096.8576293}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0005154609680175781, "speed": 29100.166512488435, "timestamp": 1770966096.8786554}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770966096.9412181}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770966096.9587958}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770966096.9735734}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770966097.023483}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966097.9318576}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2086", "timestamp": 1770966097.9488382}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20858170680235347, "timestamp": 1770966097.969499}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966098.0274682}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966098.04495}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]"], "timestamp": 1770966098.0633907}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966098.1180499}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2086", "timestamp": 1770966098.168763}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770966098.2278998}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9220", "timestamp": 1770966098.5028527}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.0490", "timestamp": 1770966099.0674248}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 2.37s) ==========", "timestamp": 1770966099.0921433}, {"type": "layer_end", "layer": 4, "duration": 2.3709702491760254, "timestamp": 1770966099.1153886}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966099.1896932}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966099.2826445}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770966099.397283}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966099.513115}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770966099.5420358}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770966099.644643}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770966099.7543955}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 14074.85 mol/s", "timestamp": 1770966099.7769337}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0007104873657226562, "speed": 14074.845637583892, "timestamp": 1770966099.8059292}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770966099.9163594}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770966099.948333}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770966099.9872706}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770966100.0888557}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966100.8448732}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0779", "timestamp": 1770966100.864459}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07790196379399363, "timestamp": 1770966100.8878186}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966100.9547453}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966100.9757683}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]"], "timestamp": 1770966100.9937415}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966101.0656555}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0779", "timestamp": 1770966101.1566625}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770966101.2218883}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770966101.2935011}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770966101.3853579}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.88s) ==========", "timestamp": 1770966101.4208562}, {"type": "layer_end", "layer": 5, "duration": 1.8772809505462646, "timestamp": 1770966101.4470627}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966101.5292966}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966101.6301951}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770966101.697292}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966101.7619996}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770966101.7831907}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770966101.8478189}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770966101.9126747}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 13273.11 mol/s", "timestamp": 1770966101.9332707}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.00037670135498046875, "speed": 13273.113924050633, "timestamp": 1770966101.9540303}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770966102.0259023}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770966102.0514698}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770966102.0737133}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770966102.1386118}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966102.6407573}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0808", "timestamp": 1770966102.6696954}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08080577504626216, "timestamp": 1770966102.7044168}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966102.8322616}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966102.8600528}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]"], "timestamp": 1770966102.8891006}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966102.9741554}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0808", "timestamp": 1770966103.0589523}, {"type": "log", "level": "info", "message": "INFO -     体积: 111.01", "timestamp": 1770966103.1420808}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770966103.2300985}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770966103.2901852}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.53s) ==========", "timestamp": 1770966103.3104234}, {"type": "layer_end", "layer": 6, "duration": 1.5271871089935303, "timestamp": 1770966103.3291073}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966103.7674372}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966103.8264434}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770966103.8926873}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 26.08 秒", "timestamp": 1770966103.9581962}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770966104.0210285}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966104.08251}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770966104.1538846}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966104.219425}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2044, 体积: 110.23, 扩散: 4.9220, 吸附: -50.0490", "timestamp": 1770966104.2928493}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770966104.3662102}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2308, 体积: 122.61, 扩散: 4.9070, 吸附: -51.6780", "timestamp": 1770966104.4260736}, {"type": "log", "level": "info", "message": "INFO -   3. [H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770966104.49456}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2499, 体积: 125.98, 扩散: 5.6210, 吸附: -50.0210", "timestamp": 1770966104.5598893}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "timestamp": 1770966104.6266665}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2804, 体积: 121.53, 扩散: 5.1580, 吸附: -53.4790", "timestamp": 1770966104.6948006}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966104.7544692}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3358, 体积: 129.51, 扩散: 5.9590, 吸附: -50.1450", "timestamp": 1770966104.8230598}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 29.09s", "timestamp": 1770966104.8438196}, {"type": "finish", "total_results": 5, "duration": 29.088597536087036, "timestamp": 1770966104.861026}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/2446319d-0888-41d8-b98e-d93eebdd2931.json", "timestamp": 1770966104.884796}, {"type": "log", "level": "info", "message": "远程日志已上传: 2446319d-0888-41d8-b98e-d93eebdd2931", "timestamp": 1770966104.916152}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770966104.9662588}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770966105.0231788}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966105.0428905}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.2000, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770966105.0619526}, {"type": "log", "level": "info", "message": "远程日志已启动: 2446319d-0888-41d8-b98e-d93eebdd2931", "timestamp": 1770966105.1066535}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770966105.1382194}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 29.41s", "timestamp": 1770966105.1605775}, {"type": "finish", "total_results": 5, "duration": 29.405396938323975, "timestamp": 1770966105.1831214}]}