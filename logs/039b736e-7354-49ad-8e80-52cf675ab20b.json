{"metadata": {"log_name": "039b736e-7354-49ad-8e80-52cf675ab20b", "start_time": 1770967836.349102, "end_time": 1770967878.6514575, "duration": 42.30235552787781, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 111.05600000000003, "diffusion": 5.213, "adsorption": -51.696}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770967839.3702562, "end_time": 1770967852.1007648, "duration": 12.73050856590271, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0050425010805359655}, {"layer_num": 1, "start_time": 1770967852.8659918, "end_time": 1770967856.837848, "duration": 3.971856117248535, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.020988914879896504}, {"layer_num": 2, "start_time": 1770967857.635656, "end_time": 1770967860.976544, "duration": 3.3408877849578857, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.03365029463319899}, {"layer_num": 3, "start_time": 1770967861.5266826, "end_time": 1770967864.3942459, "duration": 2.867563247680664, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.03184940139015042}, {"layer_num": 4, "start_time": 1770967864.9370375, "end_time": 1770967867.7093937, "duration": 2.7723562717437744, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.005690822648033468}, {"layer_num": 5, "start_time": 1770967868.5791183, "end_time": 1770967871.2648923, "duration": 2.685774087905884, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.025653927705721773}, {"layer_num": 6, "start_time": 1770967871.979237, "end_time": 1770967874.2845883, "duration": 2.3053512573242188, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.14336930120025654}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0050425010805359655, "volume": 110.49600000000002, "diffusion": 5.213, "adsorption": -51.696, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.21891140280928034, "volume": 122.50400000000003, "diffusion": 4.899, "adsorption": -50.738, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.2240727466000882, "volume": 128.80000000000004, "diffusion": 5.128, "adsorption": -50.869, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2342456721440849, "volume": 126.32800000000003, "diffusion": 4.883, "adsorption": -52.272, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.23840037542684875, "volume": 131.43200000000004, "diffusion": 5.28, "adsorption": -52.421, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.24862497096531982, "volume": 137.52800000000002, "diffusion": 5.245, "adsorption": -51.625, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.26665697170216673, "volume": 135.16800000000003, "diffusion": 5.248, "adsorption": -50.958, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.27374276680650267, "volume": 118.10400000000003, "diffusion": 5.398, "adsorption": -48.684, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.28216841398384557, "volume": 129.37600000000003, "diffusion": 5.572, "adsorption": -50.863, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.28561360310801753, "volume": 133.59200000000004, "diffusion": 4.945, "adsorption": -51.157, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28776362150133256, "volume": 132.72800000000004, "diffusion": 4.925, "adsorption": -51.052, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.30278046488639493, "volume": 127.39200000000002, "diffusion": 4.615, "adsorption": -52.402, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.31318167104658257, "volume": 133.38400000000004, "diffusion": 5.173, "adsorption": -49.896, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.31748501983195815, "volume": 122.06400000000004, "diffusion": 4.739, "adsorption": -53.892, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3245371983352369, "volume": 138.19200000000004, "diffusion": 5.494, "adsorption": -52.149, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3362442412779604, "volume": 128.24000000000004, "diffusion": 5.194, "adsorption": -54.761, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.343008932300256, "volume": 128.08000000000004, "diffusion": 4.944, "adsorption": -54.076, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.35511598184271, "volume": 122.58400000000003, "diffusion": 5.431, "adsorption": -55.306, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.36634931237670687, "volume": 127.12000000000003, "diffusion": 5.224, "adsorption": -47.912, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.3745258787144202, "volume": 139.04800000000003, "diffusion": 4.917, "adsorption": -52.828, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3748476804323674, "volume": 128.27200000000002, "diffusion": 6.244, "adsorption": -51.316, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.39380909275761317, "volume": 117.96000000000002, "diffusion": 6.191, "adsorption": -49.214, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3967469342436788, "volume": 126.79200000000003, "diffusion": 4.771, "adsorption": -48.762, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4125632812814226, "volume": 144.67200000000003, "diffusion": 5.78, "adsorption": -51.715, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4132085446524548, "volume": 139.54400000000004, "diffusion": 4.663, "adsorption": -50.814, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.42552277444632547, "volume": 132.41600000000003, "diffusion": 4.947, "adsorption": -48.557, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.42874660717332236, "volume": 146.57600000000002, "diffusion": 5.5, "adsorption": -50.768, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.4425862029963059, "volume": 134.54400000000004, "diffusion": 4.473, "adsorption": -50.16, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.44773374791982595, "volume": 151.16800000000003, "diffusion": 5.584, "adsorption": -51.431, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4553039632431245, "volume": 140.21600000000004, "diffusion": 5.999, "adsorption": -50.973, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.4598017180441677, "volume": 155.30400000000003, "diffusion": 5.529, "adsorption": -51.709, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.4697956777289561, "volume": 126.50400000000003, "diffusion": 5.1, "adsorption": -57.021, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4724462165385492, "volume": 126.43200000000003, "diffusion": 5.8, "adsorption": -47.881, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4729084019864078, "volume": 139.18400000000003, "diffusion": 5.621, "adsorption": -49.26, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.47710129523177425, "volume": 132.80800000000002, "diffusion": 5.636, "adsorption": -48.248, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.48799457992539774, "volume": 127.33600000000003, "diffusion": 4.866, "adsorption": -46.96, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4914853436787465, "volume": 131.77600000000004, "diffusion": 4.934, "adsorption": -56.028, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.4943403062884435, "volume": 122.91200000000003, "diffusion": 5.099, "adsorption": -57.998, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.505967332317959, "volume": 130.59200000000004, "diffusion": 6.203, "adsorption": -49.281, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5117944053460913, "volume": 123.29600000000003, "diffusion": 4.385, "adsorption": -47.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.521291352588436, "volume": 146.96000000000004, "diffusion": 4.595, "adsorption": -50.327, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5297528350297718, "volume": 119.42400000000004, "diffusion": 6.356, "adsorption": -47.644, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5314078931354008, "volume": 146.11200000000002, "diffusion": 5.51, "adsorption": -54.432, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5318109946140765, "volume": 150.30400000000003, "diffusion": 5.312, "adsorption": -48.949, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.5337166201540379, "volume": 129.29600000000002, "diffusion": 5.893, "adsorption": -55.815, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5380327647721482, "volume": 128.06400000000002, "diffusion": 5.153, "adsorption": -45.262, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5390758699505522, "volume": 135.98400000000004, "diffusion": 5.751, "adsorption": -48.053, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.5422463288819659, "volume": 142.56000000000003, "diffusion": 5.021, "adsorption": -55.517, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5423230661951155, "volume": 154.68800000000005, "diffusion": 5.76, "adsorption": -50.929, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5499744874863781, "volume": 129.56000000000003, "diffusion": 4.624, "adsorption": -47.037, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5519535562521619, "volume": 119.00000000000003, "diffusion": 6.043, "adsorption": -46.161, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.5593639069981451, "volume": 121.03200000000002, "diffusion": 5.01, "adsorption": -59.116, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5704578069933894, "volume": 127.48000000000003, "diffusion": 3.977, "adsorption": -48.5, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.5730887093164606, "volume": 126.55200000000004, "diffusion": 5.236, "adsorption": -44.301, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5753088524494179, "volume": 119.49600000000002, "diffusion": 5.917, "adsorption": -45.419, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5801311993981675, "volume": 130.00000000000003, "diffusion": 5.764, "adsorption": -46.46, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6112331096587014, "volume": 138.56800000000004, "diffusion": 5.566, "adsorption": -46.599, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6114128991175265, "volume": 128.16800000000003, "diffusion": 5.873, "adsorption": -45.997, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6160865927771378, "volume": 131.02400000000003, "diffusion": 5.763, "adsorption": -45.996, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.6250752319056859, "volume": 145.97600000000003, "diffusion": 5.936, "adsorption": -54.659, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6377794296395829, "volume": 139.17600000000004, "diffusion": 5.37, "adsorption": -45.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.6390222744104049, "volume": 134.31200000000004, "diffusion": 5.377, "adsorption": -58.557, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.6401962570329082, "volume": 146.39200000000002, "diffusion": 4.899, "adsorption": -47.185, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.6424001344114343, "volume": 142.28800000000004, "diffusion": 6.436, "adsorption": -49.515, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6661724927700662, "volume": 126.72800000000004, "diffusion": 5.847, "adsorption": -44.744, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6731059742136243, "volume": 135.56800000000004, "diffusion": 4.056, "adsorption": -47.725, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.6818566011264745, "volume": 145.64000000000004, "diffusion": 5.218, "adsorption": -58.063, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.6880733145970075, "volume": 146.43200000000004, "diffusion": 5.726, "adsorption": -56.368, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.690807649407513, "volume": 138.40800000000004, "diffusion": 5.54, "adsorption": -45.117, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7014185538599726, "volume": 139.12800000000004, "diffusion": 7.229, "adsorption": -50.629, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.701698022395622, "volume": 143.29600000000002, "diffusion": 5.098, "adsorption": -44.987, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7187110074293263, "volume": 133.61600000000004, "diffusion": 5.25, "adsorption": -42.934, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7204315635188089, "volume": 128.63200000000003, "diffusion": 5.947, "adsorption": -58.957, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7269532131128394, "volume": 137.96000000000004, "diffusion": 7.076, "adsorption": -49.502, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.727706437527045, "volume": 129.40000000000003, "diffusion": 5.928, "adsorption": -59.026, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.7316673805626379, "volume": 144.93600000000004, "diffusion": 5.966, "adsorption": -46.834, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.7358322386326128, "volume": 146.17600000000004, "diffusion": 6.085, "adsorption": -47.348, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7363144242178393, "volume": 147.44000000000003, "diffusion": 5.562, "adsorption": -45.807, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7478138426825838, "volume": 155.87200000000004, "diffusion": 4.809, "adsorption": -47.099, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7619427605499858, "volume": 145.32800000000003, "diffusion": 5.658, "adsorption": -45.355, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.7688587308166388, "volume": 154.84000000000003, "diffusion": 5.541, "adsorption": -46.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7886358673021393, "volume": 128.96800000000002, "diffusion": 6.782, "adsorption": -46.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7928712468397034, "volume": 130.71200000000002, "diffusion": 6.312, "adsorption": -44.716, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.809532135339633, "volume": 139.13600000000002, "diffusion": 7.009, "adsorption": -48.04, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.835992651470262, "volume": 135.96000000000004, "diffusion": 5.732, "adsorption": -42.87, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.8521219648163798, "volume": 155.80800000000005, "diffusion": 5.88, "adsorption": -46.161, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8695260050747209, "volume": 134.03200000000004, "diffusion": 5.912, "adsorption": -42.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8799367299198212, "volume": 138.77600000000004, "diffusion": 4.909, "adsorption": -41.839, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8890491566521843, "volume": 155.72800000000004, "diffusion": 5.601, "adsorption": -44.59, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.8891801557665786, "volume": 142.68000000000004, "diffusion": 6.219, "adsorption": -44.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9074344521898812, "volume": 141.91200000000003, "diffusion": 6.313, "adsorption": -44.483, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9204955886347002, "volume": 135.98400000000004, "diffusion": 6.257, "adsorption": -43.153, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9264956075890209, "volume": 150.81600000000003, "diffusion": 5.717, "adsorption": -43.566, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9611206696472158, "volume": 137.32800000000003, "diffusion": 5.578, "adsorption": -40.417, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.9639768948386143, "volume": 139.23200000000003, "diffusion": 4.6, "adsorption": -41.483, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.9729919744153686, "volume": 151.28800000000004, "diffusion": 5.177, "adsorption": -41.291, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.97531813494612, "volume": 133.36800000000002, "diffusion": 5.31, "adsorption": -38.672, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 0.975744680131559, "volume": 156.84800000000004, "diffusion": 6.112, "adsorption": -44.959, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9761900766008624, "volume": 135.56000000000003, "diffusion": 5.866, "adsorption": -40.835, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.9970599804451854, "volume": 151.40000000000003, "diffusion": 5.26, "adsorption": -40.93, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 1.0012610208834294, "volume": 129.00000000000003, "diffusion": 6.458, "adsorption": -41.342, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.0426553444065205, "volume": 123.40800000000003, "diffusion": 6.062, "adsorption": -64.94, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.0602790606173, "volume": 146.38400000000004, "diffusion": 6.46, "adsorption": -43.029, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1543662339325824, "volume": 147.88000000000002, "diffusion": 6.045, "adsorption": -40.268, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1749077341748828, "volume": 156.91200000000003, "diffusion": 4.628, "adsorption": -40.499, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2311750802647512, "volume": 136.43200000000004, "diffusion": 5.364, "adsorption": -34.917, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.3605546311169474, "volume": 157.25600000000003, "diffusion": 5.653, "adsorption": -36.874, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3741894629913012, "volume": 145.79200000000003, "diffusion": 6.274, "adsorption": -36.913, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3804012503201064, "volume": 156.16000000000003, "diffusion": 5.735, "adsorption": -36.633, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.020988914879896504, "volume": 110.22400000000003, "diffusion": 5.228, "adsorption": -51.513, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.1793929837787092, "volume": 122.89600000000003, "diffusion": 4.918, "adsorption": -51.417, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.25107590350451275, "volume": 132.16800000000003, "diffusion": 5.32, "adsorption": -50.999, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2683998610097915, "volume": 126.44000000000003, "diffusion": 4.889, "adsorption": -52.863, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.3306709385704899, "volume": 130.10400000000004, "diffusion": 4.776, "adsorption": -50.398, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.03365029463319899, "volume": 110.41600000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.18717610529093134, "volume": 122.09600000000003, "diffusion": 4.891, "adsorption": -51.248, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.2856576664389141, "volume": 129.38400000000004, "diffusion": 4.982, "adsorption": -53.011, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3555813794806354, "volume": 125.11200000000002, "diffusion": 4.518, "adsorption": -50.047, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.36573779045258137, "volume": 132.09600000000003, "diffusion": 5.235, "adsorption": -54.661, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.03184940139015042, "volume": 110.61600000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.17874466221121169, "volume": 122.82400000000003, "diffusion": 4.918, "adsorption": -51.417, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.24884279588313268, "volume": 131.92000000000004, "diffusion": 5.32, "adsorption": -50.999, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2666129719272915, "volume": 126.24800000000003, "diffusion": 4.889, "adsorption": -52.862, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.3486643768199734, "volume": 129.30400000000003, "diffusion": 4.759, "adsorption": -50.02, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.005690822648033468, "volume": 110.42400000000002, "diffusion": 5.213, "adsorption": -51.696, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.22364540622823648, "volume": 131.82400000000004, "diffusion": 5.319, "adsorption": -51.415, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.23614650062384687, "volume": 122.38400000000003, "diffusion": 4.753, "adsorption": -50.905, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.37843172353145493, "volume": 126.74400000000003, "diffusion": 4.513, "adsorption": -49.923, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.39412458026154884, "volume": 129.55200000000002, "diffusion": 4.743, "adsorption": -49.328, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.025653927705721773, "volume": 111.01600000000002, "diffusion": 5.242, "adsorption": -51.356, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.26542264219037387, "volume": 122.83200000000004, "diffusion": 4.74, "adsorption": -50.513, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.32903629242368926, "volume": 129.05600000000004, "diffusion": 4.768, "adsorption": -50.29, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4016836888686822, "volume": 131.94400000000005, "diffusion": 5.011, "adsorption": -54.709, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7509015006253494, "volume": 126.30400000000003, "diffusion": 4.321, "adsorption": -44.071, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.14336930120025654, "volume": 110.47200000000002, "diffusion": 4.958, "adsorption": -50.159, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.2288795813666054, "volume": 129.41600000000003, "diffusion": 5.137, "adsorption": -50.852, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2783339237334194, "volume": 126.68000000000004, "diffusion": 4.899, "adsorption": -53.03, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3623675122062633, "volume": 122.78400000000003, "diffusion": 4.668, "adsorption": -49.073, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3654427237076119, "volume": 131.51200000000003, "diffusion": 5.042, "adsorption": -54.254, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 111.05600000000003, "diffusion": 5.213, "adsorption": -51.696}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.14336930120025654, "volume": 110.47200000000002, "diffusion": 4.958, "adsorption": -50.159, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.3623675122062633, "volume": 122.78400000000003, "diffusion": 4.668, "adsorption": -49.073, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 6, "score": 0.2288795813666054, "volume": 129.41600000000003, "diffusion": 5.137, "adsorption": -50.852, "is_selected": false, "rank": 2}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.2783339237334194, "volume": 126.68000000000004, "diffusion": 4.899, "adsorption": -53.03, "is_selected": false, "rank": 3}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.3654427237076119, "volume": 131.51200000000003, "diffusion": 5.042, "adsorption": -54.254, "is_selected": false, "rank": 5}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.24862497096531982, "volume": 137.52800000000002, "diffusion": 5.245, "adsorption": -51.625, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.26665697170216673, "volume": 135.16800000000003, "diffusion": 5.248, "adsorption": -50.958, "is_selected": false, "rank": 7}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.27374276680650267, "volume": 118.10400000000003, "diffusion": 5.398, "adsorption": -48.684, "is_selected": false, "rank": 8}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.28216841398384557, "volume": 129.37600000000003, "diffusion": 5.572, "adsorption": -50.863, "is_selected": false, "rank": 9}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.28561360310801753, "volume": 133.59200000000004, "diffusion": 4.945, "adsorption": -51.157, "is_selected": false, "rank": 10}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.28776362150133256, "volume": 132.72800000000004, "diffusion": 4.925, "adsorption": -51.052, "is_selected": false, "rank": 11}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.30278046488639493, "volume": 127.39200000000002, "diffusion": 4.615, "adsorption": -52.402, "is_selected": false, "rank": 12}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.31318167104658257, "volume": 133.38400000000004, "diffusion": 5.173, "adsorption": -49.896, "is_selected": false, "rank": 13}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.31748501983195815, "volume": 122.06400000000004, "diffusion": 4.739, "adsorption": -53.892, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3245371983352369, "volume": 138.19200000000004, "diffusion": 5.494, "adsorption": -52.149, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.3362442412779604, "volume": 128.24000000000004, "diffusion": 5.194, "adsorption": -54.761, "is_selected": false, "rank": 16}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.343008932300256, "volume": 128.08000000000004, "diffusion": 4.944, "adsorption": -54.076, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.35511598184271, "volume": 122.58400000000003, "diffusion": 5.431, "adsorption": -55.306, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.36634931237670687, "volume": 127.12000000000003, "diffusion": 5.224, "adsorption": -47.912, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.3745258787144202, "volume": 139.04800000000003, "diffusion": 4.917, "adsorption": -52.828, "is_selected": false, "rank": 20}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3748476804323674, "volume": 128.27200000000002, "diffusion": 6.244, "adsorption": -51.316, "is_selected": false, "rank": 21}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.39380909275761317, "volume": 117.96000000000002, "diffusion": 6.191, "adsorption": -49.214, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.3967469342436788, "volume": 126.79200000000003, "diffusion": 4.771, "adsorption": -48.762, "is_selected": false, "rank": 23}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4125632812814226, "volume": 144.67200000000003, "diffusion": 5.78, "adsorption": -51.715, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4132085446524548, "volume": 139.54400000000004, "diffusion": 4.663, "adsorption": -50.814, "is_selected": false, "rank": 25}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.42552277444632547, "volume": 132.41600000000003, "diffusion": 4.947, "adsorption": -48.557, "is_selected": false, "rank": 26}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.42874660717332236, "volume": 146.57600000000002, "diffusion": 5.5, "adsorption": -50.768, "is_selected": false, "rank": 27}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.4425862029963059, "volume": 134.54400000000004, "diffusion": 4.473, "adsorption": -50.16, "is_selected": false, "rank": 28}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.44773374791982595, "volume": 151.16800000000003, "diffusion": 5.584, "adsorption": -51.431, "is_selected": false, "rank": 29}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4553039632431245, "volume": 140.21600000000004, "diffusion": 5.999, "adsorption": -50.973, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4598017180441677, "volume": 155.30400000000003, "diffusion": 5.529, "adsorption": -51.709, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.4697956777289561, "volume": 126.50400000000003, "diffusion": 5.1, "adsorption": -57.021, "is_selected": false, "rank": 32}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4724462165385492, "volume": 126.43200000000003, "diffusion": 5.8, "adsorption": -47.881, "is_selected": false, "rank": 33}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4729084019864078, "volume": 139.18400000000003, "diffusion": 5.621, "adsorption": -49.26, "is_selected": false, "rank": 34}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.47710129523177425, "volume": 132.80800000000002, "diffusion": 5.636, "adsorption": -48.248, "is_selected": false, "rank": 35}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.48799457992539774, "volume": 127.33600000000003, "diffusion": 4.866, "adsorption": -46.96, "is_selected": false, "rank": 36}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4914853436787465, "volume": 131.77600000000004, "diffusion": 4.934, "adsorption": -56.028, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4943403062884435, "volume": 122.91200000000003, "diffusion": 5.099, "adsorption": -57.998, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.505967332317959, "volume": 130.59200000000004, "diffusion": 6.203, "adsorption": -49.281, "is_selected": false, "rank": 39}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5117944053460913, "volume": 123.29600000000003, "diffusion": 4.385, "adsorption": -47.513, "is_selected": false, "rank": 40}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.521291352588436, "volume": 146.96000000000004, "diffusion": 4.595, "adsorption": -50.327, "is_selected": false, "rank": 41}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5297528350297718, "volume": 119.42400000000004, "diffusion": 6.356, "adsorption": -47.644, "is_selected": false, "rank": 42}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5314078931354008, "volume": 146.11200000000002, "diffusion": 5.51, "adsorption": -54.432, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5318109946140765, "volume": 150.30400000000003, "diffusion": 5.312, "adsorption": -48.949, "is_selected": false, "rank": 44}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5337166201540379, "volume": 129.29600000000002, "diffusion": 5.893, "adsorption": -55.815, "is_selected": false, "rank": 45}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5380327647721482, "volume": 128.06400000000002, "diffusion": 5.153, "adsorption": -45.262, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5390758699505522, "volume": 135.98400000000004, "diffusion": 5.751, "adsorption": -48.053, "is_selected": false, "rank": 47}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5422463288819659, "volume": 142.56000000000003, "diffusion": 5.021, "adsorption": -55.517, "is_selected": false, "rank": 48}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5423230661951155, "volume": 154.68800000000005, "diffusion": 5.76, "adsorption": -50.929, "is_selected": false, "rank": 49}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5499744874863781, "volume": 129.56000000000003, "diffusion": 4.624, "adsorption": -47.037, "is_selected": false, "rank": 50}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5519535562521619, "volume": 119.00000000000003, "diffusion": 6.043, "adsorption": -46.161, "is_selected": false, "rank": 51}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.5593639069981451, "volume": 121.03200000000002, "diffusion": 5.01, "adsorption": -59.116, "is_selected": false, "rank": 52}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5704578069933894, "volume": 127.48000000000003, "diffusion": 3.977, "adsorption": -48.5, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.5730887093164606, "volume": 126.55200000000004, "diffusion": 5.236, "adsorption": -44.301, "is_selected": false, "rank": 54}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5753088524494179, "volume": 119.49600000000002, "diffusion": 5.917, "adsorption": -45.419, "is_selected": false, "rank": 55}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5801311993981675, "volume": 130.00000000000003, "diffusion": 5.764, "adsorption": -46.46, "is_selected": false, "rank": 56}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6112331096587014, "volume": 138.56800000000004, "diffusion": 5.566, "adsorption": -46.599, "is_selected": false, "rank": 57}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6114128991175265, "volume": 128.16800000000003, "diffusion": 5.873, "adsorption": -45.997, "is_selected": false, "rank": 58}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6160865927771378, "volume": 131.02400000000003, "diffusion": 5.763, "adsorption": -45.996, "is_selected": false, "rank": 59}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6250752319056859, "volume": 145.97600000000003, "diffusion": 5.936, "adsorption": -54.659, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6377794296395829, "volume": 139.17600000000004, "diffusion": 5.37, "adsorption": -45.588, "is_selected": false, "rank": 61}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6390222744104049, "volume": 134.31200000000004, "diffusion": 5.377, "adsorption": -58.557, "is_selected": false, "rank": 62}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.6401962570329082, "volume": 146.39200000000002, "diffusion": 4.899, "adsorption": -47.185, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.6424001344114343, "volume": 142.28800000000004, "diffusion": 6.436, "adsorption": -49.515, "is_selected": false, "rank": 64}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6661724927700662, "volume": 126.72800000000004, "diffusion": 5.847, "adsorption": -44.744, "is_selected": false, "rank": 65}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6731059742136243, "volume": 135.56800000000004, "diffusion": 4.056, "adsorption": -47.725, "is_selected": false, "rank": 66}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.6818566011264745, "volume": 145.64000000000004, "diffusion": 5.218, "adsorption": -58.063, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6880733145970075, "volume": 146.43200000000004, "diffusion": 5.726, "adsorption": -56.368, "is_selected": false, "rank": 68}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.690807649407513, "volume": 138.40800000000004, "diffusion": 5.54, "adsorption": -45.117, "is_selected": false, "rank": 69}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7014185538599726, "volume": 139.12800000000004, "diffusion": 7.229, "adsorption": -50.629, "is_selected": false, "rank": 70}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.701698022395622, "volume": 143.29600000000002, "diffusion": 5.098, "adsorption": -44.987, "is_selected": false, "rank": 71}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7187110074293263, "volume": 133.61600000000004, "diffusion": 5.25, "adsorption": -42.934, "is_selected": false, "rank": 72}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7204315635188089, "volume": 128.63200000000003, "diffusion": 5.947, "adsorption": -58.957, "is_selected": false, "rank": 73}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7269532131128394, "volume": 137.96000000000004, "diffusion": 7.076, "adsorption": -49.502, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.727706437527045, "volume": 129.40000000000003, "diffusion": 5.928, "adsorption": -59.026, "is_selected": false, "rank": 75}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.7316673805626379, "volume": 144.93600000000004, "diffusion": 5.966, "adsorption": -46.834, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7358322386326128, "volume": 146.17600000000004, "diffusion": 6.085, "adsorption": -47.348, "is_selected": false, "rank": 77}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7363144242178393, "volume": 147.44000000000003, "diffusion": 5.562, "adsorption": -45.807, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7478138426825838, "volume": 155.87200000000004, "diffusion": 4.809, "adsorption": -47.099, "is_selected": false, "rank": 79}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7619427605499858, "volume": 145.32800000000003, "diffusion": 5.658, "adsorption": -45.355, "is_selected": false, "rank": 80}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.7688587308166388, "volume": 154.84000000000003, "diffusion": 5.541, "adsorption": -46.325, "is_selected": false, "rank": 81}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7886358673021393, "volume": 128.96800000000002, "diffusion": 6.782, "adsorption": -46.072, "is_selected": false, "rank": 82}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7928712468397034, "volume": 130.71200000000002, "diffusion": 6.312, "adsorption": -44.716, "is_selected": false, "rank": 83}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.809532135339633, "volume": 139.13600000000002, "diffusion": 7.009, "adsorption": -48.04, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.835992651470262, "volume": 135.96000000000004, "diffusion": 5.732, "adsorption": -42.87, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8521219648163798, "volume": 155.80800000000005, "diffusion": 5.88, "adsorption": -46.161, "is_selected": false, "rank": 86}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8695260050747209, "volume": 134.03200000000004, "diffusion": 5.912, "adsorption": -42.588, "is_selected": false, "rank": 87}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8799367299198212, "volume": 138.77600000000004, "diffusion": 4.909, "adsorption": -41.839, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8890491566521843, "volume": 155.72800000000004, "diffusion": 5.601, "adsorption": -44.59, "is_selected": false, "rank": 89}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.8891801557665786, "volume": 142.68000000000004, "diffusion": 6.219, "adsorption": -44.606, "is_selected": false, "rank": 90}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9074344521898812, "volume": 141.91200000000003, "diffusion": 6.313, "adsorption": -44.483, "is_selected": false, "rank": 91}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9204955886347002, "volume": 135.98400000000004, "diffusion": 6.257, "adsorption": -43.153, "is_selected": false, "rank": 92}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9264956075890209, "volume": 150.81600000000003, "diffusion": 5.717, "adsorption": -43.566, "is_selected": false, "rank": 93}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9611206696472158, "volume": 137.32800000000003, "diffusion": 5.578, "adsorption": -40.417, "is_selected": false, "rank": 94}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.9639768948386143, "volume": 139.23200000000003, "diffusion": 4.6, "adsorption": -41.483, "is_selected": false, "rank": 95}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.9729919744153686, "volume": 151.28800000000004, "diffusion": 5.177, "adsorption": -41.291, "is_selected": false, "rank": 96}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.97531813494612, "volume": 133.36800000000002, "diffusion": 5.31, "adsorption": -38.672, "is_selected": false, "rank": 97}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 0.975744680131559, "volume": 156.84800000000004, "diffusion": 6.112, "adsorption": -44.959, "is_selected": false, "rank": 98}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9761900766008624, "volume": 135.56000000000003, "diffusion": 5.866, "adsorption": -40.835, "is_selected": false, "rank": 99}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.9970599804451854, "volume": 151.40000000000003, "diffusion": 5.26, "adsorption": -40.93, "is_selected": false, "rank": 100}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 1.0012610208834294, "volume": 129.00000000000003, "diffusion": 6.458, "adsorption": -41.342, "is_selected": false, "rank": 101}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0426553444065205, "volume": 123.40800000000003, "diffusion": 6.062, "adsorption": -64.94, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.0602790606173, "volume": 146.38400000000004, "diffusion": 6.46, "adsorption": -43.029, "is_selected": false, "rank": 103}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1543662339325824, "volume": 147.88000000000002, "diffusion": 6.045, "adsorption": -40.268, "is_selected": false, "rank": 104}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1749077341748828, "volume": 156.91200000000003, "diffusion": 4.628, "adsorption": -40.499, "is_selected": false, "rank": 105}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2311750802647512, "volume": 136.43200000000004, "diffusion": 5.364, "adsorption": -34.917, "is_selected": false, "rank": 106}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.3605546311169474, "volume": 157.25600000000003, "diffusion": 5.653, "adsorption": -36.874, "is_selected": false, "rank": 107}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3741894629913012, "volume": 145.79200000000003, "diffusion": 6.274, "adsorption": -36.913, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3804012503201064, "volume": 156.16000000000003, "diffusion": 5.735, "adsorption": -36.633, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770967836.3519435}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 039b736e-7354-49ad-8e80-52cf675ab20b", "timestamp": 1770967836.6173804}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/039b736e-7354-49ad-8e80-52cf675ab20b.log", "timestamp": 1770967836.7700734}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770967837.1101017}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 111.0560, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770967837.1377492}, {"type": "log", "level": "info", "message": "远程日志已启动: 039b736e-7354-49ad-8e80-52cf675ab20b", "timestamp": 1770967837.1926348}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770967837.3119695}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967837.4623828}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770967837.6218972}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770967837.769813}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 111.06, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770967837.9283218}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770967838.08212}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967838.2172399}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770967838.239728}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 111.0560, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770967838.2610314}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770967838.2904422}, {"type": "log", "level": "info", "message": "远程日志已启动: 039b736e-7354-49ad-8e80-52cf675ab20b", "timestamp": 1770967838.426933}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770967838.577889}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770967838.7555583}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770967838.7789168}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967838.930661}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967839.0668285}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770967839.2099507}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967839.345067}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770967839.3706825}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770967839.5874844}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770967839.7617383}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.03s, 速度: 5721.02 mol/s", "timestamp": 1770967839.7851968}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.03006458282470703, "speed": 5721.017351308486, "timestamp": 1770967839.807272}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770967839.9969077}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770967840.175299}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770967840.3760815}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770967840.4062517}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770967840.4286606}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770967840.6104884}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770967850.8303485}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0050", "timestamp": 1770967850.855294}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0050425010805359655, "timestamp": 1770967850.8785172}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967851.049801}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967851.0717642}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770967851.0953124}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967851.3867116}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0050", "timestamp": 1770967851.5991623}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.50", "timestamp": 1770967851.7775562}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770967851.9198399}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770967852.0733747}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 12.73s) ==========", "timestamp": 1770967852.1021092}, {"type": "layer_end", "layer": 0, "duration": 12.73050856590271, "timestamp": 1770967852.1289744}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967852.3099413}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967852.5067358}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770967852.6886048}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967852.842311}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770967852.8662655}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770967853.0658035}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770967853.2623317}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 11050.24 mol/s", "timestamp": 1770967853.283748}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.002714872360229492, "speed": 11050.243259857732, "timestamp": 1770967853.3056414}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770967853.4722278}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770967853.4970183}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770967853.5204237}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770967853.68183}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967855.7544484}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0210", "timestamp": 1770967855.7765589}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.020988914879896504, "timestamp": 1770967855.7997575}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967855.972541}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967855.998181}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N"], "timestamp": 1770967856.022538}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967856.161924}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0210", "timestamp": 1770967856.2910147}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.22", "timestamp": 1770967856.4568357}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2280", "timestamp": 1770967856.6451035}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5130", "timestamp": 1770967856.8128014}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 3.97s) ==========", "timestamp": 1770967856.8382003}, {"type": "layer_end", "layer": 1, "duration": 3.971856117248535, "timestamp": 1770967856.8599415}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967857.0214472}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967857.1972654}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770967857.35875}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967857.6093638}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770967857.6361494}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770967857.8041284}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770967857.9411137}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 12708.47 mol/s", "timestamp": 1770967857.9606519}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.001967191696166992, "speed": 12708.471700399952, "timestamp": 1770967857.9835305}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770967858.1226654}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770967858.1460152}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770967858.169841}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770967858.3316445}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967860.083863}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0337", "timestamp": 1770967860.1025112}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.03365029463319899, "timestamp": 1770967860.1202006}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967860.246011}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967860.2663214}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770967860.2866344}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967860.4138243}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0337", "timestamp": 1770967860.5264583}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.42", "timestamp": 1770967860.701111}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770967860.8303227}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770967860.955814}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 3.34s) ==========", "timestamp": 1770967860.9772503}, {"type": "layer_end", "layer": 2, "duration": 3.3408877849578857, "timestamp": 1770967860.9957721}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967861.131309}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967861.2595477}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770967861.3762038}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967861.5059836}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770967861.5269158}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770967861.6895666}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770967861.820401}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 27280.03 mol/s", "timestamp": 1770967861.8393412}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0007331371307373047, "speed": 27280.026016260163, "timestamp": 1770967861.8564825}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770967861.977647}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770967861.9955797}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770967862.0132809}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770967862.1393301}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967863.5199463}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0318", "timestamp": 1770967863.535261}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.03184940139015042, "timestamp": 1770967863.5509264}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967863.6716075}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967863.688889}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N"], "timestamp": 1770967863.707802}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967863.8408394}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0318", "timestamp": 1770967863.974726}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.62", "timestamp": 1770967864.135514}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770967864.2728038}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770967864.3790684}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.87s) ==========", "timestamp": 1770967864.3947403}, {"type": "layer_end", "layer": 3, "duration": 2.867563247680664, "timestamp": 1770967864.4097683}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967864.5116496}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967864.6374068}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770967864.7824366}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967864.918612}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770967864.937337}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770967865.0706837}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770967865.2110627}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 26412.49 mol/s", "timestamp": 1770967865.2312093}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0005679130554199219, "speed": 26412.493702770782, "timestamp": 1770967865.2504635}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770967865.38394}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770967865.4005172}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770967865.4228966}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770967865.5503426}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967866.8364623}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0057", "timestamp": 1770967866.8559701}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.005690822648033468, "timestamp": 1770967866.875576}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967866.9973505}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967867.0145764}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N"], "timestamp": 1770967867.0303087}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967867.1480272}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0057", "timestamp": 1770967867.272265}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.42", "timestamp": 1770967867.400784}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770967867.5381951}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770967867.6829734}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 2.77s) ==========", "timestamp": 1770967867.7101972}, {"type": "layer_end", "layer": 4, "duration": 2.7723562717437744, "timestamp": 1770967867.729503}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967867.874348}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967867.9955208}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770967868.331103}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967868.553805}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770967868.5793397}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770967868.7411778}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770967868.9132216}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 20919.22 mol/s", "timestamp": 1770967868.938588}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0004780292510986328, "speed": 20919.221945137157, "timestamp": 1770967868.9674027}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770967869.1911492}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770967869.2190895}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770967869.2460415}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770967869.4838781}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967870.3738937}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0257", "timestamp": 1770967870.3948483}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.025653927705721773, "timestamp": 1770967870.416723}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967870.567366}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967870.588814}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770967870.6082137}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967870.7456532}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0257", "timestamp": 1770967870.9190545}, {"type": "log", "level": "info", "message": "INFO -     体积: 111.02", "timestamp": 1770967871.0312161}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770967871.1337085}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770967871.237598}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 2.69s) ==========", "timestamp": 1770967871.2660174}, {"type": "layer_end", "layer": 5, "duration": 2.685774087905884, "timestamp": 1770967871.2860353}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967871.5496495}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967871.6946402}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770967871.8398724}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967871.9612656}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770967871.9794552}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770967872.115027}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770967872.2701728}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 24614.46 mol/s", "timestamp": 1770967872.2962992}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.00020313262939453125, "speed": 24614.460093896712, "timestamp": 1770967872.322395}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770967872.4737022}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770967872.4929907}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770967872.5133681}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770967872.643397}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770967873.2027502}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1434", "timestamp": 1770967873.2226498}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.14336930120025654, "timestamp": 1770967873.2438958}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770967873.3969543}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770967873.4213824}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770967873.4419491}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967873.5930188}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1434", "timestamp": 1770967873.7604265}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.47", "timestamp": 1770967873.9145355}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9580", "timestamp": 1770967874.0893214}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.1590", "timestamp": 1770967874.2572265}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 2.31s) ==========", "timestamp": 1770967874.2851386}, {"type": "layer_end", "layer": 6, "duration": 2.3053512573242188, "timestamp": 1770967874.305961}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770967874.9479787}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967875.0835352}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770967875.2150667}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 35.52 秒", "timestamp": 1770967875.3548648}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770967875.8860514}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770967876.0312455}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770967876.1766994}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967876.3231757}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.0599, 体积: 110.58, 扩散: 5.3560, 吸附: -51.2090", "timestamp": 1770967876.4842045}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "timestamp": 1770967876.6482084}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2277, 体积: 129.29, 扩散: 5.1370, 吸附: -50.8520", "timestamp": 1770967876.8185163}, {"type": "log", "level": "info", "message": "INFO -   3. [H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770967876.9573593}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3459, 体积: 131.65, 扩散: 5.2300, 吸附: -54.4060", "timestamp": 1770967877.099884}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770967877.2169225}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.4763, 体积: 120.85, 扩散: 4.6240, 吸附: -46.9540", "timestamp": 1770967877.3464627}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770967877.4851933}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.7529, 体积: 126.33, 扩散: 4.3310, 吸附: -44.0070", "timestamp": 1770967877.6408637}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 41.31s", "timestamp": 1770967877.663079}, {"type": "finish", "total_results": 5, "duration": 41.313682556152344, "timestamp": 1770967877.6872435}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/039b736e-7354-49ad-8e80-52cf675ab20b.json", "timestamp": 1770967877.7210963}, {"type": "log", "level": "info", "message": "远程日志已上传: 039b736e-7354-49ad-8e80-52cf675ab20b", "timestamp": 1770967877.7643614}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770967877.916319}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770967878.1199334}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770967878.141965}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 111.0560, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770967878.1780334}, {"type": "log", "level": "info", "message": "远程日志已启动: 039b736e-7354-49ad-8e80-52cf675ab20b", "timestamp": 1770967878.2303653}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770967878.6313875}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 42.30s", "timestamp": 1770967878.6516786}, {"type": "finish", "total_results": 5, "duration": 42.30235552787781, "timestamp": 1770967878.669545}]}