{"metadata": {"log_name": "7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "start_time": 1770968633.4239542, "end_time": 1770968689.8410542, "duration": 56.417099952697754, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.66400000000003, "diffusion": 4.971, "adsorption": -50.23}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770968637.318668, "end_time": 1770968652.7730904, "duration": 15.45442247390747, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08035795738204997}, {"layer_num": 1, "start_time": 1770968654.1358976, "end_time": 1770968659.1405904, "duration": 5.004692792892456, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.00397599942167279}, {"layer_num": 2, "start_time": 1770968660.191542, "end_time": 1770968664.4507725, "duration": 4.259230613708496, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12198371364418253}, {"layer_num": 3, "start_time": 1770968665.3810794, "end_time": 1770968669.7651827, "duration": 4.384103298187256, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1227351514178707}, {"layer_num": 4, "start_time": 1770968670.699802, "end_time": 1770968674.4777641, "duration": 3.7779622077941895, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1225302791439699}, {"layer_num": 5, "start_time": 1770968675.4512115, "end_time": 1770968679.552452, "duration": 4.101240634918213, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12267486094112155}, {"layer_num": 6, "start_time": 1770968680.9139874, "end_time": 1770968684.2956676, "duration": 3.3816802501678467, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1037411633790206}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.08035795738204997, "volume": 110.01600000000002, "diffusion": 4.959, "adsorption": -49.023, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2044898771936294, "volume": 131.59200000000004, "diffusion": 5.032, "adsorption": -50.282, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.21052865943475368, "volume": 122.19200000000004, "diffusion": 4.681, "adsorption": -49.426, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.22445898444528264, "volume": 126.72000000000003, "diffusion": 5.256, "adsorption": -50.599, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.25151614292217794, "volume": 121.79200000000003, "diffusion": 4.668, "adsorption": -48.723, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.26348041443411013, "volume": 128.79200000000003, "diffusion": 4.757, "adsorption": -49.282, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.270357051267979, "volume": 133.33600000000004, "diffusion": 5.165, "adsorption": -49.787, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.31151585450957253, "volume": 126.54400000000003, "diffusion": 4.87, "adsorption": -52.703, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.33017665190342477, "volume": 139.39200000000002, "diffusion": 4.768, "adsorption": -50.728, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3318819932472412, "volume": 119.15200000000003, "diffusion": 5.418, "adsorption": -47.463, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.3367365923510644, "volume": 121.25600000000003, "diffusion": 5.168, "adsorption": -53.602, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.3398258415190327, "volume": 125.66400000000003, "diffusion": 4.818, "adsorption": -53.135, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.3474053293424619, "volume": 135.52800000000002, "diffusion": 4.431, "adsorption": -49.994, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.34769858145152266, "volume": 118.65600000000003, "diffusion": 5.787, "adsorption": -48.366, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.36735828540636983, "volume": 127.20000000000003, "diffusion": 4.868, "adsorption": -46.928, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3717060975469948, "volume": 126.64800000000002, "diffusion": 4.859, "adsorption": -53.658, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3730267241417708, "volume": 131.88800000000003, "diffusion": 5.369, "adsorption": -51.924, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.38242263292882445, "volume": 129.96800000000002, "diffusion": 5.985, "adsorption": -50.163, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4014641018877781, "volume": 127.18400000000003, "diffusion": 6.072, "adsorption": -49.716, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4067319390679586, "volume": 138.82400000000004, "diffusion": 5.453, "adsorption": -49.304, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.4069632146450328, "volume": 122.29600000000003, "diffusion": 4.789, "adsorption": -54.671, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.41137344016233945, "volume": 129.52800000000002, "diffusion": 4.627, "adsorption": -47.355, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4247412113682889, "volume": 144.86400000000003, "diffusion": 5.425, "adsorption": -49.822, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4272454469951258, "volume": 134.05600000000004, "diffusion": 4.799, "adsorption": -47.195, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.42939773453774804, "volume": 139.97600000000003, "diffusion": 4.703, "adsorption": -52.082, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4372137248826788, "volume": 132.82400000000004, "diffusion": 5.926, "adsorption": -49.479, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.437221570868937, "volume": 138.20000000000005, "diffusion": 5.871, "adsorption": -50.107, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.43822434472341887, "volume": 122.24800000000003, "diffusion": 5.182, "adsorption": -55.104, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.4386318950706382, "volume": 128.56800000000004, "diffusion": 5.257, "adsorption": -53.902, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.44162807622791844, "volume": 138.27200000000002, "diffusion": 5.189, "adsorption": -52.713, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.44824029092294965, "volume": 137.89600000000004, "diffusion": 5.579, "adsorption": -51.567, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.45301194233244785, "volume": 128.62400000000002, "diffusion": 5.342, "adsorption": -53.848, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4535049602379925, "volume": 127.28800000000003, "diffusion": 3.977, "adsorption": -48.5, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.48178370388866815, "volume": 146.88000000000002, "diffusion": 5.618, "adsorption": -50.638, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.48942111239572705, "volume": 128.03200000000004, "diffusion": 5.242, "adsorption": -45.576, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.49046341927114223, "volume": 128.84000000000003, "diffusion": 6.242, "adsorption": -51.411, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.49068556678785363, "volume": 129.38400000000004, "diffusion": 5.317, "adsorption": -54.448, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.49652726895772803, "volume": 135.26400000000004, "diffusion": 3.97, "adsorption": -49.01, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.5156676569850313, "volume": 135.22400000000002, "diffusion": 5.012, "adsorption": -55.01, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.516298996788492, "volume": 132.66400000000004, "diffusion": 5.248, "adsorption": -45.847, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5210399904632391, "volume": 153.60800000000003, "diffusion": 5.255, "adsorption": -48.96, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5286108507578866, "volume": 119.52000000000002, "diffusion": 6.342, "adsorption": -47.337, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5356109458479349, "volume": 140.47200000000004, "diffusion": 5.946, "adsorption": -49.056, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.5462570926447672, "volume": 126.68000000000004, "diffusion": 4.8, "adsorption": -56.377, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5486453971025795, "volume": 145.76800000000003, "diffusion": 5.4, "adsorption": -47.8, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.5523540907752781, "volume": 126.93600000000004, "diffusion": 5.219, "adsorption": -44.279, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.558016223957523, "volume": 131.84800000000004, "diffusion": 4.925, "adsorption": -56.213, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5655842085302619, "volume": 133.26400000000004, "diffusion": 5.977, "adsorption": -47.568, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5680100278630349, "volume": 154.99200000000005, "diffusion": 4.858, "adsorption": -47.807, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5701313365231104, "volume": 119.06400000000002, "diffusion": 5.984, "adsorption": -45.367, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5785726205343753, "volume": 126.86400000000003, "diffusion": 5.531, "adsorption": -44.88, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.579306550904518, "volume": 142.44800000000004, "diffusion": 5.41, "adsorption": -53.642, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5864150573818602, "volume": 130.44000000000003, "diffusion": 5.768, "adsorption": -46.088, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5909961414152396, "volume": 122.70400000000004, "diffusion": 4.38, "adsorption": -44.147, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5960455694315496, "volume": 131.40000000000003, "diffusion": 5.768, "adsorption": -46.072, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6011337356686273, "volume": 139.90400000000002, "diffusion": 5.43, "adsorption": -46.135, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.6040888556567324, "volume": 135.59200000000004, "diffusion": 5.742, "adsorption": -46.484, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.607883965871418, "volume": 142.07200000000003, "diffusion": 6.367, "adsorption": -49.506, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6078952709384235, "volume": 129.28800000000004, "diffusion": 6.493, "adsorption": -47.996, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6199408640432729, "volume": 118.74400000000003, "diffusion": 5.871, "adsorption": -44.104, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.6317945690610428, "volume": 146.51200000000003, "diffusion": 4.285, "adsorption": -47.386, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.6389119052470799, "volume": 150.55200000000005, "diffusion": 5.779, "adsorption": -48.289, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6399148764705407, "volume": 143.12000000000003, "diffusion": 4.806, "adsorption": -44.982, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6440951289376409, "volume": 146.98400000000004, "diffusion": 4.585, "adsorption": -54.219, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6581513119739486, "volume": 146.13600000000002, "diffusion": 5.519, "adsorption": -54.037, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.6758256217797971, "volume": 130.30400000000003, "diffusion": 6.359, "adsorption": -46.561, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6857270696084441, "volume": 134.43200000000004, "diffusion": 6.308, "adsorption": -46.848, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6864983326208963, "volume": 141.64000000000004, "diffusion": 6.385, "adsorption": -48.185, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6872353577567466, "volume": 133.41600000000003, "diffusion": 5.227, "adsorption": -43.028, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.69198479935414, "volume": 150.64800000000002, "diffusion": 6.078, "adsorption": -52.038, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.694472443363136, "volume": 155.44800000000004, "diffusion": 5.134, "adsorption": -54.533, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.69708239143087, "volume": 146.55200000000002, "diffusion": 5.755, "adsorption": -53.831, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7013867378669507, "volume": 135.84000000000003, "diffusion": 5.548, "adsorption": -44.239, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.7041271253131312, "volume": 142.72000000000003, "diffusion": 6.751, "adsorption": -51.174, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.74831149963087, "volume": 154.97600000000003, "diffusion": 5.541, "adsorption": -46.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.7497791932739648, "volume": 138.20800000000003, "diffusion": 5.901, "adsorption": -44.976, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.7519962558419371, "volume": 156.36000000000004, "diffusion": 5.865, "adsorption": -47.564, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7534770376571428, "volume": 138.94400000000005, "diffusion": 7.198, "adsorption": -51.066, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.7641446732089419, "volume": 146.32800000000003, "diffusion": 6.235, "adsorption": -47.089, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7695859030308883, "volume": 131.22400000000002, "diffusion": 5.967, "adsorption": -43.81, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.7749948437071925, "volume": 129.54400000000004, "diffusion": 6.648, "adsorption": -45.759, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7814317276849454, "volume": 146.86400000000003, "diffusion": 5.853, "adsorption": -45.594, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.8036023520635696, "volume": 146.21600000000004, "diffusion": 6.386, "adsorption": -46.92, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.8091748408214553, "volume": 127.84800000000003, "diffusion": 5.708, "adsorption": -58.696, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8142193539377949, "volume": 138.90400000000002, "diffusion": 5.145, "adsorption": -41.456, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.8189366278493169, "volume": 135.25600000000003, "diffusion": 5.506, "adsorption": -58.419, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8463745474472776, "volume": 138.66400000000004, "diffusion": 6.771, "adsorption": -46.358, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8541035487926314, "volume": 149.11200000000002, "diffusion": 6.015, "adsorption": -45.263, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.8563915792821573, "volume": 140.33600000000004, "diffusion": 4.66, "adsorption": -41.428, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8630061861999037, "volume": 132.83200000000002, "diffusion": 5.801, "adsorption": -41.93, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8808635329872547, "volume": 156.29600000000005, "diffusion": 5.464, "adsorption": -44.046, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8816708808220339, "volume": 128.44800000000004, "diffusion": 5.738, "adsorption": -59.718, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.882944781380633, "volume": 145.04000000000002, "diffusion": 5.833, "adsorption": -56.909, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 0.9036501780523756, "volume": 154.88800000000003, "diffusion": 6.063, "adsorption": -45.469, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9079601956420681, "volume": 138.16800000000003, "diffusion": 7.331, "adsorption": -47.138, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9312070293310839, "volume": 128.72000000000003, "diffusion": 5.812, "adsorption": -40.203, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.9373585195968346, "volume": 151.34400000000002, "diffusion": 5.277, "adsorption": -41.721, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.9707172544423301, "volume": 145.70400000000004, "diffusion": 5.187, "adsorption": -60.454, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9831862672851569, "volume": 135.75200000000004, "diffusion": 5.794, "adsorption": -40.336, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0211576236046622, "volume": 156.32000000000005, "diffusion": 4.678, "adsorption": -41.027, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.0519143687581194, "volume": 151.26400000000004, "diffusion": 5.31, "adsorption": -39.902, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1353860576810992, "volume": 147.05600000000004, "diffusion": 5.566, "adsorption": -38.73, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1910843380275953, "volume": 137.57600000000002, "diffusion": 6.079, "adsorption": -38.091, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.2102018591704566, "volume": 145.84000000000003, "diffusion": 6.225, "adsorption": -39.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.2172314836026215, "volume": 123.71200000000003, "diffusion": 5.947, "adsorption": -65.349, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.2227666588828887, "volume": 155.36000000000004, "diffusion": 5.69, "adsorption": -38.941, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2236982179261051, "volume": 136.38400000000004, "diffusion": 5.393, "adsorption": -35.054, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.228572425166984, "volume": 157.69600000000003, "diffusion": 5.521, "adsorption": -38.628, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2914402454922336, "volume": 146.23200000000003, "diffusion": 6.958, "adsorption": -40.681, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.00397599942167279, "volume": 110.22400000000003, "diffusion": 4.971, "adsorption": -50.23, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.19627837697590783, "volume": 122.99200000000003, "diffusion": 4.9, "adsorption": -51.412, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.20965929298557076, "volume": 127.19200000000004, "diffusion": 5.25, "adsorption": -50.3, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.27790146821727435, "volume": 132.55200000000002, "diffusion": 4.972, "adsorption": -48.892, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3807760748326912, "volume": 123.00000000000003, "diffusion": 4.425, "adsorption": -52.9, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.12198371364418253, "volume": 110.68800000000003, "diffusion": 5.242, "adsorption": -51.356, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.13069710180233962, "volume": 122.63200000000003, "diffusion": 4.888, "adsorption": -50.132, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.26466454769978137, "volume": 132.22400000000002, "diffusion": 4.991, "adsorption": -49.128, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2768514562684262, "volume": 127.19200000000004, "diffusion": 5.155, "adsorption": -48.715, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.493418061244987, "volume": 122.59200000000003, "diffusion": 5.538, "adsorption": -54.777, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1227351514178707, "volume": 110.68800000000003, "diffusion": 5.23, "adsorption": -51.409, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.18867244132971855, "volume": 121.99200000000003, "diffusion": 4.672, "adsorption": -49.792, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.20360342082113395, "volume": 132.06400000000002, "diffusion": 4.946, "adsorption": -50.143, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.20947179090417845, "volume": 122.69600000000003, "diffusion": 4.909, "adsorption": -51.708, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.21642078496478806, "volume": 127.11200000000002, "diffusion": 5.259, "adsorption": -50.395, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1225302791439699, "volume": 110.24000000000002, "diffusion": 5.241, "adsorption": -51.308, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.17283834094222655, "volume": 122.68000000000004, "diffusion": 4.678, "adsorption": -50.319, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.22012093442061792, "volume": 132.64000000000004, "diffusion": 5.046, "adsorption": -50.338, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.22590724750679617, "volume": 127.60800000000003, "diffusion": 5.253, "adsorption": -50.499, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.35809383031509046, "volume": 121.74400000000003, "diffusion": 4.628, "adsorption": -47.066, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.12267486094112155, "volume": 110.22400000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.21623138426726116, "volume": 126.66400000000003, "diffusion": 5.114, "adsorption": -49.512, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.21638422221939907, "volume": 122.84000000000003, "diffusion": 4.681, "adsorption": -49.426, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3608713020460091, "volume": 123.00000000000003, "diffusion": 4.624, "adsorption": -47.223, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5374241164075242, "volume": 132.22400000000002, "diffusion": 5.428, "adsorption": -54.427, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1037411633790206, "volume": 110.36800000000002, "diffusion": 5.072, "adsorption": -48.878, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.19966360018480606, "volume": 123.23200000000003, "diffusion": 4.899, "adsorption": -51.429, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.21081594736278467, "volume": 127.32000000000004, "diffusion": 5.25, "adsorption": -50.3, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3104602763165448, "volume": 122.87200000000003, "diffusion": 4.873, "adsorption": -53.251, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5490934321573402, "volume": 132.77600000000004, "diffusion": 5.558, "adsorption": -54.101, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.66400000000003, "diffusion": 4.971, "adsorption": -50.23}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.1037411633790206, "volume": 110.36800000000002, "diffusion": 5.072, "adsorption": -48.878, "is_selected": false, "rank": 1}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.5490934321573402, "volume": 132.77600000000004, "diffusion": 5.558, "adsorption": -54.101, "is_selected": false, "rank": 5}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.3104602763165448, "volume": 122.87200000000003, "diffusion": 4.873, "adsorption": -53.251, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6, "score": 0.21081594736278467, "volume": 127.32000000000004, "diffusion": 5.25, "adsorption": -50.3, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.19966360018480606, "volume": 123.23200000000003, "diffusion": 4.899, "adsorption": -51.429, "is_selected": false, "rank": 2}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.26348041443411013, "volume": 128.79200000000003, "diffusion": 4.757, "adsorption": -49.282, "is_selected": false, "rank": 6}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.270357051267979, "volume": 133.33600000000004, "diffusion": 5.165, "adsorption": -49.787, "is_selected": false, "rank": 7}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.31151585450957253, "volume": 126.54400000000003, "diffusion": 4.87, "adsorption": -52.703, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.33017665190342477, "volume": 139.39200000000002, "diffusion": 4.768, "adsorption": -50.728, "is_selected": false, "rank": 9}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3318819932472412, "volume": 119.15200000000003, "diffusion": 5.418, "adsorption": -47.463, "is_selected": false, "rank": 10}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.3367365923510644, "volume": 121.25600000000003, "diffusion": 5.168, "adsorption": -53.602, "is_selected": false, "rank": 11}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.3398258415190327, "volume": 125.66400000000003, "diffusion": 4.818, "adsorption": -53.135, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.3474053293424619, "volume": 135.52800000000002, "diffusion": 4.431, "adsorption": -49.994, "is_selected": false, "rank": 13}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.34769858145152266, "volume": 118.65600000000003, "diffusion": 5.787, "adsorption": -48.366, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.36735828540636983, "volume": 127.20000000000003, "diffusion": 4.868, "adsorption": -46.928, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.3717060975469948, "volume": 126.64800000000002, "diffusion": 4.859, "adsorption": -53.658, "is_selected": false, "rank": 16}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3730267241417708, "volume": 131.88800000000003, "diffusion": 5.369, "adsorption": -51.924, "is_selected": false, "rank": 17}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.38242263292882445, "volume": 129.96800000000002, "diffusion": 5.985, "adsorption": -50.163, "is_selected": false, "rank": 18}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4014641018877781, "volume": 127.18400000000003, "diffusion": 6.072, "adsorption": -49.716, "is_selected": false, "rank": 19}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4067319390679586, "volume": 138.82400000000004, "diffusion": 5.453, "adsorption": -49.304, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4069632146450328, "volume": 122.29600000000003, "diffusion": 4.789, "adsorption": -54.671, "is_selected": false, "rank": 21}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.41137344016233945, "volume": 129.52800000000002, "diffusion": 4.627, "adsorption": -47.355, "is_selected": false, "rank": 22}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4247412113682889, "volume": 144.86400000000003, "diffusion": 5.425, "adsorption": -49.822, "is_selected": false, "rank": 23}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4272454469951258, "volume": 134.05600000000004, "diffusion": 4.799, "adsorption": -47.195, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.42939773453774804, "volume": 139.97600000000003, "diffusion": 4.703, "adsorption": -52.082, "is_selected": false, "rank": 25}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4372137248826788, "volume": 132.82400000000004, "diffusion": 5.926, "adsorption": -49.479, "is_selected": false, "rank": 26}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.437221570868937, "volume": 138.20000000000005, "diffusion": 5.871, "adsorption": -50.107, "is_selected": false, "rank": 27}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.43822434472341887, "volume": 122.24800000000003, "diffusion": 5.182, "adsorption": -55.104, "is_selected": false, "rank": 28}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.4386318950706382, "volume": 128.56800000000004, "diffusion": 5.257, "adsorption": -53.902, "is_selected": false, "rank": 29}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.44162807622791844, "volume": 138.27200000000002, "diffusion": 5.189, "adsorption": -52.713, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.44824029092294965, "volume": 137.89600000000004, "diffusion": 5.579, "adsorption": -51.567, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.45301194233244785, "volume": 128.62400000000002, "diffusion": 5.342, "adsorption": -53.848, "is_selected": false, "rank": 32}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4535049602379925, "volume": 127.28800000000003, "diffusion": 3.977, "adsorption": -48.5, "is_selected": false, "rank": 33}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.48178370388866815, "volume": 146.88000000000002, "diffusion": 5.618, "adsorption": -50.638, "is_selected": false, "rank": 34}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.48942111239572705, "volume": 128.03200000000004, "diffusion": 5.242, "adsorption": -45.576, "is_selected": false, "rank": 35}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.49046341927114223, "volume": 128.84000000000003, "diffusion": 6.242, "adsorption": -51.411, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.49068556678785363, "volume": 129.38400000000004, "diffusion": 5.317, "adsorption": -54.448, "is_selected": false, "rank": 37}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.49652726895772803, "volume": 135.26400000000004, "diffusion": 3.97, "adsorption": -49.01, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5156676569850313, "volume": 135.22400000000002, "diffusion": 5.012, "adsorption": -55.01, "is_selected": false, "rank": 39}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.516298996788492, "volume": 132.66400000000004, "diffusion": 5.248, "adsorption": -45.847, "is_selected": false, "rank": 40}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5210399904632391, "volume": 153.60800000000003, "diffusion": 5.255, "adsorption": -48.96, "is_selected": false, "rank": 41}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5286108507578866, "volume": 119.52000000000002, "diffusion": 6.342, "adsorption": -47.337, "is_selected": false, "rank": 42}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5356109458479349, "volume": 140.47200000000004, "diffusion": 5.946, "adsorption": -49.056, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5462570926447672, "volume": 126.68000000000004, "diffusion": 4.8, "adsorption": -56.377, "is_selected": false, "rank": 44}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5486453971025795, "volume": 145.76800000000003, "diffusion": 5.4, "adsorption": -47.8, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.5523540907752781, "volume": 126.93600000000004, "diffusion": 5.219, "adsorption": -44.279, "is_selected": false, "rank": 46}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.558016223957523, "volume": 131.84800000000004, "diffusion": 4.925, "adsorption": -56.213, "is_selected": false, "rank": 47}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5655842085302619, "volume": 133.26400000000004, "diffusion": 5.977, "adsorption": -47.568, "is_selected": false, "rank": 48}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5680100278630349, "volume": 154.99200000000005, "diffusion": 4.858, "adsorption": -47.807, "is_selected": false, "rank": 49}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5701313365231104, "volume": 119.06400000000002, "diffusion": 5.984, "adsorption": -45.367, "is_selected": false, "rank": 50}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5785726205343753, "volume": 126.86400000000003, "diffusion": 5.531, "adsorption": -44.88, "is_selected": false, "rank": 51}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.579306550904518, "volume": 142.44800000000004, "diffusion": 5.41, "adsorption": -53.642, "is_selected": false, "rank": 52}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5864150573818602, "volume": 130.44000000000003, "diffusion": 5.768, "adsorption": -46.088, "is_selected": false, "rank": 53}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5909961414152396, "volume": 122.70400000000004, "diffusion": 4.38, "adsorption": -44.147, "is_selected": false, "rank": 54}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5960455694315496, "volume": 131.40000000000003, "diffusion": 5.768, "adsorption": -46.072, "is_selected": false, "rank": 55}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6011337356686273, "volume": 139.90400000000002, "diffusion": 5.43, "adsorption": -46.135, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6040888556567324, "volume": 135.59200000000004, "diffusion": 5.742, "adsorption": -46.484, "is_selected": false, "rank": 57}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.607883965871418, "volume": 142.07200000000003, "diffusion": 6.367, "adsorption": -49.506, "is_selected": false, "rank": 58}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6078952709384235, "volume": 129.28800000000004, "diffusion": 6.493, "adsorption": -47.996, "is_selected": false, "rank": 59}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6199408640432729, "volume": 118.74400000000003, "diffusion": 5.871, "adsorption": -44.104, "is_selected": false, "rank": 60}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.6317945690610428, "volume": 146.51200000000003, "diffusion": 4.285, "adsorption": -47.386, "is_selected": false, "rank": 61}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6389119052470799, "volume": 150.55200000000005, "diffusion": 5.779, "adsorption": -48.289, "is_selected": false, "rank": 62}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6399148764705407, "volume": 143.12000000000003, "diffusion": 4.806, "adsorption": -44.982, "is_selected": false, "rank": 63}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6440951289376409, "volume": 146.98400000000004, "diffusion": 4.585, "adsorption": -54.219, "is_selected": false, "rank": 64}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6581513119739486, "volume": 146.13600000000002, "diffusion": 5.519, "adsorption": -54.037, "is_selected": false, "rank": 65}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.6758256217797971, "volume": 130.30400000000003, "diffusion": 6.359, "adsorption": -46.561, "is_selected": false, "rank": 66}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6857270696084441, "volume": 134.43200000000004, "diffusion": 6.308, "adsorption": -46.848, "is_selected": false, "rank": 67}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6864983326208963, "volume": 141.64000000000004, "diffusion": 6.385, "adsorption": -48.185, "is_selected": false, "rank": 68}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6872353577567466, "volume": 133.41600000000003, "diffusion": 5.227, "adsorption": -43.028, "is_selected": false, "rank": 69}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.69198479935414, "volume": 150.64800000000002, "diffusion": 6.078, "adsorption": -52.038, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.694472443363136, "volume": 155.44800000000004, "diffusion": 5.134, "adsorption": -54.533, "is_selected": false, "rank": 71}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.69708239143087, "volume": 146.55200000000002, "diffusion": 5.755, "adsorption": -53.831, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7013867378669507, "volume": 135.84000000000003, "diffusion": 5.548, "adsorption": -44.239, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.7041271253131312, "volume": 142.72000000000003, "diffusion": 6.751, "adsorption": -51.174, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.74831149963087, "volume": 154.97600000000003, "diffusion": 5.541, "adsorption": -46.325, "is_selected": false, "rank": 75}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.7497791932739648, "volume": 138.20800000000003, "diffusion": 5.901, "adsorption": -44.976, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7519962558419371, "volume": 156.36000000000004, "diffusion": 5.865, "adsorption": -47.564, "is_selected": false, "rank": 77}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7534770376571428, "volume": 138.94400000000005, "diffusion": 7.198, "adsorption": -51.066, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.7641446732089419, "volume": 146.32800000000003, "diffusion": 6.235, "adsorption": -47.089, "is_selected": false, "rank": 79}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7695859030308883, "volume": 131.22400000000002, "diffusion": 5.967, "adsorption": -43.81, "is_selected": false, "rank": 80}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.7749948437071925, "volume": 129.54400000000004, "diffusion": 6.648, "adsorption": -45.759, "is_selected": false, "rank": 81}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7814317276849454, "volume": 146.86400000000003, "diffusion": 5.853, "adsorption": -45.594, "is_selected": false, "rank": 82}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8036023520635696, "volume": 146.21600000000004, "diffusion": 6.386, "adsorption": -46.92, "is_selected": false, "rank": 83}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.8091748408214553, "volume": 127.84800000000003, "diffusion": 5.708, "adsorption": -58.696, "is_selected": false, "rank": 84}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8142193539377949, "volume": 138.90400000000002, "diffusion": 5.145, "adsorption": -41.456, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.8189366278493169, "volume": 135.25600000000003, "diffusion": 5.506, "adsorption": -58.419, "is_selected": false, "rank": 86}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8463745474472776, "volume": 138.66400000000004, "diffusion": 6.771, "adsorption": -46.358, "is_selected": false, "rank": 87}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8541035487926314, "volume": 149.11200000000002, "diffusion": 6.015, "adsorption": -45.263, "is_selected": false, "rank": 88}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.8563915792821573, "volume": 140.33600000000004, "diffusion": 4.66, "adsorption": -41.428, "is_selected": false, "rank": 89}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8630061861999037, "volume": 132.83200000000002, "diffusion": 5.801, "adsorption": -41.93, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8808635329872547, "volume": 156.29600000000005, "diffusion": 5.464, "adsorption": -44.046, "is_selected": false, "rank": 91}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8816708808220339, "volume": 128.44800000000004, "diffusion": 5.738, "adsorption": -59.718, "is_selected": false, "rank": 92}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.882944781380633, "volume": 145.04000000000002, "diffusion": 5.833, "adsorption": -56.909, "is_selected": false, "rank": 93}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 0.9036501780523756, "volume": 154.88800000000003, "diffusion": 6.063, "adsorption": -45.469, "is_selected": false, "rank": 94}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9079601956420681, "volume": 138.16800000000003, "diffusion": 7.331, "adsorption": -47.138, "is_selected": false, "rank": 95}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9312070293310839, "volume": 128.72000000000003, "diffusion": 5.812, "adsorption": -40.203, "is_selected": false, "rank": 96}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.9373585195968346, "volume": 151.34400000000002, "diffusion": 5.277, "adsorption": -41.721, "is_selected": false, "rank": 97}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.9707172544423301, "volume": 145.70400000000004, "diffusion": 5.187, "adsorption": -60.454, "is_selected": false, "rank": 98}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9831862672851569, "volume": 135.75200000000004, "diffusion": 5.794, "adsorption": -40.336, "is_selected": false, "rank": 99}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0211576236046622, "volume": 156.32000000000005, "diffusion": 4.678, "adsorption": -41.027, "is_selected": false, "rank": 100}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.0519143687581194, "volume": 151.26400000000004, "diffusion": 5.31, "adsorption": -39.902, "is_selected": false, "rank": 101}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1353860576810992, "volume": 147.05600000000004, "diffusion": 5.566, "adsorption": -38.73, "is_selected": false, "rank": 102}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1910843380275953, "volume": 137.57600000000002, "diffusion": 6.079, "adsorption": -38.091, "is_selected": false, "rank": 103}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2102018591704566, "volume": 145.84000000000003, "diffusion": 6.225, "adsorption": -39.513, "is_selected": false, "rank": 104}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2172314836026215, "volume": 123.71200000000003, "diffusion": 5.947, "adsorption": -65.349, "is_selected": false, "rank": 105}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2227666588828887, "volume": 155.36000000000004, "diffusion": 5.69, "adsorption": -38.941, "is_selected": false, "rank": 106}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2236982179261051, "volume": 136.38400000000004, "diffusion": 5.393, "adsorption": -35.054, "is_selected": false, "rank": 107}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.228572425166984, "volume": 157.69600000000003, "diffusion": 5.521, "adsorption": -38.628, "is_selected": false, "rank": 108}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2914402454922336, "volume": 146.23200000000003, "diffusion": 6.958, "adsorption": -40.681, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770968633.4327004}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "timestamp": 1770968633.7156057}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/7282d3fc-1562-4403-8dbe-8a76e69ee6fd.log", "timestamp": 1770968633.9049528}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770968634.2913306}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6640, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770968634.3262317}, {"type": "log", "level": "info", "message": "远程日志已启动: 7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "timestamp": 1770968634.4697778}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770968634.6888883}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968634.9224317}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770968635.1323636}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770968635.327885}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.66, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770968635.5745625}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770968635.7787228}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968635.960068}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770968635.9838965}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6640, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770968636.0053344}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770968636.028685}, {"type": "log", "level": "info", "message": "远程日志已启动: 7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "timestamp": 1770968636.0518842}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770968636.222923}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770968636.4330597}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770968636.4634285}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968636.668959}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968636.8762276}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770968637.071783}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968637.2944963}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770968637.3194046}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.05 秒", "timestamp": 1770968637.6429157}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770968637.9036927}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.05s, 速度: 3526.66 mol/s", "timestamp": 1770968637.925684}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.04877138137817383, "speed": 3526.658362745769, "timestamp": 1770968637.9508514}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770968638.2231498}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770968638.4780638}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770968638.733884}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770968638.7628062}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770968638.7892768}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770968639.0827012}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770968650.734822}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0804", "timestamp": 1770968650.7586215}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.08035795738204997, "timestamp": 1770968650.784868}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968651.1097808}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968651.1363778}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770968651.158078}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968651.4360359}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0804", "timestamp": 1770968651.6878908}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.02", "timestamp": 1770968652.0562918}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9590", "timestamp": 1770968652.323382}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.0230", "timestamp": 1770968652.733927}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 15.45s) ==========", "timestamp": 1770968652.773849}, {"type": "layer_end", "layer": 0, "duration": 15.45442247390747, "timestamp": 1770968652.806208}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968653.223039}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968653.5385003}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770968653.821166}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968654.1054122}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770968654.1364303}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770968654.3728726}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770968654.605036}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 8296.24 mol/s", "timestamp": 1770968654.6338394}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0036160945892333984, "speed": 8296.243159491, "timestamp": 1770968654.662481}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770968654.9099197}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770968654.9351623}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770968654.9587753}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770968655.1835544}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968657.674809}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0040", "timestamp": 1770968657.703114}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.00397599942167279, "timestamp": 1770968657.7241664}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968657.8926063}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968657.9149256}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770968657.9348729}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968658.1611395}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0040", "timestamp": 1770968658.399098}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.22", "timestamp": 1770968658.6424096}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9710", "timestamp": 1770968658.8669066}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.2300", "timestamp": 1770968659.107015}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 5.00s) ==========", "timestamp": 1770968659.1417458}, {"type": "layer_end", "layer": 1, "duration": 5.004692792892456, "timestamp": 1770968659.1671224}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968659.3897717}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968659.7304904}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770968659.9583392}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968660.1707559}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770968660.1917963}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770968660.381983}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770968660.590828}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 16236.85 mol/s", "timestamp": 1770968660.6183271}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0015397071838378906, "speed": 16236.85351502013, "timestamp": 1770968660.645205}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770968660.8394878}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770968660.8636115}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770968660.8879404}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770968661.082774}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968663.102584}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1220", "timestamp": 1770968663.1246905}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12198371364418253, "timestamp": 1770968663.1472392}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968663.35755}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968663.3862524}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770968663.4181886}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968663.619118}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1220", "timestamp": 1770968663.8402774}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770968664.0228198}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770968664.2558062}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770968664.4270957}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 4.26s) ==========", "timestamp": 1770968664.4517112}, {"type": "layer_end", "layer": 2, "duration": 4.259230613708496, "timestamp": 1770968664.4708004}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968664.6585958}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968664.885638}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770968665.1381915}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968665.3542266}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770968665.381339}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770968665.5957055}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770968665.7887769}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 17678.84 mol/s", "timestamp": 1770968665.8105185}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.001131296157836914, "speed": 17678.836670179135, "timestamp": 1770968665.8340266}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770968666.0151365}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770968666.0393853}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770968666.060715}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770968666.237321}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968668.2851918}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1227", "timestamp": 1770968668.3085554}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1227351514178707, "timestamp": 1770968668.3322463}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968668.536115}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968668.5677214}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]"], "timestamp": 1770968668.591243}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968668.9401598}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1227", "timestamp": 1770968669.1712236}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770968669.3589995}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2300", "timestamp": 1770968669.5385091}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.4090", "timestamp": 1770968669.7372036}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 4.38s) ==========", "timestamp": 1770968669.766459}, {"type": "layer_end", "layer": 3, "duration": 4.384103298187256, "timestamp": 1770968669.7919586}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968670.036017}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968670.2476234}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770968670.4802926}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968670.6756446}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770968670.7000968}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770968670.9151611}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770968671.1466978}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 11956.40 mol/s", "timestamp": 1770968671.175138}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0012545585632324219, "speed": 11956.396807297606, "timestamp": 1770968671.2019093}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770968671.4396803}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770968671.4633102}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770968671.4863913}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770968671.7082653}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968673.083621}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1225", "timestamp": 1770968673.1094193}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1225302791439699, "timestamp": 1770968673.135733}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968673.3328576}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968673.3539205}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770968673.3801455}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968673.5960007}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1225", "timestamp": 1770968673.813764}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.24", "timestamp": 1770968674.0480459}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770968674.2313337}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770968674.4423451}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 3.78s) ==========", "timestamp": 1770968674.479538}, {"type": "layer_end", "layer": 4, "duration": 3.7779622077941895, "timestamp": 1770968674.5059524}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968674.7451253}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968674.9739385}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770968675.2121465}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968675.4172027}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770968675.451713}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770968675.7071147}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770968675.9182572}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 5979.90 mol/s", "timestamp": 1770968675.9420693}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0016722679138183594, "speed": 5979.903051040776, "timestamp": 1770968675.9672716}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770968676.1649647}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770968676.188068}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770968676.2140489}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770968676.4354448}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968677.5748494}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1227", "timestamp": 1770968677.6079655}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12267486094112155, "timestamp": 1770968677.6384459}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968677.8916318}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968677.9230607}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770968677.949304}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968678.2087874}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1227", "timestamp": 1770968678.4561079}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.22", "timestamp": 1770968678.7184005}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770968679.0403984}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770968679.4321573}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 4.10s) ==========", "timestamp": 1770968679.5540893}, {"type": "layer_end", "layer": 5, "duration": 4.101240634918213, "timestamp": 1770968679.6450381}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968680.0236907}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968680.294414}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770968680.5883362}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968680.877008}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770968680.914261}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770968681.1927216}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770968681.430319}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 9939.11 mol/s", "timestamp": 1770968681.4538555}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0005030632019042969, "speed": 9939.109004739337, "timestamp": 1770968681.4825966}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770968681.6976573}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770968681.724455}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770968681.751464}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770968681.9561865}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770968682.6513789}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1037", "timestamp": 1770968682.6732807}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1037411633790206, "timestamp": 1770968682.6982567}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770968682.907617}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770968682.935994}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770968682.9627767}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968683.1922653}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1037", "timestamp": 1770968683.3992693}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.37", "timestamp": 1770968683.5799835}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0720", "timestamp": 1770968683.778039}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.8780", "timestamp": 1770968684.2671003}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 3.38s) ==========", "timestamp": 1770968684.2961068}, {"type": "layer_end", "layer": 6, "duration": 3.3816802501678467, "timestamp": 1770968684.3180375}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770968684.9516008}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968685.1486757}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770968685.3484137}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 47.86 秒", "timestamp": 1770968685.5384223}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770968685.7860625}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770968686.012696}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770968686.2605338}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770968686.5209813}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2043, 体积: 126.82, 扩散: 4.8250, 吸附: -49.7450", "timestamp": 1770968686.7722478}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968686.9831812}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2196, 体积: 110.23, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770968687.186823}, {"type": "log", "level": "info", "message": "INFO -   3. [H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770968687.380308}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2273, 体积: 132.74, 扩散: 5.0570, 吸附: -50.0540", "timestamp": 1770968687.5995097}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770968687.8398335}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2603, 体积: 122.32, 扩散: 4.6670, 吸附: -48.6600", "timestamp": 1770968688.1303787}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770968688.453204}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.4524, 体积: 121.73, 扩散: 5.4980, 吸附: -54.3560", "timestamp": 1770968688.9288917}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 55.55s", "timestamp": 1770968688.9703798}, {"type": "finish", "total_results": 5, "duration": 55.54601311683655, "timestamp": 1770968689.0060272}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/7282d3fc-1562-4403-8dbe-8a76e69ee6fd.json", "timestamp": 1770968689.051389}, {"type": "log", "level": "info", "message": "远程日志已上传: 7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "timestamp": 1770968689.098471}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770968689.334169}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770968689.5717459}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770968689.5948198}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6640, 扩散: 4.9710, 吸附: -50.2300", "timestamp": 1770968689.6217136}, {"type": "log", "level": "info", "message": "远程日志已启动: 7282d3fc-1562-4403-8dbe-8a76e69ee6fd", "timestamp": 1770968689.6674452}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770968689.81597}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 56.42s", "timestamp": 1770968689.8413897}, {"type": "finish", "total_results": 5, "duration": 56.417099952697754, "timestamp": 1770968689.8647962}]}