{"metadata": {"log_name": "e91599ef-ce17-4c46-8f73-cc9132cb08bc", "start_time": 1770892091.9649382, "end_time": 1770892118.357351, "duration": 26.392412900924683, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.03200000000002, "diffusion": 5.225, "adsorption": -51.449}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770892093.331767, "end_time": 1770892103.8514433, "duration": 10.519676208496094, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.1915853760150385}, {"layer_num": 1, "start_time": 1770892104.009936, "end_time": 1770892106.8105903, "duration": 2.800654172897339, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0777909151216118}, {"layer_num": 2, "start_time": 1770892107.0256166, "end_time": 1770892109.6365714, "duration": 2.610954761505127, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.01594704662738313}, {"layer_num": 3, "start_time": 1770892109.910453, "end_time": 1770892111.8413339, "duration": 1.9308807849884033, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18727016922000803}, {"layer_num": 4, "start_time": 1770892112.0741246, "end_time": 1770892113.7498555, "duration": 1.6757309436798096, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.09072794622234821}, {"layer_num": 5, "start_time": 1770892113.980673, "end_time": 1770892115.3252819, "duration": 1.3446087837219238, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12420031848498267}, {"layer_num": 6, "start_time": 1770892115.5221794, "end_time": 1770892116.7393544, "duration": 1.217175006866455, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.17515722407446016}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.1915853760150385, "volume": 123.14400000000003, "diffusion": 5.055, "adsorption": -52.133, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.19382368876846262, "volume": 110.19200000000002, "diffusion": 4.959, "adsorption": -49.023, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.21701993554110915, "volume": 128.32800000000003, "diffusion": 5.293, "adsorption": -50.802, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2230482554355525, "volume": 127.09600000000003, "diffusion": 5.259, "adsorption": -50.395, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.2438490068339641, "volume": 119.52000000000002, "diffusion": 5.477, "adsorption": -49.573, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.25049205495498317, "volume": 126.32800000000003, "diffusion": 4.8, "adsorption": -51.088, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2798783479995388, "volume": 129.56000000000003, "diffusion": 5.75, "adsorption": -51.482, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.2876837202003122, "volume": 120.54400000000003, "diffusion": 5.139, "adsorption": -54.462, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.28977793029207033, "volume": 137.48000000000005, "diffusion": 5.319, "adsorption": -51.832, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2951278864463787, "volume": 126.69600000000003, "diffusion": 5.817, "adsorption": -50.928, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.31145743864109826, "volume": 128.16000000000003, "diffusion": 5.087, "adsorption": -49.386, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.32315373207110826, "volume": 131.12800000000004, "diffusion": 5.126, "adsorption": -49.52, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.32498199787674453, "volume": 121.48800000000003, "diffusion": 4.673, "adsorption": -49.473, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.33353913245199507, "volume": 129.67200000000003, "diffusion": 4.768, "adsorption": -50.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.335464379374274, "volume": 133.21600000000004, "diffusion": 5.159, "adsorption": -49.526, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.33661799491770084, "volume": 126.88000000000002, "diffusion": 4.963, "adsorption": -53.736, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3435313955814636, "volume": 122.44800000000004, "diffusion": 5.224, "adsorption": -55.402, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.3497391908933085, "volume": 130.12000000000003, "diffusion": 5.682, "adsorption": -50.082, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.3512442894901192, "volume": 127.79200000000003, "diffusion": 4.546, "adsorption": -52.476, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3738856386867272, "volume": 137.92000000000004, "diffusion": 5.57, "adsorption": -52.382, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.37782011050755926, "volume": 128.83200000000002, "diffusion": 4.944, "adsorption": -54.076, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3809298001507764, "volume": 131.51200000000003, "diffusion": 5.041, "adsorption": -54.03, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3992583998668279, "volume": 118.87200000000003, "diffusion": 5.825, "adsorption": -47.949, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4024392845530809, "volume": 131.92000000000004, "diffusion": 5.449, "adsorption": -54.204, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.41151987587270716, "volume": 144.82400000000004, "diffusion": 5.674, "adsorption": -51.288, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.41260961869495555, "volume": 128.68000000000004, "diffusion": 6.276, "adsorption": -50.729, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.4126688459042918, "volume": 134.69600000000003, "diffusion": 4.605, "adsorption": -50.251, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.444332947351896, "volume": 126.76800000000003, "diffusion": 4.886, "adsorption": -47.55, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.44473336623997445, "volume": 124.45600000000003, "diffusion": 5.912, "adsorption": -48.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.4599883841094331, "volume": 134.96800000000005, "diffusion": 5.236, "adsorption": -55.415, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4606476529441743, "volume": 139.18400000000003, "diffusion": 6.041, "adsorption": -50.771, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.46086621329755506, "volume": 139.35200000000003, "diffusion": 4.694, "adsorption": -53.04, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.46851750803482123, "volume": 118.08800000000002, "diffusion": 5.917, "adsorption": -46.941, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.47202302632878446, "volume": 122.58400000000003, "diffusion": 4.762, "adsorption": -56.068, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.48234350826873, "volume": 127.50400000000003, "diffusion": 4.315, "adsorption": -48.887, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.4830401719377023, "volume": 138.95200000000003, "diffusion": 4.667, "adsorption": -49.504, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4866959645190272, "volume": 138.11200000000002, "diffusion": 5.214, "adsorption": -47.515, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5027624358154119, "volume": 133.33600000000004, "diffusion": 5.955, "adsorption": -48.855, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5173861102917263, "volume": 129.91200000000003, "diffusion": 5.078, "adsorption": -56.741, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.5185024252467054, "volume": 155.08800000000005, "diffusion": 5.134, "adsorption": -53.02, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5288079169435113, "volume": 133.95200000000003, "diffusion": 4.808, "adsorption": -47.477, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5298310287283985, "volume": 146.48000000000005, "diffusion": 5.5, "adsorption": -53.952, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.529972573910998, "volume": 142.06400000000002, "diffusion": 5.871, "adsorption": -53.425, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5380399551144014, "volume": 120.09600000000003, "diffusion": 6.4, "adsorption": -47.647, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5463324587163396, "volume": 129.17600000000004, "diffusion": 4.624, "adsorption": -47.036, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.5581030870051608, "volume": 134.96800000000005, "diffusion": 5.109, "adsorption": -56.753, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.560534237669902, "volume": 130.17600000000004, "diffusion": 6.406, "adsorption": -48.852, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5658340618956099, "volume": 144.13600000000002, "diffusion": 5.687, "adsorption": -48.577, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.573989078864084, "volume": 127.11200000000002, "diffusion": 5.198, "adsorption": -44.356, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.5782884019741436, "volume": 150.88800000000003, "diffusion": 5.748, "adsorption": -49.616, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.5835602152570787, "volume": 128.60800000000003, "diffusion": 5.957, "adsorption": -56.159, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5860302744169794, "volume": 123.40000000000003, "diffusion": 4.599, "adsorption": -45.537, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6140799128876424, "volume": 146.43200000000004, "diffusion": 4.591, "adsorption": -54.226, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.6191433305683975, "volume": 135.96000000000004, "diffusion": 5.487, "adsorption": -45.732, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6202411243994076, "volume": 138.38400000000004, "diffusion": 5.317, "adsorption": -45.533, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6204042406812682, "volume": 132.41600000000003, "diffusion": 4.428, "adsorption": -46.914, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.6260998948570267, "volume": 142.19200000000004, "diffusion": 5.37, "adsorption": -56.698, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.6319457448891426, "volume": 150.77600000000004, "diffusion": 5.722, "adsorption": -48.593, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6394404471773476, "volume": 118.97600000000003, "diffusion": 5.75, "adsorption": -43.6, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.6396780533903739, "volume": 146.75200000000004, "diffusion": 5.037, "adsorption": -46.819, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.6407477101544445, "volume": 154.38400000000004, "diffusion": 5.384, "adsorption": -47.895, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6531036987285448, "volume": 154.20000000000005, "diffusion": 5.751, "adsorption": -48.859, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.6595885581236967, "volume": 145.74400000000003, "diffusion": 5.989, "adsorption": -48.211, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.66708413963171, "volume": 127.03200000000002, "diffusion": 4.957, "adsorption": -59.36, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.672580926698398, "volume": 127.64800000000002, "diffusion": 4.949, "adsorption": -43.566, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.6768070880187729, "volume": 145.64000000000004, "diffusion": 5.927, "adsorption": -55.202, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6908893188353841, "volume": 135.70400000000004, "diffusion": 3.906, "adsorption": -47.931, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.6917037822639928, "volume": 136.56800000000004, "diffusion": 5.677, "adsorption": -45.206, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.710634882780167, "volume": 156.04000000000005, "diffusion": 5.669, "adsorption": -47.89, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7117919172508911, "volume": 149.20800000000003, "diffusion": 5.76, "adsorption": -47.104, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.7161458257602717, "volume": 146.27200000000005, "diffusion": 5.722, "adsorption": -56.451, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7173014178221726, "volume": 139.28800000000004, "diffusion": 7.222, "adsorption": -50.262, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7215573841154244, "volume": 130.26400000000004, "diffusion": 6.593, "adsorption": -46.718, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7245076263964796, "volume": 133.52800000000002, "diffusion": 6.139, "adsorption": -45.686, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7395633580080259, "volume": 128.03200000000004, "diffusion": 5.947, "adsorption": -58.957, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.7397551863017826, "volume": 145.96800000000005, "diffusion": 5.397, "adsorption": -57.97, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7401162446710893, "volume": 155.32000000000005, "diffusion": 4.81, "adsorption": -47.177, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.7576884241842105, "volume": 128.65600000000003, "diffusion": 5.903, "adsorption": -59.315, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.774479364769596, "volume": 147.64000000000004, "diffusion": 6.449, "adsorption": -48.046, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7750227618486166, "volume": 130.67200000000003, "diffusion": 5.967, "adsorption": -43.81, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7867087100112711, "volume": 143.85600000000002, "diffusion": 4.885, "adsorption": -44.345, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7958259301281052, "volume": 146.84000000000003, "diffusion": 5.775, "adsorption": -45.343, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7970994954743311, "volume": 138.31200000000004, "diffusion": 7.164, "adsorption": -48.551, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8032331288472984, "volume": 134.10400000000004, "diffusion": 5.754, "adsorption": -43.162, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.8250150485535273, "volume": 138.24000000000004, "diffusion": 5.697, "adsorption": -43.246, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.8326553400174233, "volume": 141.76800000000003, "diffusion": 6.392, "adsorption": -45.946, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8457676554704086, "volume": 151.72800000000004, "diffusion": 5.469, "adsorption": -44.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8718698995678972, "volume": 140.78400000000002, "diffusion": 5.892, "adsorption": -43.479, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8913935341627042, "volume": 138.29600000000002, "diffusion": 4.896, "adsorption": -41.647, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8971528554915866, "volume": 156.08800000000005, "diffusion": 5.436, "adsorption": -43.934, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9014410878613752, "volume": 138.45600000000005, "diffusion": 7.334, "adsorption": -47.342, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9080061631499823, "volume": 134.16800000000003, "diffusion": 6.044, "adsorption": -42.327, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.911352625252047, "volume": 130.84000000000003, "diffusion": 6.427, "adsorption": -43.008, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9180165662502908, "volume": 139.90400000000002, "diffusion": 5.855, "adsorption": -42.429, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.9218312008347245, "volume": 146.47200000000004, "diffusion": 6.467, "adsorption": -45.396, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.92321128837318, "volume": 140.44800000000004, "diffusion": 4.64, "adsorption": -42.277, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9313934306443704, "volume": 137.42400000000004, "diffusion": 5.543, "adsorption": -40.789, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9642924927061771, "volume": 132.36800000000002, "diffusion": 5.31, "adsorption": -38.672, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9897145594794978, "volume": 147.56000000000003, "diffusion": 5.679, "adsorption": -41.815, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.012091862840145, "volume": 152.10400000000004, "diffusion": 5.088, "adsorption": -41.099, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0256470801690747, "volume": 155.94400000000005, "diffusion": 4.872, "adsorption": -42.174, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.041542620804747, "volume": 122.68800000000003, "diffusion": 6.056, "adsorption": -64.611, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 1.045813987584448, "volume": 134.94400000000005, "diffusion": 5.762, "adsorption": -39.159, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.121032079008458, "volume": 156.16800000000003, "diffusion": 6.343, "adsorption": -43.084, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2126821241008616, "volume": 136.46400000000003, "diffusion": 5.361, "adsorption": -35.218, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2321024743576994, "volume": 157.32000000000005, "diffusion": 5.281, "adsorption": -37.873, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2651787342693919, "volume": 145.88000000000002, "diffusion": 6.756, "adsorption": -40.364, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3559085324468896, "volume": 155.20000000000005, "diffusion": 6.077, "adsorption": -38.032, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.4598369799101731, "volume": 146.35200000000003, "diffusion": 6.128, "adsorption": -35.038, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0777909151216118, "volume": 110.69600000000003, "diffusion": 5.546, "adsorption": -51.626, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2155707521744115, "volume": 126.25600000000003, "diffusion": 5.224, "adsorption": -50.284, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3176131812980433, "volume": 118.79200000000003, "diffusion": 5.252, "adsorption": -47.456, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3700729446048421, "volume": 128.72000000000003, "diffusion": 5.197, "adsorption": -54.791, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3836718076271656, "volume": 122.84800000000003, "diffusion": 4.664, "adsorption": -48.708, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.01594704662738313, "volume": 110.83200000000002, "diffusion": 5.242, "adsorption": -51.356, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.227301470395974, "volume": 127.14400000000003, "diffusion": 5.25, "adsorption": -50.3, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24923838100286494, "volume": 122.79200000000003, "diffusion": 4.897, "adsorption": -50.24, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3587474013032783, "volume": 118.64800000000002, "diffusion": 5.45, "adsorption": -47.378, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.4735631562189071, "volume": 128.38400000000004, "diffusion": 5.129, "adsorption": -56.395, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.18727016922000803, "volume": 110.41600000000003, "diffusion": 5.077, "adsorption": -48.783, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.2098067623194564, "volume": 128.76800000000003, "diffusion": 5.292, "adsorption": -50.991, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.23200371557206606, "volume": 127.46400000000003, "diffusion": 5.235, "adsorption": -50.22, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.33217063066371794, "volume": 119.34400000000002, "diffusion": 5.279, "adsorption": -47.381, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.38160687197766946, "volume": 122.37600000000003, "diffusion": 4.667, "adsorption": -48.66, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.09072794622234821, "volume": 110.33600000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.24885399233792485, "volume": 126.89600000000003, "diffusion": 5.336, "adsorption": -50.174, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.26566916453313927, "volume": 119.13600000000002, "diffusion": 5.378, "adsorption": -48.814, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.32807250097145013, "volume": 122.19200000000004, "diffusion": 4.711, "adsorption": -49.405, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.4933329603079765, "volume": 128.99200000000002, "diffusion": 5.128, "adsorption": -56.636, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.12420031848498267, "volume": 110.52800000000002, "diffusion": 4.971, "adsorption": -50.23, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.19505267466317816, "volume": 122.92800000000003, "diffusion": 5.051, "adsorption": -52.213, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.36392692643332125, "volume": 125.91200000000003, "diffusion": 5.065, "adsorption": -48.208, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3697724230412503, "volume": 119.05600000000003, "diffusion": 5.434, "adsorption": -47.2, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.37176420192518134, "volume": 128.65600000000003, "diffusion": 5.027, "adsorption": -54.272, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.17515722407446016, "volume": 122.12800000000003, "diffusion": 4.904, "adsorption": -51.384, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.18536648108849274, "volume": 110.46400000000003, "diffusion": 5.081, "adsorption": -48.81, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.209050568581087, "volume": 128.37600000000003, "diffusion": 5.296, "adsorption": -50.956, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.32664310322621787, "volume": 118.80800000000002, "diffusion": 5.016, "adsorption": -47.901, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3607809246565733, "volume": 126.93600000000004, "diffusion": 5.152, "adsorption": -48.136, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.03200000000002, "diffusion": 5.225, "adsorption": -51.449}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.17515722407446016, "volume": 122.12800000000003, "diffusion": 4.904, "adsorption": -51.384, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.18536648108849274, "volume": 110.46400000000003, "diffusion": 5.081, "adsorption": -48.81, "is_selected": false, "rank": 2}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 6, "score": 0.209050568581087, "volume": 128.37600000000003, "diffusion": 5.296, "adsorption": -50.956, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6, "score": 0.3607809246565733, "volume": 126.93600000000004, "diffusion": 5.152, "adsorption": -48.136, "is_selected": false, "rank": 5}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.32664310322621787, "volume": 118.80800000000002, "diffusion": 5.016, "adsorption": -47.901, "is_selected": false, "rank": 4}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.25049205495498317, "volume": 126.32800000000003, "diffusion": 4.8, "adsorption": -51.088, "is_selected": false, "rank": 6}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.2798783479995388, "volume": 129.56000000000003, "diffusion": 5.75, "adsorption": -51.482, "is_selected": false, "rank": 7}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.2876837202003122, "volume": 120.54400000000003, "diffusion": 5.139, "adsorption": -54.462, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.28977793029207033, "volume": 137.48000000000005, "diffusion": 5.319, "adsorption": -51.832, "is_selected": false, "rank": 9}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.2951278864463787, "volume": 126.69600000000003, "diffusion": 5.817, "adsorption": -50.928, "is_selected": false, "rank": 10}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.31145743864109826, "volume": 128.16000000000003, "diffusion": 5.087, "adsorption": -49.386, "is_selected": false, "rank": 11}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.32315373207110826, "volume": 131.12800000000004, "diffusion": 5.126, "adsorption": -49.52, "is_selected": false, "rank": 12}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.32498199787674453, "volume": 121.48800000000003, "diffusion": 4.673, "adsorption": -49.473, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.33353913245199507, "volume": 129.67200000000003, "diffusion": 4.768, "adsorption": -50.29, "is_selected": false, "rank": 14}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.335464379374274, "volume": 133.21600000000004, "diffusion": 5.159, "adsorption": -49.526, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.33661799491770084, "volume": 126.88000000000002, "diffusion": 4.963, "adsorption": -53.736, "is_selected": false, "rank": 16}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.3435313955814636, "volume": 122.44800000000004, "diffusion": 5.224, "adsorption": -55.402, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.3497391908933085, "volume": 130.12000000000003, "diffusion": 5.682, "adsorption": -50.082, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.3512442894901192, "volume": 127.79200000000003, "diffusion": 4.546, "adsorption": -52.476, "is_selected": false, "rank": 19}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3738856386867272, "volume": 137.92000000000004, "diffusion": 5.57, "adsorption": -52.382, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.37782011050755926, "volume": 128.83200000000002, "diffusion": 4.944, "adsorption": -54.076, "is_selected": false, "rank": 21}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3809298001507764, "volume": 131.51200000000003, "diffusion": 5.041, "adsorption": -54.03, "is_selected": false, "rank": 22}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3992583998668279, "volume": 118.87200000000003, "diffusion": 5.825, "adsorption": -47.949, "is_selected": false, "rank": 23}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4024392845530809, "volume": 131.92000000000004, "diffusion": 5.449, "adsorption": -54.204, "is_selected": false, "rank": 24}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.41151987587270716, "volume": 144.82400000000004, "diffusion": 5.674, "adsorption": -51.288, "is_selected": false, "rank": 25}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.41260961869495555, "volume": 128.68000000000004, "diffusion": 6.276, "adsorption": -50.729, "is_selected": false, "rank": 26}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.4126688459042918, "volume": 134.69600000000003, "diffusion": 4.605, "adsorption": -50.251, "is_selected": false, "rank": 27}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.444332947351896, "volume": 126.76800000000003, "diffusion": 4.886, "adsorption": -47.55, "is_selected": false, "rank": 28}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.44473336623997445, "volume": 124.45600000000003, "diffusion": 5.912, "adsorption": -48.325, "is_selected": false, "rank": 29}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4599883841094331, "volume": 134.96800000000005, "diffusion": 5.236, "adsorption": -55.415, "is_selected": false, "rank": 30}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4606476529441743, "volume": 139.18400000000003, "diffusion": 6.041, "adsorption": -50.771, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.46086621329755506, "volume": 139.35200000000003, "diffusion": 4.694, "adsorption": -53.04, "is_selected": false, "rank": 32}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.46851750803482123, "volume": 118.08800000000002, "diffusion": 5.917, "adsorption": -46.941, "is_selected": false, "rank": 33}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.47202302632878446, "volume": 122.58400000000003, "diffusion": 4.762, "adsorption": -56.068, "is_selected": false, "rank": 34}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.48234350826873, "volume": 127.50400000000003, "diffusion": 4.315, "adsorption": -48.887, "is_selected": false, "rank": 35}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4830401719377023, "volume": 138.95200000000003, "diffusion": 4.667, "adsorption": -49.504, "is_selected": false, "rank": 36}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4866959645190272, "volume": 138.11200000000002, "diffusion": 5.214, "adsorption": -47.515, "is_selected": false, "rank": 37}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5027624358154119, "volume": 133.33600000000004, "diffusion": 5.955, "adsorption": -48.855, "is_selected": false, "rank": 38}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5173861102917263, "volume": 129.91200000000003, "diffusion": 5.078, "adsorption": -56.741, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5185024252467054, "volume": 155.08800000000005, "diffusion": 5.134, "adsorption": -53.02, "is_selected": false, "rank": 40}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5288079169435113, "volume": 133.95200000000003, "diffusion": 4.808, "adsorption": -47.477, "is_selected": false, "rank": 41}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5298310287283985, "volume": 146.48000000000005, "diffusion": 5.5, "adsorption": -53.952, "is_selected": false, "rank": 42}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.529972573910998, "volume": 142.06400000000002, "diffusion": 5.871, "adsorption": -53.425, "is_selected": false, "rank": 43}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5380399551144014, "volume": 120.09600000000003, "diffusion": 6.4, "adsorption": -47.647, "is_selected": false, "rank": 44}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5463324587163396, "volume": 129.17600000000004, "diffusion": 4.624, "adsorption": -47.036, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.5581030870051608, "volume": 134.96800000000005, "diffusion": 5.109, "adsorption": -56.753, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.560534237669902, "volume": 130.17600000000004, "diffusion": 6.406, "adsorption": -48.852, "is_selected": false, "rank": 47}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5658340618956099, "volume": 144.13600000000002, "diffusion": 5.687, "adsorption": -48.577, "is_selected": false, "rank": 48}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.573989078864084, "volume": 127.11200000000002, "diffusion": 5.198, "adsorption": -44.356, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5782884019741436, "volume": 150.88800000000003, "diffusion": 5.748, "adsorption": -49.616, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5835602152570787, "volume": 128.60800000000003, "diffusion": 5.957, "adsorption": -56.159, "is_selected": false, "rank": 51}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5860302744169794, "volume": 123.40000000000003, "diffusion": 4.599, "adsorption": -45.537, "is_selected": false, "rank": 52}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6140799128876424, "volume": 146.43200000000004, "diffusion": 4.591, "adsorption": -54.226, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6191433305683975, "volume": 135.96000000000004, "diffusion": 5.487, "adsorption": -45.732, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6202411243994076, "volume": 138.38400000000004, "diffusion": 5.317, "adsorption": -45.533, "is_selected": false, "rank": 55}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6204042406812682, "volume": 132.41600000000003, "diffusion": 4.428, "adsorption": -46.914, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6260998948570267, "volume": 142.19200000000004, "diffusion": 5.37, "adsorption": -56.698, "is_selected": false, "rank": 57}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6319457448891426, "volume": 150.77600000000004, "diffusion": 5.722, "adsorption": -48.593, "is_selected": false, "rank": 58}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6394404471773476, "volume": 118.97600000000003, "diffusion": 5.75, "adsorption": -43.6, "is_selected": false, "rank": 59}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.6396780533903739, "volume": 146.75200000000004, "diffusion": 5.037, "adsorption": -46.819, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.6407477101544445, "volume": 154.38400000000004, "diffusion": 5.384, "adsorption": -47.895, "is_selected": false, "rank": 61}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6531036987285448, "volume": 154.20000000000005, "diffusion": 5.751, "adsorption": -48.859, "is_selected": false, "rank": 62}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.6595885581236967, "volume": 145.74400000000003, "diffusion": 5.989, "adsorption": -48.211, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.66708413963171, "volume": 127.03200000000002, "diffusion": 4.957, "adsorption": -59.36, "is_selected": false, "rank": 64}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.672580926698398, "volume": 127.64800000000002, "diffusion": 4.949, "adsorption": -43.566, "is_selected": false, "rank": 65}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6768070880187729, "volume": 145.64000000000004, "diffusion": 5.927, "adsorption": -55.202, "is_selected": false, "rank": 66}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6908893188353841, "volume": 135.70400000000004, "diffusion": 3.906, "adsorption": -47.931, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6917037822639928, "volume": 136.56800000000004, "diffusion": 5.677, "adsorption": -45.206, "is_selected": false, "rank": 68}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.710634882780167, "volume": 156.04000000000005, "diffusion": 5.669, "adsorption": -47.89, "is_selected": false, "rank": 69}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7117919172508911, "volume": 149.20800000000003, "diffusion": 5.76, "adsorption": -47.104, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7161458257602717, "volume": 146.27200000000005, "diffusion": 5.722, "adsorption": -56.451, "is_selected": false, "rank": 71}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7173014178221726, "volume": 139.28800000000004, "diffusion": 7.222, "adsorption": -50.262, "is_selected": false, "rank": 72}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7215573841154244, "volume": 130.26400000000004, "diffusion": 6.593, "adsorption": -46.718, "is_selected": false, "rank": 73}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7245076263964796, "volume": 133.52800000000002, "diffusion": 6.139, "adsorption": -45.686, "is_selected": false, "rank": 74}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7395633580080259, "volume": 128.03200000000004, "diffusion": 5.947, "adsorption": -58.957, "is_selected": false, "rank": 75}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.7397551863017826, "volume": 145.96800000000005, "diffusion": 5.397, "adsorption": -57.97, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7401162446710893, "volume": 155.32000000000005, "diffusion": 4.81, "adsorption": -47.177, "is_selected": false, "rank": 77}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.7576884241842105, "volume": 128.65600000000003, "diffusion": 5.903, "adsorption": -59.315, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.774479364769596, "volume": 147.64000000000004, "diffusion": 6.449, "adsorption": -48.046, "is_selected": false, "rank": 79}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7750227618486166, "volume": 130.67200000000003, "diffusion": 5.967, "adsorption": -43.81, "is_selected": false, "rank": 80}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7867087100112711, "volume": 143.85600000000002, "diffusion": 4.885, "adsorption": -44.345, "is_selected": false, "rank": 81}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7958259301281052, "volume": 146.84000000000003, "diffusion": 5.775, "adsorption": -45.343, "is_selected": false, "rank": 82}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7970994954743311, "volume": 138.31200000000004, "diffusion": 7.164, "adsorption": -48.551, "is_selected": false, "rank": 83}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8032331288472984, "volume": 134.10400000000004, "diffusion": 5.754, "adsorption": -43.162, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.8250150485535273, "volume": 138.24000000000004, "diffusion": 5.697, "adsorption": -43.246, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.8326553400174233, "volume": 141.76800000000003, "diffusion": 6.392, "adsorption": -45.946, "is_selected": false, "rank": 86}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8457676554704086, "volume": 151.72800000000004, "diffusion": 5.469, "adsorption": -44.244, "is_selected": false, "rank": 87}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8718698995678972, "volume": 140.78400000000002, "diffusion": 5.892, "adsorption": -43.479, "is_selected": false, "rank": 88}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8913935341627042, "volume": 138.29600000000002, "diffusion": 4.896, "adsorption": -41.647, "is_selected": false, "rank": 89}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8971528554915866, "volume": 156.08800000000005, "diffusion": 5.436, "adsorption": -43.934, "is_selected": false, "rank": 90}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9014410878613752, "volume": 138.45600000000005, "diffusion": 7.334, "adsorption": -47.342, "is_selected": false, "rank": 91}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9080061631499823, "volume": 134.16800000000003, "diffusion": 6.044, "adsorption": -42.327, "is_selected": false, "rank": 92}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.911352625252047, "volume": 130.84000000000003, "diffusion": 6.427, "adsorption": -43.008, "is_selected": false, "rank": 93}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9180165662502908, "volume": 139.90400000000002, "diffusion": 5.855, "adsorption": -42.429, "is_selected": false, "rank": 94}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.9218312008347245, "volume": 146.47200000000004, "diffusion": 6.467, "adsorption": -45.396, "is_selected": false, "rank": 95}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.92321128837318, "volume": 140.44800000000004, "diffusion": 4.64, "adsorption": -42.277, "is_selected": false, "rank": 96}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9313934306443704, "volume": 137.42400000000004, "diffusion": 5.543, "adsorption": -40.789, "is_selected": false, "rank": 97}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9642924927061771, "volume": 132.36800000000002, "diffusion": 5.31, "adsorption": -38.672, "is_selected": false, "rank": 98}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9897145594794978, "volume": 147.56000000000003, "diffusion": 5.679, "adsorption": -41.815, "is_selected": false, "rank": 99}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.012091862840145, "volume": 152.10400000000004, "diffusion": 5.088, "adsorption": -41.099, "is_selected": false, "rank": 100}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0256470801690747, "volume": 155.94400000000005, "diffusion": 4.872, "adsorption": -42.174, "is_selected": false, "rank": 101}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.041542620804747, "volume": 122.68800000000003, "diffusion": 6.056, "adsorption": -64.611, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 1.045813987584448, "volume": 134.94400000000005, "diffusion": 5.762, "adsorption": -39.159, "is_selected": false, "rank": 103}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.121032079008458, "volume": 156.16800000000003, "diffusion": 6.343, "adsorption": -43.084, "is_selected": false, "rank": 104}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2126821241008616, "volume": 136.46400000000003, "diffusion": 5.361, "adsorption": -35.218, "is_selected": false, "rank": 105}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2321024743576994, "volume": 157.32000000000005, "diffusion": 5.281, "adsorption": -37.873, "is_selected": false, "rank": 106}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2651787342693919, "volume": 145.88000000000002, "diffusion": 6.756, "adsorption": -40.364, "is_selected": false, "rank": 107}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3559085324468896, "volume": 155.20000000000005, "diffusion": 6.077, "adsorption": -38.032, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.4598369799101731, "volume": 146.35200000000003, "diffusion": 6.128, "adsorption": -35.038, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770892091.9655683}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: e91599ef-ce17-4c46-8f73-cc9132cb08bc", "timestamp": 1770892091.9887645}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/e91599ef-ce17-4c46-8f73-cc9132cb08bc.log", "timestamp": 1770892092.0071347}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892092.3888888}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2250, 吸附: -51.4490", "timestamp": 1770892092.4222178}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770892092.4448867}, {"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770892092.4791424}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892092.516777}, {"type": "log", "level": "info", "message": "远程日志已启动: e91599ef-ce17-4c46-8f73-cc9132cb08bc", "timestamp": 1770892092.5200076}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892092.5409365}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770892092.5645642}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770892092.6201172}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770892092.6497805}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770892092.6730618}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.2250, 吸附: -51.4490", "timestamp": 1770892092.6975534}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.2250, 吸附: -51.4490", "timestamp": 1770892092.716627}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770892092.7360578}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770892092.7592556}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892092.7862513}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892092.810626}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892092.8405128}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2250, 吸附: -51.4490", "timestamp": 1770892092.8698344}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770892092.9037206}, {"type": "log", "level": "info", "message": "远程日志已启动: e91599ef-ce17-4c46-8f73-cc9132cb08bc", "timestamp": 1770892092.93395}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770892092.939922}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770892092.9946194}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770892093.0234063}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770892093.0531106}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770892093.0797877}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892093.108382}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892093.1331809}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892093.1616929}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892093.1886113}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770892093.2176476}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770892093.2469711}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892093.274115}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892093.2998583}, {"type": "log", "level": "info", "message": "========== 第 0 层开始 ==========", "timestamp": 1770892093.332672}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.04 秒", "timestamp": 1770892093.4073267}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.04 秒", "timestamp": 1770892093.4419281}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770892093.470855}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770892093.4991953}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.04s, 速度: 3891.28 mol/s", "timestamp": 1770892093.5262673}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.04420137405395508, "speed": 3891.2817459033195, "timestamp": 1770892093.562116}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770892093.6208642}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770892093.6559467}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770892093.6922765}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770892093.7296362}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770892093.75513}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770892093.7765126}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770892093.799139}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770892093.8185885}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770892093.8385377}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770892093.8647764}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770892103.5165567}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770892103.5332644}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H], Score: 0.1916", "timestamp": 1770892103.5500596}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.1915853760150385, "timestamp": 1770892103.5777593}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892103.600434}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892103.6238108}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892103.644647}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770892103.6648962}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892103.6864116}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892103.7023857}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1916", "timestamp": 1770892103.7211168}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1916", "timestamp": 1770892103.7386472}, {"type": "log", "level": "info", "message": "INFO -     体积: 123.14", "timestamp": 1770892103.7534447}, {"type": "log", "level": "info", "message": "INFO -     体积: 123.14", "timestamp": 1770892103.7716084}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0550", "timestamp": 1770892103.788133}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0550", "timestamp": 1770892103.8038628}, {"type": "log", "level": "info", "message": "INFO -     吸附: -52.1330", "timestamp": 1770892103.8185308}, {"type": "log", "level": "info", "message": "INFO -     吸附: -52.1330", "timestamp": 1770892103.8336945}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.52s) ==========", "timestamp": 1770892103.8521178}, {"type": "layer_end", "layer": 0, "duration": 10.519676208496094, "timestamp": 1770892103.8695638}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892103.8833768}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892103.8984559}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892103.9128914}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892103.9300632}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770892103.9457986}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770892103.960438}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892103.9758265}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892103.992115}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770892104.0101416}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770892104.029573}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770892104.0513244}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770892104.0752726}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770892104.0981762}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 32674.40 mol/s", "timestamp": 1770892104.1231096}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0009181499481201172, "speed": 32674.40145416775, "timestamp": 1770892104.145786}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770892104.1688266}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770892104.192092}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770892104.2116628}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770892104.2314587}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770892104.249515}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770892104.2672093}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892106.398065}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892106.426855}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0778", "timestamp": 1770892106.4460063}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0777909151216118, "timestamp": 1770892106.4663002}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892106.4855335}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892106.5045617}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892106.5257993}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770892106.5461226}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892106.5663502}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892106.590404}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0778", "timestamp": 1770892106.6178792}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0778", "timestamp": 1770892106.6452754}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.70", "timestamp": 1770892106.670318}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.70", "timestamp": 1770892106.6981878}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5460", "timestamp": 1770892106.7198982}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5460", "timestamp": 1770892106.7411647}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6260", "timestamp": 1770892106.7659957}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6260", "timestamp": 1770892106.78783}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 2.80s) ==========", "timestamp": 1770892106.8114626}, {"type": "layer_end", "layer": 1, "duration": 2.800654172897339, "timestamp": 1770892106.8337657}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892106.8569977}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892106.8787355}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892106.8995118}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892106.9181828}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770892106.9407141}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770892106.9623132}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892106.9848893}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892107.00181}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770892107.025965}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770892107.0775735}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770892107.114119}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770892107.1373262}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770892107.164476}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 20520.08 mol/s", "timestamp": 1770892107.1933317}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0012183189392089844, "speed": 20520.078277886496, "timestamp": 1770892107.2134075}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770892107.2353919}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770892107.2539284}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770892107.2737272}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770892107.293381}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770892107.3117156}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770892107.3285456}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892109.2267635}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892109.2492857}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0159", "timestamp": 1770892109.2682414}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.01594704662738313, "timestamp": 1770892109.2862444}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892109.3060079}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892109.323984}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892109.3439522}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770892109.3647428}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892109.3853824}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892109.4148912}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0159", "timestamp": 1770892109.443403}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0159", "timestamp": 1770892109.469304}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.83", "timestamp": 1770892109.492457}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.83", "timestamp": 1770892109.513173}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770892109.5358293}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770892109.5555253}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770892109.5793056}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770892109.6045156}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.61s) ==========", "timestamp": 1770892109.6370726}, {"type": "layer_end", "layer": 2, "duration": 2.610954761505127, "timestamp": 1770892109.6658702}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892109.6904078}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892109.7140687}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892109.7384717}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892109.7652369}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770892109.7950604}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770892109.826415}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892109.8583624}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892109.8828275}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770892109.910688}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770892109.9411228}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770892109.9749272}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770892110.004457}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770892110.031962}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 12251.51 mol/s", "timestamp": 1770892110.0646992}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0016324520111083984, "speed": 12251.508689937198, "timestamp": 1770892110.0878592}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770892110.1138718}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770892110.1388698}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770892110.1629055}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770892110.196715}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770892110.2180278}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770892110.241553}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892111.469699}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892111.4937122}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1873", "timestamp": 1770892111.514814}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18727016922000803, "timestamp": 1770892111.5361102}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892111.558212}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892111.577527}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892111.597581}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770892111.618148}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892111.6353817}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892111.6532876}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1873", "timestamp": 1770892111.6773658}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1873", "timestamp": 1770892111.6961234}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.42", "timestamp": 1770892111.7197957}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.42", "timestamp": 1770892111.7452693}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0770", "timestamp": 1770892111.764069}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0770", "timestamp": 1770892111.782299}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.7830", "timestamp": 1770892111.799418}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.7830", "timestamp": 1770892111.815911}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 1.93s) ==========", "timestamp": 1770892111.8422859}, {"type": "layer_end", "layer": 3, "duration": 1.9308807849884033, "timestamp": 1770892111.8620236}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892111.8828967}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892111.9035192}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892111.9295192}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892111.9610257}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770892111.9890583}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770892112.0139067}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892112.032935}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892112.0525486}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770892112.0744026}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770892112.1012204}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770892112.1221778}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770892112.1421258}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770892112.161726}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 24423.35 mol/s", "timestamp": 1770892112.1835654}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.000614166259765625, "speed": 24423.35403726708, "timestamp": 1770892112.2072833}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770892112.2313545}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770892112.2521777}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770892112.2736192}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770892112.2962189}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770892112.3152497}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770892112.332416}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892113.3678648}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892113.3880062}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0907", "timestamp": 1770892113.4082928}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.09072794622234821, "timestamp": 1770892113.429606}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892113.4496121}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892113.4684002}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892113.4877172}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770892113.5073757}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892113.5283468}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892113.5521445}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0907", "timestamp": 1770892113.5725791}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0907", "timestamp": 1770892113.5897982}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.34", "timestamp": 1770892113.609267}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.34", "timestamp": 1770892113.6286263}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770892113.6514919}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770892113.6747484}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770892113.697851}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770892113.7195883}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 1.68s) ==========", "timestamp": 1770892113.7507203}, {"type": "layer_end", "layer": 4, "duration": 1.6757309436798096, "timestamp": 1770892113.7731776}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892113.7952955}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892113.8173695}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892113.848062}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892113.8722456}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770892113.893077}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770892113.9137661}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892113.9360745}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892113.9596388}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770892113.9810061}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770892114.0042634}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770892114.023673}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770892114.0450075}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770892114.0660324}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 19481.21 mol/s", "timestamp": 1770892114.0866427}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0005133152008056641, "speed": 19481.207617278218, "timestamp": 1770892114.1077368}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770892114.1305733}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770892114.1498897}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770892114.1723251}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770892114.1924276}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770892114.213008}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770892114.2327483}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892114.969478}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892114.9888272}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1242", "timestamp": 1770892115.0050478}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.12420031848498267, "timestamp": 1770892115.0225165}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892115.0409806}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892115.0612366}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892115.085687}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770892115.1059787}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892115.1249475}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892115.1435559}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1242", "timestamp": 1770892115.164568}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1242", "timestamp": 1770892115.182511}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.53", "timestamp": 1770892115.1990895}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.53", "timestamp": 1770892115.2197137}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9710", "timestamp": 1770892115.240872}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9710", "timestamp": 1770892115.2575512}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.2300", "timestamp": 1770892115.2738137}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.2300", "timestamp": 1770892115.2993631}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.34s) ==========", "timestamp": 1770892115.3259954}, {"type": "layer_end", "layer": 5, "duration": 1.3446087837219238, "timestamp": 1770892115.3462448}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892115.3624136}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892115.3770897}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892115.396424}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892115.421491}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770892115.4400678}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770892115.465659}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892115.4861994}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892115.5046017}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770892115.5223804}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770892115.5414104}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770892115.5618331}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770892115.5837874}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770892115.6061049}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 12610.66 mol/s", "timestamp": 1770892115.6277773}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.00039649009704589844, "speed": 12610.65544197234, "timestamp": 1770892115.6783473}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770892115.715724}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770892115.7417605}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770892115.765204}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770892115.7858095}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770892115.8052437}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770892115.8246922}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892116.2989004}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770892116.3254502}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H], Score: 0.1752", "timestamp": 1770892116.3522365}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.17515722407446016, "timestamp": 1770892116.3760679}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892116.4004533}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770892116.4273684}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770892116.4528852}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]"], "timestamp": 1770892116.4843667}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892116.5137224}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892116.5458992}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1752", "timestamp": 1770892116.5683043}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1752", "timestamp": 1770892116.5931056}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.13", "timestamp": 1770892116.6148107}, {"type": "log", "level": "info", "message": "INFO -     体积: 122.13", "timestamp": 1770892116.637681}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9040", "timestamp": 1770892116.6570299}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9040", "timestamp": 1770892116.6751573}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3840", "timestamp": 1770892116.6968427}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3840", "timestamp": 1770892116.7165887}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.22s) ==========", "timestamp": 1770892116.740067}, {"type": "layer_end", "layer": 6, "duration": 1.217175006866455, "timestamp": 1770892116.7617152}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892117.225967}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770892117.2547302}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892117.2795281}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892117.3048635}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770892117.3290484}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770892117.357373}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 23.67 秒", "timestamp": 1770892117.3772063}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 23.67 秒", "timestamp": 1770892117.3994524}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770892117.4216495}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770892117.4530404}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892117.4735081}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770892117.5006917}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770892117.5295005}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770892117.5523903}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892117.5766008}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770892117.60296}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1584, 体积: 111.26, 扩散: 4.8910, 吸附: -50.0200", "timestamp": 1770892117.6242058}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.1584, 体积: 111.26, 扩散: 4.8910, 吸附: -50.0200", "timestamp": 1770892117.647555}, {"type": "log", "level": "info", "message": "INFO -   2. [H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770892117.6696215}, {"type": "log", "level": "info", "message": "INFO -   2. [H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770892117.69374}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3278, 体积: 119.21, 扩散: 5.0110, 吸附: -47.9600", "timestamp": 1770892117.7144847}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3278, 体积: 119.21, 扩散: 5.0110, 吸附: -47.9600", "timestamp": 1770892117.737477}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "timestamp": 1770892117.7552323}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "timestamp": 1770892117.7750523}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3526, 体积: 127.83, 扩散: 5.0330, 吸附: -54.0920", "timestamp": 1770892117.793848}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3526, 体积: 127.83, 扩散: 5.0330, 吸附: -54.0920", "timestamp": 1770892117.8113155}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770892117.827487}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770892117.8456802}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3756, 体积: 126.36, 扩散: 5.0480, 吸附: -48.1340", "timestamp": 1770892117.8673146}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3756, 体积: 126.36, 扩散: 5.0480, 吸附: -48.1340", "timestamp": 1770892117.8866916}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892117.9085348}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770892117.927983}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3855, 体积: 122.91, 扩散: 4.6810, 吸附: -48.6310", "timestamp": 1770892117.9458375}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3855, 体积: 122.91, 扩散: 4.6810, 吸附: -48.6310", "timestamp": 1770892117.9622471}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.01s", "timestamp": 1770892117.9779818}, {"type": "finish", "total_results": 5, "duration": 26.012887954711914, "timestamp": 1770892117.9955394}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/e91599ef-ce17-4c46-8f73-cc9132cb08bc.json", "timestamp": 1770892118.0270448}, {"type": "log", "level": "info", "message": "远程日志已上传: e91599ef-ce17-4c46-8f73-cc9132cb08bc", "timestamp": 1770892118.0716505}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770892118.0950656}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770892118.1390426}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770892118.1665242}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770892118.1937253}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770892118.2198765}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2250, 吸附: -51.4490", "timestamp": 1770892118.2432106}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770892118.2614121}, {"type": "log", "level": "info", "message": "远程日志已启动: e91599ef-ce17-4c46-8f73-cc9132cb08bc", "timestamp": 1770892118.2839599}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770892118.2877307}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.39s", "timestamp": 1770892118.357552}, {"type": "finish", "total_results": 5, "duration": 26.392412900924683, "timestamp": 1770892118.3773084}]}