{"metadata": {"log_name": "ae24e683-4af3-40db-b259-7bd0a2e36a7f", "start_time": 1770945327.682322, "end_time": 1770945354.4605784, "duration": 26.7782564163208, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.03200000000002, "diffusion": 5.553, "adsorption": -51.881}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770945328.744129, "end_time": 1770945339.2851896, "duration": 10.54106068611145, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0014541224371091733}, {"layer_num": 1, "start_time": 1770945339.516784, "end_time": 1770945342.535942, "duration": 3.019158124923706, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.26733216497326223}, {"layer_num": 2, "start_time": 1770945342.7534041, "end_time": 1770945345.0636892, "duration": 2.3102850914001465, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.22657641997486533}, {"layer_num": 3, "start_time": 1770945345.2792394, "end_time": 1770945347.3004093, "duration": 2.021169900894165, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.24407044107073614}, {"layer_num": 4, "start_time": 1770945347.8752055, "end_time": 1770945349.79625, "duration": 1.9210445880889893, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07563373504333264}, {"layer_num": 5, "start_time": 1770945350.055024, "end_time": 1770945351.5016983, "duration": 1.4466743469238281, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.00618002035771418}, {"layer_num": 6, "start_time": 1770945351.728637, "end_time": 1770945352.905616, "duration": 1.1769790649414062, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.028614593516887353}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0014541224371091733, "volume": 110.19200000000002, "diffusion": 5.553, "adsorption": -51.881, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.20692496855471762, "volume": 129.56000000000003, "diffusion": 5.713, "adsorption": -51.87, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.25693152743517933, "volume": 128.32800000000003, "diffusion": 5.258, "adsorption": -52.53, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2630762458340966, "volume": 123.14400000000003, "diffusion": 4.899, "adsorption": -51.429, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.26515064675130146, "volume": 137.92000000000004, "diffusion": 5.573, "adsorption": -51.741, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28234532884324703, "volume": 126.69600000000003, "diffusion": 5.603, "adsorption": -49.773, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.2892266259904291, "volume": 133.21600000000004, "diffusion": 5.352, "adsorption": -51.149, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.3086424593323267, "volume": 128.83200000000002, "diffusion": 5.1, "adsorption": -52.853, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.31075447071466866, "volume": 127.09600000000003, "diffusion": 5.228, "adsorption": -50.201, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.32446116840333805, "volume": 126.32800000000003, "diffusion": 4.889, "adsorption": -52.863, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.3287451343608875, "volume": 128.60800000000003, "diffusion": 5.425, "adsorption": -49.514, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3315272742162503, "volume": 118.87200000000003, "diffusion": 5.78, "adsorption": -48.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.35032910837624953, "volume": 122.44800000000004, "diffusion": 5.255, "adsorption": -55.06, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.35509225979497094, "volume": 137.48000000000005, "diffusion": 5.038, "adsorption": -51.658, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3568834157622792, "volume": 126.88000000000002, "diffusion": 5.012, "adsorption": -53.72, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.36178206897810267, "volume": 121.48800000000003, "diffusion": 4.748, "adsorption": -53.83, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.3648982210291605, "volume": 138.95200000000003, "diffusion": 5.176, "adsorption": -52.472, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3675729919969931, "volume": 119.52000000000002, "diffusion": 4.954, "adsorption": -48.881, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.3690449479908049, "volume": 142.19200000000004, "diffusion": 5.558, "adsorption": -50.569, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3966517707815534, "volume": 131.12800000000004, "diffusion": 5.125, "adsorption": -49.67, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.4055477380945194, "volume": 134.96800000000005, "diffusion": 5.271, "adsorption": -54.097, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4072132887742897, "volume": 144.82400000000004, "diffusion": 5.649, "adsorption": -50.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.4072425957276353, "volume": 134.69600000000003, "diffusion": 4.798, "adsorption": -51.066, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.4118575547742842, "volume": 129.67200000000003, "diffusion": 4.768, "adsorption": -50.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.41920201974550736, "volume": 127.11200000000002, "diffusion": 5.615, "adsorption": -47.509, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4197795500509616, "volume": 128.68000000000004, "diffusion": 6.066, "adsorption": -49.15, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.42433960429769463, "volume": 118.08800000000002, "diffusion": 5.87, "adsorption": -46.796, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4298352077934464, "volume": 146.43200000000004, "diffusion": 5.199, "adsorption": -52.491, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4347935513769519, "volume": 131.51200000000003, "diffusion": 4.974, "adsorption": -54.221, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.43633614733615445, "volume": 144.13600000000002, "diffusion": 5.144, "adsorption": -52.793, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.44653131247096634, "volume": 130.12000000000003, "diffusion": 5.745, "adsorption": -47.914, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4467895688314757, "volume": 147.64000000000004, "diffusion": 5.84, "adsorption": -52.803, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4545833805175692, "volume": 118.97600000000003, "diffusion": 6.189, "adsorption": -47.406, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4579558774469904, "volume": 133.33600000000004, "diffusion": 5.956, "adsorption": -48.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.45932173048690284, "volume": 131.92000000000004, "diffusion": 4.991, "adsorption": -49.128, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.46398969195122053, "volume": 146.48000000000005, "diffusion": 5.513, "adsorption": -54.052, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.47748894082942184, "volume": 126.76800000000003, "diffusion": 4.948, "adsorption": -48.138, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.48323034991671443, "volume": 122.58400000000003, "diffusion": 4.774, "adsorption": -55.839, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.48524863315981887, "volume": 139.35200000000003, "diffusion": 4.656, "adsorption": -50.891, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.4920020346152549, "volume": 135.96000000000004, "diffusion": 5.793, "adsorption": -48.195, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.5003515787100414, "volume": 130.17600000000004, "diffusion": 6.532, "adsorption": -49.443, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5030201654728453, "volume": 139.18400000000003, "diffusion": 5.949, "adsorption": -48.997, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.5088883744458463, "volume": 150.88800000000003, "diffusion": 6.01, "adsorption": -52.837, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5143137062773454, "volume": 150.77600000000004, "diffusion": 5.346, "adsorption": -50.035, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5198791819391878, "volume": 129.91200000000003, "diffusion": 5.071, "adsorption": -56.246, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.5208033685232445, "volume": 142.06400000000002, "diffusion": 6.403, "adsorption": -53.206, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.5215548040826609, "volume": 127.79200000000003, "diffusion": 4.282, "adsorption": -49.611, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.5252256168468931, "volume": 141.76800000000003, "diffusion": 6.263, "adsorption": -49.997, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5274738497075644, "volume": 120.09600000000003, "diffusion": 6.106, "adsorption": -46.063, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.529576177074532, "volume": 127.03200000000002, "diffusion": 4.823, "adsorption": -56.094, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5313537321528651, "volume": 128.16000000000003, "diffusion": 5.727, "adsorption": -46.083, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5332538860328309, "volume": 132.41600000000003, "diffusion": 4.697, "adsorption": -48.843, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5351066853191426, "volume": 133.95200000000003, "diffusion": 4.569, "adsorption": -49.451, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5548596873352727, "volume": 127.50400000000003, "diffusion": 4.315, "adsorption": -48.887, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5806889802098265, "volume": 127.64800000000002, "diffusion": 5.242, "adsorption": -45.576, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5813769725421807, "volume": 129.17600000000004, "diffusion": 5.218, "adsorption": -45.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.5830015498066057, "volume": 145.64000000000004, "diffusion": 5.927, "adsorption": -55.202, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.5910854468059589, "volume": 134.96800000000005, "diffusion": 5.458, "adsorption": -57.888, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5967279274508386, "volume": 124.45600000000003, "diffusion": 5.847, "adsorption": -44.744, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5976931205233714, "volume": 123.40000000000003, "diffusion": 4.457, "adsorption": -47.059, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6079436617704638, "volume": 138.11200000000002, "diffusion": 5.507, "adsorption": -45.924, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6145631887322682, "volume": 128.03200000000004, "diffusion": 5.728, "adsorption": -59.135, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.6195855608000373, "volume": 136.56800000000004, "diffusion": 5.402, "adsorption": -45.807, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.6227059974436202, "volume": 146.75200000000004, "diffusion": 4.314, "adsorption": -50.742, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.6250059029121224, "volume": 130.84000000000003, "diffusion": 6.299, "adsorption": -46.666, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6353117500088532, "volume": 138.38400000000004, "diffusion": 5.266, "adsorption": -46.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.6440809726326124, "volume": 138.24000000000004, "diffusion": 5.65, "adsorption": -45.478, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.6551460630003759, "volume": 146.27200000000005, "diffusion": 5.606, "adsorption": -57.35, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.664920737201053, "volume": 154.20000000000005, "diffusion": 5.395, "adsorption": -47.816, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6821130612346287, "volume": 135.70400000000004, "diffusion": 3.966, "adsorption": -49.062, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.696544543544467, "volume": 145.74400000000003, "diffusion": 5.999, "adsorption": -46.837, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7074423892803178, "volume": 138.31200000000004, "diffusion": 6.59, "adsorption": -47.321, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7210096308955213, "volume": 149.20800000000003, "diffusion": 5.856, "adsorption": -46.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.7340315936363446, "volume": 128.65600000000003, "diffusion": 5.964, "adsorption": -60.368, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.7454832627131502, "volume": 120.54400000000003, "diffusion": 5.177, "adsorption": -61.95, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.753066857525642, "volume": 155.08800000000005, "diffusion": 4.933, "adsorption": -55.892, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7617277179032692, "volume": 155.32000000000005, "diffusion": 4.869, "adsorption": -47.956, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7643843770835418, "volume": 146.84000000000003, "diffusion": 5.841, "adsorption": -45.344, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.7653871498315106, "volume": 154.38400000000004, "diffusion": 5.593, "adsorption": -45.74, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7808451196762276, "volume": 134.16800000000003, "diffusion": 6.325, "adsorption": -44.575, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7842680914935123, "volume": 134.10400000000004, "diffusion": 5.155, "adsorption": -43.341, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.788276771009965, "volume": 156.04000000000005, "diffusion": 5.808, "adsorption": -46.274, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7948481606815085, "volume": 130.67200000000003, "diffusion": 6.278, "adsorption": -43.637, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7990672713605971, "volume": 130.26400000000004, "diffusion": 6.76, "adsorption": -45.001, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8101542943545137, "volume": 138.45600000000005, "diffusion": 7.243, "adsorption": -47.601, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.8108577512763235, "volume": 139.28800000000004, "diffusion": 7.299, "adsorption": -47.894, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8155519718080559, "volume": 133.52800000000002, "diffusion": 5.912, "adsorption": -42.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.819128084734724, "volume": 145.96800000000005, "diffusion": 5.302, "adsorption": -59.617, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8272901911740558, "volume": 151.72800000000004, "diffusion": 5.347, "adsorption": -44.769, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.857757977612899, "volume": 138.29600000000002, "diffusion": 5.417, "adsorption": -41.913, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8584479199789429, "volume": 156.08800000000005, "diffusion": 5.939, "adsorption": -45.476, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8869995537634598, "volume": 143.85600000000002, "diffusion": 4.911, "adsorption": -43.857, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.8876430700059934, "volume": 146.47200000000004, "diffusion": 6.501, "adsorption": -45.21, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8896576144155959, "volume": 137.42400000000004, "diffusion": 5.542, "adsorption": -40.835, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9374676731712497, "volume": 139.90400000000002, "diffusion": 5.923, "adsorption": -41.516, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 0.9618151859181502, "volume": 122.68800000000003, "diffusion": 6.062, "adsorption": -64.94, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9624832720635825, "volume": 134.94400000000005, "diffusion": 5.977, "adsorption": -40.472, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9768511045909017, "volume": 132.36800000000002, "diffusion": 5.406, "adsorption": -38.956, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.037882202835228, "volume": 145.88000000000002, "diffusion": 6.678, "adsorption": -43.07, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0447952188525702, "volume": 147.56000000000003, "diffusion": 5.526, "adsorption": -39.795, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.070285350299794, "volume": 140.44800000000004, "diffusion": 4.532, "adsorption": -41.332, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0834979952998867, "volume": 155.94400000000005, "diffusion": 4.882, "adsorption": -42.449, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1253647349000975, "volume": 140.78400000000002, "diffusion": 6.764, "adsorption": -41.024, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.1415675235819167, "volume": 156.16800000000003, "diffusion": 6.303, "adsorption": -41.726, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.1589895172290616, "volume": 152.10400000000004, "diffusion": 5.074, "adsorption": -39.942, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1805313413878047, "volume": 155.20000000000005, "diffusion": 5.699, "adsorption": -39.019, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1889316772598597, "volume": 136.46400000000003, "diffusion": 5.547, "adsorption": -35.493, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.373316981914549, "volume": 157.32000000000005, "diffusion": 5.409, "adsorption": -36.012, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3859903254455401, "volume": 146.35200000000003, "diffusion": 6.117, "adsorption": -35.377, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.26733216497326223, "volume": 110.30400000000003, "diffusion": 5.077, "adsorption": -48.783, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.27699146530926977, "volume": 129.30400000000003, "diffusion": 5.59, "adsorption": -50.235, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.32583782959074403, "volume": 137.48800000000003, "diffusion": 5.734, "adsorption": -52.637, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.36400999273889334, "volume": 123.09600000000003, "diffusion": 4.671, "adsorption": -50.386, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3944612124485531, "volume": 129.06400000000002, "diffusion": 5.033, "adsorption": -54.092, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.22657641997486533, "volume": 109.34400000000002, "diffusion": 4.895, "adsorption": -50.12, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24294758728933033, "volume": 122.67200000000003, "diffusion": 4.907, "adsorption": -51.678, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.2835947330800378, "volume": 137.71200000000005, "diffusion": 5.57, "adsorption": -52.382, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3292436292775991, "volume": 129.42400000000004, "diffusion": 5.553, "adsorption": -49.235, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3993851463752297, "volume": 128.40800000000004, "diffusion": 5.197, "adsorption": -54.791, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24407044107073614, "volume": 129.92800000000003, "diffusion": 5.744, "adsorption": -51.382, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.26644725084913023, "volume": 110.71200000000003, "diffusion": 5.072, "adsorption": -48.878, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3804495925120074, "volume": 137.75200000000004, "diffusion": 6.031, "adsorption": -51.147, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3896055919873124, "volume": 122.53600000000003, "diffusion": 4.744, "adsorption": -49.628, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.4070115858690466, "volume": 128.48000000000002, "diffusion": 4.982, "adsorption": -54.242, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07563373504333264, "volume": 110.44000000000003, "diffusion": 5.213, "adsorption": -51.696, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.26144955275417264, "volume": 137.71200000000005, "diffusion": 5.579, "adsorption": -51.791, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2967261916699926, "volume": 130.36800000000002, "diffusion": 5.643, "adsorption": -50.226, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.30755096736396487, "volume": 128.16800000000003, "diffusion": 5.262, "adsorption": -53.443, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.40205761070904256, "volume": 122.41600000000003, "diffusion": 4.541, "adsorption": -50.026, "layer": 4, "parent_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.00618002035771418, "volume": 110.71200000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2295886948461995, "volume": 129.40800000000004, "diffusion": 5.729, "adsorption": -52.258, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24530469974730762, "volume": 122.38400000000003, "diffusion": 5.003, "adsorption": -52.469, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.314240057790043, "volume": 137.59200000000004, "diffusion": 5.831, "adsorption": -52.118, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.40262572920821765, "volume": 128.89600000000004, "diffusion": 5.194, "adsorption": -54.761, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.028614593516887353, "volume": 110.87200000000003, "diffusion": 5.502, "adsorption": -51.677, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.27836593796828935, "volume": 137.83200000000002, "diffusion": 5.486, "adsorption": -52.117, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.28617537856847264, "volume": 130.18400000000003, "diffusion": 5.612, "adsorption": -50.283, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3233011536498932, "volume": 128.39200000000002, "diffusion": 5.103, "adsorption": -53.185, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.4438654617538219, "volume": 122.86400000000003, "diffusion": 4.667, "adsorption": -48.981, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.03200000000002, "diffusion": 5.553, "adsorption": -51.881}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.028614593516887353, "volume": 110.87200000000003, "diffusion": 5.502, "adsorption": -51.677, "is_selected": false, "rank": 1}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.28617537856847264, "volume": 130.18400000000003, "diffusion": 5.612, "adsorption": -50.283, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 6, "score": 0.3233011536498932, "volume": 128.39200000000002, "diffusion": 5.103, "adsorption": -53.185, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.4438654617538219, "volume": 122.86400000000003, "diffusion": 4.667, "adsorption": -48.981, "is_selected": false, "rank": 5}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 6, "score": 0.27836593796828935, "volume": 137.83200000000002, "diffusion": 5.486, "adsorption": -52.117, "is_selected": false, "rank": 2}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.28234532884324703, "volume": 126.69600000000003, "diffusion": 5.603, "adsorption": -49.773, "is_selected": false, "rank": 6}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.2892266259904291, "volume": 133.21600000000004, "diffusion": 5.352, "adsorption": -51.149, "is_selected": false, "rank": 7}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.3086424593323267, "volume": 128.83200000000002, "diffusion": 5.1, "adsorption": -52.853, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.31075447071466866, "volume": 127.09600000000003, "diffusion": 5.228, "adsorption": -50.201, "is_selected": false, "rank": 9}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.32446116840333805, "volume": 126.32800000000003, "diffusion": 4.889, "adsorption": -52.863, "is_selected": false, "rank": 10}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3287451343608875, "volume": 128.60800000000003, "diffusion": 5.425, "adsorption": -49.514, "is_selected": false, "rank": 11}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3315272742162503, "volume": 118.87200000000003, "diffusion": 5.78, "adsorption": -48.244, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.35032910837624953, "volume": 122.44800000000004, "diffusion": 5.255, "adsorption": -55.06, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.35509225979497094, "volume": 137.48000000000005, "diffusion": 5.038, "adsorption": -51.658, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.3568834157622792, "volume": 126.88000000000002, "diffusion": 5.012, "adsorption": -53.72, "is_selected": false, "rank": 15}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.36178206897810267, "volume": 121.48800000000003, "diffusion": 4.748, "adsorption": -53.83, "is_selected": false, "rank": 16}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.3648982210291605, "volume": 138.95200000000003, "diffusion": 5.176, "adsorption": -52.472, "is_selected": false, "rank": 17}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3675729919969931, "volume": 119.52000000000002, "diffusion": 4.954, "adsorption": -48.881, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3690449479908049, "volume": 142.19200000000004, "diffusion": 5.558, "adsorption": -50.569, "is_selected": false, "rank": 19}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3966517707815534, "volume": 131.12800000000004, "diffusion": 5.125, "adsorption": -49.67, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.4055477380945194, "volume": 134.96800000000005, "diffusion": 5.271, "adsorption": -54.097, "is_selected": false, "rank": 21}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4072132887742897, "volume": 144.82400000000004, "diffusion": 5.649, "adsorption": -50.606, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.4072425957276353, "volume": 134.69600000000003, "diffusion": 4.798, "adsorption": -51.066, "is_selected": false, "rank": 23}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.4118575547742842, "volume": 129.67200000000003, "diffusion": 4.768, "adsorption": -50.29, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.41920201974550736, "volume": 127.11200000000002, "diffusion": 5.615, "adsorption": -47.509, "is_selected": false, "rank": 25}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4197795500509616, "volume": 128.68000000000004, "diffusion": 6.066, "adsorption": -49.15, "is_selected": false, "rank": 26}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.42433960429769463, "volume": 118.08800000000002, "diffusion": 5.87, "adsorption": -46.796, "is_selected": false, "rank": 27}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4298352077934464, "volume": 146.43200000000004, "diffusion": 5.199, "adsorption": -52.491, "is_selected": false, "rank": 28}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4347935513769519, "volume": 131.51200000000003, "diffusion": 4.974, "adsorption": -54.221, "is_selected": false, "rank": 29}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.43633614733615445, "volume": 144.13600000000002, "diffusion": 5.144, "adsorption": -52.793, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.44653131247096634, "volume": 130.12000000000003, "diffusion": 5.745, "adsorption": -47.914, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4467895688314757, "volume": 147.64000000000004, "diffusion": 5.84, "adsorption": -52.803, "is_selected": false, "rank": 32}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4545833805175692, "volume": 118.97600000000003, "diffusion": 6.189, "adsorption": -47.406, "is_selected": false, "rank": 33}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4579558774469904, "volume": 133.33600000000004, "diffusion": 5.956, "adsorption": -48.879, "is_selected": false, "rank": 34}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.45932173048690284, "volume": 131.92000000000004, "diffusion": 4.991, "adsorption": -49.128, "is_selected": false, "rank": 35}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.46398969195122053, "volume": 146.48000000000005, "diffusion": 5.513, "adsorption": -54.052, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.47748894082942184, "volume": 126.76800000000003, "diffusion": 4.948, "adsorption": -48.138, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.48323034991671443, "volume": 122.58400000000003, "diffusion": 4.774, "adsorption": -55.839, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.48524863315981887, "volume": 139.35200000000003, "diffusion": 4.656, "adsorption": -50.891, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.4920020346152549, "volume": 135.96000000000004, "diffusion": 5.793, "adsorption": -48.195, "is_selected": false, "rank": 40}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.5003515787100414, "volume": 130.17600000000004, "diffusion": 6.532, "adsorption": -49.443, "is_selected": false, "rank": 41}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5030201654728453, "volume": 139.18400000000003, "diffusion": 5.949, "adsorption": -48.997, "is_selected": false, "rank": 42}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5088883744458463, "volume": 150.88800000000003, "diffusion": 6.01, "adsorption": -52.837, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5143137062773454, "volume": 150.77600000000004, "diffusion": 5.346, "adsorption": -50.035, "is_selected": false, "rank": 44}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5198791819391878, "volume": 129.91200000000003, "diffusion": 5.071, "adsorption": -56.246, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.5208033685232445, "volume": 142.06400000000002, "diffusion": 6.403, "adsorption": -53.206, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.5215548040826609, "volume": 127.79200000000003, "diffusion": 4.282, "adsorption": -49.611, "is_selected": false, "rank": 47}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.5252256168468931, "volume": 141.76800000000003, "diffusion": 6.263, "adsorption": -49.997, "is_selected": false, "rank": 48}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5274738497075644, "volume": 120.09600000000003, "diffusion": 6.106, "adsorption": -46.063, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.529576177074532, "volume": 127.03200000000002, "diffusion": 4.823, "adsorption": -56.094, "is_selected": false, "rank": 50}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5313537321528651, "volume": 128.16000000000003, "diffusion": 5.727, "adsorption": -46.083, "is_selected": false, "rank": 51}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5332538860328309, "volume": 132.41600000000003, "diffusion": 4.697, "adsorption": -48.843, "is_selected": false, "rank": 52}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5351066853191426, "volume": 133.95200000000003, "diffusion": 4.569, "adsorption": -49.451, "is_selected": false, "rank": 53}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5548596873352727, "volume": 127.50400000000003, "diffusion": 4.315, "adsorption": -48.887, "is_selected": false, "rank": 54}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5806889802098265, "volume": 127.64800000000002, "diffusion": 5.242, "adsorption": -45.576, "is_selected": false, "rank": 55}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5813769725421807, "volume": 129.17600000000004, "diffusion": 5.218, "adsorption": -45.879, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5830015498066057, "volume": 145.64000000000004, "diffusion": 5.927, "adsorption": -55.202, "is_selected": false, "rank": 57}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5910854468059589, "volume": 134.96800000000005, "diffusion": 5.458, "adsorption": -57.888, "is_selected": false, "rank": 58}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5967279274508386, "volume": 124.45600000000003, "diffusion": 5.847, "adsorption": -44.744, "is_selected": false, "rank": 59}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5976931205233714, "volume": 123.40000000000003, "diffusion": 4.457, "adsorption": -47.059, "is_selected": false, "rank": 60}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6079436617704638, "volume": 138.11200000000002, "diffusion": 5.507, "adsorption": -45.924, "is_selected": false, "rank": 61}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6145631887322682, "volume": 128.03200000000004, "diffusion": 5.728, "adsorption": -59.135, "is_selected": false, "rank": 62}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6195855608000373, "volume": 136.56800000000004, "diffusion": 5.402, "adsorption": -45.807, "is_selected": false, "rank": 63}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.6227059974436202, "volume": 146.75200000000004, "diffusion": 4.314, "adsorption": -50.742, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.6250059029121224, "volume": 130.84000000000003, "diffusion": 6.299, "adsorption": -46.666, "is_selected": false, "rank": 65}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6353117500088532, "volume": 138.38400000000004, "diffusion": 5.266, "adsorption": -46.244, "is_selected": false, "rank": 66}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.6440809726326124, "volume": 138.24000000000004, "diffusion": 5.65, "adsorption": -45.478, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.6551460630003759, "volume": 146.27200000000005, "diffusion": 5.606, "adsorption": -57.35, "is_selected": false, "rank": 68}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.664920737201053, "volume": 154.20000000000005, "diffusion": 5.395, "adsorption": -47.816, "is_selected": false, "rank": 69}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6821130612346287, "volume": 135.70400000000004, "diffusion": 3.966, "adsorption": -49.062, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.696544543544467, "volume": 145.74400000000003, "diffusion": 5.999, "adsorption": -46.837, "is_selected": false, "rank": 71}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7074423892803178, "volume": 138.31200000000004, "diffusion": 6.59, "adsorption": -47.321, "is_selected": false, "rank": 72}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7210096308955213, "volume": 149.20800000000003, "diffusion": 5.856, "adsorption": -46.513, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.7340315936363446, "volume": 128.65600000000003, "diffusion": 5.964, "adsorption": -60.368, "is_selected": false, "rank": 74}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.7454832627131502, "volume": 120.54400000000003, "diffusion": 5.177, "adsorption": -61.95, "is_selected": false, "rank": 75}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.753066857525642, "volume": 155.08800000000005, "diffusion": 4.933, "adsorption": -55.892, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7617277179032692, "volume": 155.32000000000005, "diffusion": 4.869, "adsorption": -47.956, "is_selected": false, "rank": 77}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7643843770835418, "volume": 146.84000000000003, "diffusion": 5.841, "adsorption": -45.344, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.7653871498315106, "volume": 154.38400000000004, "diffusion": 5.593, "adsorption": -45.74, "is_selected": false, "rank": 79}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7808451196762276, "volume": 134.16800000000003, "diffusion": 6.325, "adsorption": -44.575, "is_selected": false, "rank": 80}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7842680914935123, "volume": 134.10400000000004, "diffusion": 5.155, "adsorption": -43.341, "is_selected": false, "rank": 81}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.788276771009965, "volume": 156.04000000000005, "diffusion": 5.808, "adsorption": -46.274, "is_selected": false, "rank": 82}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7948481606815085, "volume": 130.67200000000003, "diffusion": 6.278, "adsorption": -43.637, "is_selected": false, "rank": 83}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7990672713605971, "volume": 130.26400000000004, "diffusion": 6.76, "adsorption": -45.001, "is_selected": false, "rank": 84}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8101542943545137, "volume": 138.45600000000005, "diffusion": 7.243, "adsorption": -47.601, "is_selected": false, "rank": 85}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8108577512763235, "volume": 139.28800000000004, "diffusion": 7.299, "adsorption": -47.894, "is_selected": false, "rank": 86}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8155519718080559, "volume": 133.52800000000002, "diffusion": 5.912, "adsorption": -42.588, "is_selected": false, "rank": 87}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.819128084734724, "volume": 145.96800000000005, "diffusion": 5.302, "adsorption": -59.617, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8272901911740558, "volume": 151.72800000000004, "diffusion": 5.347, "adsorption": -44.769, "is_selected": false, "rank": 89}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.857757977612899, "volume": 138.29600000000002, "diffusion": 5.417, "adsorption": -41.913, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8584479199789429, "volume": 156.08800000000005, "diffusion": 5.939, "adsorption": -45.476, "is_selected": false, "rank": 91}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8869995537634598, "volume": 143.85600000000002, "diffusion": 4.911, "adsorption": -43.857, "is_selected": false, "rank": 92}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8876430700059934, "volume": 146.47200000000004, "diffusion": 6.501, "adsorption": -45.21, "is_selected": false, "rank": 93}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8896576144155959, "volume": 137.42400000000004, "diffusion": 5.542, "adsorption": -40.835, "is_selected": false, "rank": 94}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9374676731712497, "volume": 139.90400000000002, "diffusion": 5.923, "adsorption": -41.516, "is_selected": false, "rank": 95}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9618151859181502, "volume": 122.68800000000003, "diffusion": 6.062, "adsorption": -64.94, "is_selected": false, "rank": 96}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9624832720635825, "volume": 134.94400000000005, "diffusion": 5.977, "adsorption": -40.472, "is_selected": false, "rank": 97}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9768511045909017, "volume": 132.36800000000002, "diffusion": 5.406, "adsorption": -38.956, "is_selected": false, "rank": 98}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.037882202835228, "volume": 145.88000000000002, "diffusion": 6.678, "adsorption": -43.07, "is_selected": false, "rank": 99}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0447952188525702, "volume": 147.56000000000003, "diffusion": 5.526, "adsorption": -39.795, "is_selected": false, "rank": 100}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.070285350299794, "volume": 140.44800000000004, "diffusion": 4.532, "adsorption": -41.332, "is_selected": false, "rank": 101}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0834979952998867, "volume": 155.94400000000005, "diffusion": 4.882, "adsorption": -42.449, "is_selected": false, "rank": 102}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1253647349000975, "volume": 140.78400000000002, "diffusion": 6.764, "adsorption": -41.024, "is_selected": false, "rank": 103}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.1415675235819167, "volume": 156.16800000000003, "diffusion": 6.303, "adsorption": -41.726, "is_selected": false, "rank": 104}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.1589895172290616, "volume": 152.10400000000004, "diffusion": 5.074, "adsorption": -39.942, "is_selected": false, "rank": 105}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1805313413878047, "volume": 155.20000000000005, "diffusion": 5.699, "adsorption": -39.019, "is_selected": false, "rank": 106}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1889316772598597, "volume": 136.46400000000003, "diffusion": 5.547, "adsorption": -35.493, "is_selected": false, "rank": 107}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.373316981914549, "volume": 157.32000000000005, "diffusion": 5.409, "adsorption": -36.012, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3859903254455401, "volume": 146.35200000000003, "diffusion": 6.117, "adsorption": -35.377, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 3}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 4}, {"from": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770945327.6829822}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: ae24e683-4af3-40db-b259-7bd0a2e36a7f", "timestamp": 1770945327.6972969}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/ae24e683-4af3-40db-b259-7bd0a2e36a7f.log", "timestamp": 1770945327.7173786}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945328.0126646}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770945328.028437}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770945328.0433168}, {"type": "log", "level": "info", "message": "远程日志已启动: ae24e683-4af3-40db-b259-7bd0a2e36a7f", "timestamp": 1770945328.0596356}, {"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770945328.061531}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.0930166}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.1081524}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770945328.1234398}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770945328.1375675}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770945328.153066}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770945328.1659732}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770945328.180937}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770945328.2029996}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770945328.2308106}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770945328.2667627}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.2968879}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.3206103}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945328.3723874}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770945328.3956723}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770945328.4159975}, {"type": "log", "level": "info", "message": "远程日志已启动: ae24e683-4af3-40db-b259-7bd0a2e36a7f", "timestamp": 1770945328.4448042}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770945328.449235}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770945328.511018}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770945328.5287962}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770945328.5468829}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770945328.5665338}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945328.588011}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945328.6050813}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.6250286}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.6438277}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770945328.6639588}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770945328.683361}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.7043726}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945328.7243195}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770945328.7443736}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.04 秒", "timestamp": 1770945328.8094566}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.04 秒", "timestamp": 1770945328.8349915}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.04s, 速度: 4049.44 mol/s", "timestamp": 1770945328.8571339}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.042474985122680664, "speed": 4049.442265917498, "timestamp": 1770945328.8786328}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770945328.9172971}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770945328.9431782}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770945328.9632885}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770945328.9855247}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770945329.0051165}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770945329.023593}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770945329.0452375}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770945329.062476}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770945329.0842786}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770945329.103556}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770945338.8931723}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770945338.9169521}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0015", "timestamp": 1770945338.9377182}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0014541224371091733, "timestamp": 1770945338.9603083}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945338.9808946}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945339.0034716}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945339.0248888}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]"], "timestamp": 1770945339.0444262}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945339.066109}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945339.087279}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0015", "timestamp": 1770945339.1075325}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0015", "timestamp": 1770945339.1292403}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.19", "timestamp": 1770945339.1546137}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.19", "timestamp": 1770945339.178166}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770945339.197218}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770945339.2174098}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770945339.2399626}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770945339.2618296}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.54s) ==========", "timestamp": 1770945339.2857854}, {"type": "layer_end", "layer": 0, "duration": 10.54106068611145, "timestamp": 1770945339.3072598}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945339.3262203}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945339.3502111}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945339.3823094}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945339.4044917}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770945339.4267614}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770945339.4472926}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945339.4719558}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945339.4941442}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770945339.5171354}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770945339.5450552}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770945339.5674233}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 27600.16 mol/s", "timestamp": 1770945339.5886793}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0010869503021240234, "speed": 27600.157929370474, "timestamp": 1770945339.610673}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770945339.6350036}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770945339.656418}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770945339.6805913}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770945339.708881}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770945339.731349}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770945339.756147}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945341.6548054}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945341.7234018}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2673", "timestamp": 1770945341.8028152}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.26733216497326223, "timestamp": 1770945341.8814673}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945341.9688356}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945342.063659}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945342.2060843}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770945342.309554}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945342.3377306}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945342.3633716}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2673", "timestamp": 1770945342.3865192}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2673", "timestamp": 1770945342.4042745}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.30", "timestamp": 1770945342.4223042}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.30", "timestamp": 1770945342.4402308}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0770", "timestamp": 1770945342.4611497}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0770", "timestamp": 1770945342.479543}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.7830", "timestamp": 1770945342.4992352}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.7830", "timestamp": 1770945342.5158165}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 3.02s) ==========", "timestamp": 1770945342.5367284}, {"type": "layer_end", "layer": 1, "duration": 3.019158124923706, "timestamp": 1770945342.5540068}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945342.571923}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945342.597828}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945342.616819}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945342.6379013}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770945342.660886}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770945342.682372}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945342.7025514}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945342.724452}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770945342.7537365}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770945342.7797554}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770945342.8014987}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 28393.61 mol/s", "timestamp": 1770945342.823461}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0008804798126220703, "speed": 28393.609531546168, "timestamp": 1770945342.8476849}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770945342.8670201}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770945342.892851}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770945342.917456}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770945342.9375713}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770945342.9564095}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770945342.9761014}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945344.636271}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945344.6620677}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2266", "timestamp": 1770945344.685656}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.22657641997486533, "timestamp": 1770945344.7090697}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945344.7282424}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945344.751492}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945344.778307}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770945344.8014069}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945344.8242545}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945344.8465056}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2266", "timestamp": 1770945344.866468}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2266", "timestamp": 1770945344.8846653}, {"type": "log", "level": "info", "message": "INFO -     体积: 109.34", "timestamp": 1770945344.9064977}, {"type": "log", "level": "info", "message": "INFO -     体积: 109.34", "timestamp": 1770945344.929684}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.8950", "timestamp": 1770945344.9608252}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.8950", "timestamp": 1770945344.9937794}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.1200", "timestamp": 1770945345.0174327}, {"type": "log", "level": "info", "message": "INFO -     吸附: -50.1200", "timestamp": 1770945345.0390232}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.31s) ==========", "timestamp": 1770945345.0642393}, {"type": "layer_end", "layer": 2, "duration": 2.3102850914001465, "timestamp": 1770945345.0846806}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945345.104549}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945345.1248214}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945345.1443498}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945345.1737804}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770945345.1969192}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770945345.2168455}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945345.2379186}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945345.2586563}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770945345.2794826}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770945345.3070118}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770945345.330322}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 16697.07 mol/s", "timestamp": 1770945345.353927}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.00119781494140625, "speed": 16697.070063694267, "timestamp": 1770945345.3757722}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770945345.3993447}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770945345.425059}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770945345.4508982}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770945345.473994}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770945345.4981265}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770945345.5238347}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945346.859583}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945346.8850644}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2441", "timestamp": 1770945346.9096909}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.24407044107073614, "timestamp": 1770945346.932933}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945346.9542055}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945346.9748967}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945347.0020225}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770945347.0285888}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945347.0528615}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945347.080793}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2441", "timestamp": 1770945347.1049635}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2441", "timestamp": 1770945347.1299648}, {"type": "log", "level": "info", "message": "INFO -     体积: 129.93", "timestamp": 1770945347.1548069}, {"type": "log", "level": "info", "message": "INFO -     体积: 129.93", "timestamp": 1770945347.1789596}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.7440", "timestamp": 1770945347.2007563}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.7440", "timestamp": 1770945347.2231808}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3820", "timestamp": 1770945347.2456203}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3820", "timestamp": 1770945347.2724292}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.02s) ==========", "timestamp": 1770945347.3017044}, {"type": "layer_end", "layer": 3, "duration": 2.021169900894165, "timestamp": 1770945347.3262608}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945347.640623}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945347.6722078}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945347.7005928}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945347.7308562}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770945347.7650871}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770945347.7899125}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945347.8221767}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945347.8496733}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770945347.8754065}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770945347.8977325}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770945347.9200084}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 29154.11 mol/s", "timestamp": 1770945347.9451342}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0005145072937011719, "speed": 29154.10565338276, "timestamp": 1770945347.9662654}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770945347.9898584}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770945348.016112}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770945348.0388563}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770945348.064436}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770945348.0889273}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770945348.1179245}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945349.2639802}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945349.3012602}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0756", "timestamp": 1770945349.3251903}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07563373504333264, "timestamp": 1770945349.360309}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945349.3867953}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945349.416411}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945349.4480834}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770945349.4762876}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945349.5042078}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945349.534779}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0756", "timestamp": 1770945349.5696673}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0756", "timestamp": 1770945349.6045868}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.44", "timestamp": 1770945349.6385221}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.44", "timestamp": 1770945349.6666317}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770945349.695813}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770945349.7217581}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770945349.746353}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770945349.7718172}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 1.92s) ==========", "timestamp": 1770945349.7970207}, {"type": "layer_end", "layer": 4, "duration": 1.9210445880889893, "timestamp": 1770945349.8246439}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945349.849285}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945349.8783045}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945349.9098294}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945349.9379406}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770945349.9645336}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770945349.987645}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945350.0093002}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945350.0309527}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770945350.055247}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770945350.0803654}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770945350.1056268}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 14023.08 mol/s", "timestamp": 1770945350.1280508}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0007131099700927734, "speed": 14023.082581076564, "timestamp": 1770945350.1487775}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770945350.1743498}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770945350.199977}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770945350.2245727}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770945350.2502298}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770945350.2724829}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770945350.2965899}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945351.0509703}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945351.074586}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0062", "timestamp": 1770945351.099439}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.00618002035771418, "timestamp": 1770945351.122174}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945351.1454277}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945351.1692965}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945351.196897}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]"], "timestamp": 1770945351.2203996}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945351.2416992}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945351.2654765}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0062", "timestamp": 1770945351.29409}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0062", "timestamp": 1770945351.320287}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.71", "timestamp": 1770945351.348892}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.71", "timestamp": 1770945351.3732913}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770945351.4008722}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770945351.4269094}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770945351.451407}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770945351.4750664}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.45s) ==========", "timestamp": 1770945351.5027795}, {"type": "layer_end", "layer": 5, "duration": 1.4466743469238281, "timestamp": 1770945351.5253143}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945351.5487678}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945351.570134}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945351.5971518}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945351.6202724}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770945351.642404}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770945351.6649551}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945351.6877906}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945351.7080355}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770945351.7288966}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770945351.7535238}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770945351.7786684}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 18493.40 mol/s", "timestamp": 1770945351.8059502}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0002703666687011719, "speed": 18493.403880070546, "timestamp": 1770945351.8278732}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770945351.8497193}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770945351.872574}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770945351.8971214}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770945351.91783}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770945351.9388158}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770945351.959427}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945352.4681158}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770945352.4920783}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0286", "timestamp": 1770945352.5142868}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.028614593516887353, "timestamp": 1770945352.5370193}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945352.5618076}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770945352.5865855}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770945352.6088393}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770945352.6306062}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945352.653523}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945352.6762033}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0286", "timestamp": 1770945352.69725}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0286", "timestamp": 1770945352.7173772}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.87", "timestamp": 1770945352.7439315}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.87", "timestamp": 1770945352.7775514}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5020", "timestamp": 1770945352.806272}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5020", "timestamp": 1770945352.8302753}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6770", "timestamp": 1770945352.85223}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6770", "timestamp": 1770945352.8774233}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.18s) ==========", "timestamp": 1770945352.9067593}, {"type": "layer_end", "layer": 6, "duration": 1.1769790649414062, "timestamp": 1770945352.9303606}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945353.3767486}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770945353.4061632}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945353.4301727}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945353.4519556}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770945353.4718425}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770945353.4942513}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 24.37 秒", "timestamp": 1770945353.5162134}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 24.37 秒", "timestamp": 1770945353.545332}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770945353.5702019}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770945353.59586}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945353.619555}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770945353.6420283}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770945353.6621902}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770945353.685993}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945353.7119498}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945353.7345672}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2196, 体积: 129.69, 扩散: 5.7430, 吸附: -51.9980", "timestamp": 1770945353.75654}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2196, 体积: 129.69, 扩散: 5.7430, 吸附: -51.9980", "timestamp": 1770945353.781792}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770945353.80347}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770945353.8267517}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2298, 体积: 122.88, 扩散: 5.0670, 吸附: -52.3230", "timestamp": 1770945353.8452365}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2298, 体积: 122.88, 扩散: 5.0670, 吸附: -52.3230", "timestamp": 1770945353.8625019}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945353.8928282}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770945353.9172163}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2672, 体积: 110.79, 扩散: 5.0720, 吸附: -48.8770", "timestamp": 1770945353.9409862}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2672, 体积: 110.79, 扩散: 5.0720, 吸附: -48.8770", "timestamp": 1770945353.9645946}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "timestamp": 1770945353.989069}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "timestamp": 1770945354.008891}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3076, 体积: 137.79, 扩散: 5.5460, 吸附: -50.9460", "timestamp": 1770945354.028978}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3076, 体积: 137.79, 扩散: 5.5460, 吸附: -50.9460", "timestamp": 1770945354.0498056}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "timestamp": 1770945354.0702226}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "timestamp": 1770945354.0903177}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.5220, 体积: 128.98, 扩散: 5.1200, 吸附: -56.5830", "timestamp": 1770945354.1122696}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.5220, 体积: 128.98, 扩散: 5.1200, 吸附: -56.5830", "timestamp": 1770945354.1327603}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.47s", "timestamp": 1770945354.1548433}, {"type": "finish", "total_results": 5, "duration": 26.4722797870636, "timestamp": 1770945354.1754072}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/ae24e683-4af3-40db-b259-7bd0a2e36a7f.json", "timestamp": 1770945354.2049947}, {"type": "log", "level": "info", "message": "远程日志已上传: ae24e683-4af3-40db-b259-7bd0a2e36a7f", "timestamp": 1770945354.2487888}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770945354.276794}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770945354.2960186}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770945354.3191948}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770945354.3417027}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770945354.3648844}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770945354.388569}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770945354.418563}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770945354.4397528}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.78s", "timestamp": 1770945354.4608102}, {"type": "finish", "total_results": 5, "duration": 26.7782564163208, "timestamp": 1770945354.4827187}]}