{"metadata": {"log_name": "a6c5502c-b6ed-4782-9415-e699e4f0234d", "start_time": 1770963624.2369273, "end_time": 1770963651.5953462, "duration": 27.358418941497803, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.11200000000002, "diffusion": 4.923, "adsorption": -50.089}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770963625.8772113, "end_time": 1770963636.798996, "duration": 10.92178463935852, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.16038688526794925}, {"layer_num": 1, "start_time": 1770963637.0066535, "end_time": 1770963639.3120112, "duration": 2.3053576946258545, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.19255183583747154}, {"layer_num": 2, "start_time": 1770963639.5186794, "end_time": 1770963641.4783168, "duration": 1.9596374034881592, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.15936973908951269}, {"layer_num": 3, "start_time": 1770963641.6993663, "end_time": 1770963643.623185, "duration": 1.923818588256836, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.2150936799759408}, {"layer_num": 4, "start_time": 1770963643.9126415, "end_time": 1770963645.8437436, "duration": 1.9311020374298096, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.14133669198692547}, {"layer_num": 5, "start_time": 1770963646.2240622, "end_time": 1770963647.8085406, "duration": 1.5844783782958984, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.13293851739674595}, {"layer_num": 6, "start_time": 1770963648.1857567, "end_time": 1770963649.5753493, "duration": 1.3895926475524902, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1441701706268557}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.16038688526794925, "volume": 110.68800000000003, "diffusion": 5.213, "adsorption": -51.696, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.20574548700281037, "volume": 122.80000000000003, "diffusion": 4.677, "adsorption": -50.766, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.22907778626397898, "volume": 127.65600000000003, "diffusion": 5.093, "adsorption": -49.501, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24780231939352654, "volume": 133.96800000000002, "diffusion": 4.959, "adsorption": -50.487, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.24870601937201184, "volume": 118.91200000000003, "diffusion": 5.378, "adsorption": -48.814, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.2587945682196568, "volume": 129.36000000000004, "diffusion": 4.751, "adsorption": -49.27, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.26482170931886734, "volume": 127.34400000000004, "diffusion": 4.94, "adsorption": -48.338, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2743971557625621, "volume": 132.04000000000002, "diffusion": 5.126, "adsorption": -49.521, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2813373009577485, "volume": 126.84000000000003, "diffusion": 5.155, "adsorption": -48.715, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.29771711051918986, "volume": 126.44000000000003, "diffusion": 5.633, "adsorption": -50.176, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.3018667589155582, "volume": 134.11200000000002, "diffusion": 4.798, "adsorption": -51.066, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.33322882906472606, "volume": 130.12800000000004, "diffusion": 5.668, "adsorption": -50.087, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3388631780334638, "volume": 118.81600000000003, "diffusion": 5.96, "adsorption": -49.268, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.3539386085754002, "volume": 139.24800000000005, "diffusion": 4.661, "adsorption": -50.692, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.35595193876753406, "volume": 128.25600000000003, "diffusion": 5.357, "adsorption": -51.809, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.35878437564255405, "volume": 126.54400000000003, "diffusion": 4.971, "adsorption": -53.425, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.36180431143085423, "volume": 132.86400000000003, "diffusion": 5.357, "adsorption": -51.208, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.36231977076010735, "volume": 127.44000000000003, "diffusion": 4.001, "adsorption": -49.794, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.3645020174369589, "volume": 121.60000000000002, "diffusion": 5.168, "adsorption": -53.602, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.37280244054430567, "volume": 122.57600000000004, "diffusion": 4.748, "adsorption": -53.83, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3746169099000647, "volume": 131.56000000000003, "diffusion": 5.116, "adsorption": -52.437, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.37584687420748175, "volume": 126.48800000000003, "diffusion": 4.519, "adsorption": -52.511, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.3995656502894319, "volume": 125.92800000000003, "diffusion": 5.641, "adsorption": -48.251, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.4004115621440193, "volume": 129.34400000000002, "diffusion": 5.764, "adsorption": -49.172, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.40408971161370366, "volume": 137.99200000000002, "diffusion": 5.21, "adsorption": -51.635, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.4044544981611971, "volume": 147.12800000000004, "diffusion": 5.046, "adsorption": -49.366, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4085113185958653, "volume": 127.96800000000003, "diffusion": 5.59, "adsorption": -48.238, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.43061316226154567, "volume": 139.23200000000003, "diffusion": 5.419, "adsorption": -48.997, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4346749576313079, "volume": 132.64000000000004, "diffusion": 5.282, "adsorption": -52.713, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.44269764523175764, "volume": 119.32000000000002, "diffusion": 5.815, "adsorption": -47.119, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.4599076312799889, "volume": 122.49600000000002, "diffusion": 4.803, "adsorption": -55.483, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.46197738890315554, "volume": 135.69600000000003, "diffusion": 3.937, "adsorption": -49.599, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4770955387671424, "volume": 128.41600000000003, "diffusion": 6.275, "adsorption": -50.694, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.48038188857336683, "volume": 132.37600000000003, "diffusion": 5.027, "adsorption": -54.381, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4840257911948989, "volume": 145.78400000000005, "diffusion": 5.006, "adsorption": -52.48, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.48698185835832625, "volume": 126.37600000000003, "diffusion": 4.252, "adsorption": -46.7, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.4886303005539345, "volume": 149.46400000000003, "diffusion": 5.279, "adsorption": -51.073, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.49298860562270086, "volume": 146.50400000000005, "diffusion": 5.656, "adsorption": -50.316, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.5021410409212664, "volume": 150.72000000000003, "diffusion": 5.287, "adsorption": -49.097, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5138998071569796, "volume": 119.50400000000003, "diffusion": 6.507, "adsorption": -48.305, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5197606484227044, "volume": 132.45600000000002, "diffusion": 5.268, "adsorption": -45.969, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.5269654896456687, "volume": 122.76800000000003, "diffusion": 5.422, "adsorption": -55.276, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5342154125113269, "volume": 154.06400000000005, "diffusion": 5.255, "adsorption": -48.96, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.5382014847050536, "volume": 144.62400000000002, "diffusion": 5.873, "adsorption": -49.558, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5395643083308458, "volume": 135.60000000000002, "diffusion": 5.771, "adsorption": -47.821, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.5503883554824945, "volume": 126.49600000000002, "diffusion": 4.8, "adsorption": -56.377, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.552064828866855, "volume": 119.16000000000003, "diffusion": 6.204, "adsorption": -46.588, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5521004622637912, "volume": 146.20000000000005, "diffusion": 5.674, "adsorption": -51.288, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.553427923323045, "volume": 129.07200000000003, "diffusion": 5.146, "adsorption": -55.698, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.5578612595720194, "volume": 138.53600000000003, "diffusion": 5.547, "adsorption": -52.977, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5617044715698183, "volume": 137.84000000000003, "diffusion": 5.582, "adsorption": -47.15, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5723190944680823, "volume": 144.80800000000002, "diffusion": 5.645, "adsorption": -48.243, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5813308528754626, "volume": 126.55200000000004, "diffusion": 5.524, "adsorption": -44.914, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5892909287578341, "volume": 129.27200000000002, "diffusion": 4.142, "adsorption": -45.804, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.6032770131420273, "volume": 154.80800000000005, "diffusion": 4.81, "adsorption": -47.177, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6204736435117911, "volume": 133.44800000000004, "diffusion": 6.124, "adsorption": -52.837, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.6227543147226183, "volume": 134.90400000000002, "diffusion": 5.218, "adsorption": -55.727, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6273315577267184, "volume": 143.44000000000003, "diffusion": 5.088, "adsorption": -45.228, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.6283548286021424, "volume": 142.54400000000004, "diffusion": 5.259, "adsorption": -54.523, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6366506740488219, "volume": 146.81600000000003, "diffusion": 5.289, "adsorption": -46.266, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.6383551504139532, "volume": 142.77600000000004, "diffusion": 6.436, "adsorption": -49.515, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6434925493417147, "volume": 123.36000000000003, "diffusion": 4.284, "adsorption": -43.521, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6461095028510501, "volume": 129.63200000000003, "diffusion": 6.777, "adsorption": -48.549, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.646355617196279, "volume": 146.20000000000005, "diffusion": 5.096, "adsorption": -54.822, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.6638024262137029, "volume": 139.44000000000003, "diffusion": 4.67, "adsorption": -55.867, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.664069216814779, "volume": 130.72800000000004, "diffusion": 5.757, "adsorption": -44.956, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6650550257207213, "volume": 139.55200000000002, "diffusion": 6.407, "adsorption": -48.482, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.6770973992536966, "volume": 130.60800000000003, "diffusion": 6.331, "adsorption": -46.667, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7094691381412687, "volume": 133.84800000000004, "diffusion": 5.366, "adsorption": -43.345, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.7100079340185232, "volume": 140.26400000000004, "diffusion": 4.684, "adsorption": -43.617, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.7215814022256359, "volume": 146.83200000000002, "diffusion": 6.182, "adsorption": -47.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7405387545436373, "volume": 138.19200000000004, "diffusion": 4.817, "adsorption": -42.342, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.7409215486471432, "volume": 130.36800000000002, "diffusion": 6.893, "adsorption": -47.471, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.7422398798776977, "volume": 129.28000000000003, "diffusion": 5.907, "adsorption": -56.238, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.745235055088209, "volume": 139.22400000000005, "diffusion": 7.154, "adsorption": -50.551, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7610515966685826, "volume": 138.36800000000002, "diffusion": 7.404, "adsorption": -50.097, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.7644051829911918, "volume": 138.07200000000003, "diffusion": 5.843, "adsorption": -44.686, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7662999476247896, "volume": 156.42400000000004, "diffusion": 4.91, "adsorption": -44.361, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.77607358122261, "volume": 155.70400000000004, "diffusion": 4.988, "adsorption": -55.913, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7805921975029062, "volume": 146.04800000000003, "diffusion": 5.789, "adsorption": -54.736, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.7959366676113626, "volume": 156.51200000000003, "diffusion": 6.314, "adsorption": -48.553, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.8211040899021951, "volume": 139.47200000000004, "diffusion": 5.563, "adsorption": -43.002, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.833015645303263, "volume": 129.24000000000004, "diffusion": 5.638, "adsorption": -58.672, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8388028809309244, "volume": 136.34400000000002, "diffusion": 5.68, "adsorption": -42.629, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.8455710466220179, "volume": 134.76000000000002, "diffusion": 5.517, "adsorption": -58.455, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.8517144976806321, "volume": 146.08800000000002, "diffusion": 5.693, "adsorption": -56.243, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8537611697973838, "volume": 133.68000000000004, "diffusion": 6.116, "adsorption": -43.454, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8641263302353436, "volume": 127.48800000000003, "diffusion": 5.886, "adsorption": -58.616, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8896160825172887, "volume": 155.70400000000004, "diffusion": 5.465, "adsorption": -43.987, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.8905715495911973, "volume": 154.61600000000004, "diffusion": 5.991, "adsorption": -45.59, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.8921083853160175, "volume": 150.88000000000002, "diffusion": 4.962, "adsorption": -41.508, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8963729430132702, "volume": 149.09600000000003, "diffusion": 5.725, "adsorption": -43.754, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8994729989738911, "volume": 151.72000000000003, "diffusion": 5.786, "adsorption": -44.307, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9126774767064073, "volume": 137.49600000000004, "diffusion": 5.544, "adsorption": -41.109, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.9212760157749702, "volume": 143.40800000000004, "diffusion": 7.105, "adsorption": -53.022, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.941663789791956, "volume": 135.04000000000002, "diffusion": 6.112, "adsorption": -42.179, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9430629865271067, "volume": 134.64800000000002, "diffusion": 6.206, "adsorption": -42.415, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.955228967365255, "volume": 146.32800000000003, "diffusion": 5.279, "adsorption": -59.339, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0131040543315588, "volume": 138.23200000000003, "diffusion": 7.266, "adsorption": -45.384, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0136803540337327, "volume": 140.21600000000004, "diffusion": 5.927, "adsorption": -41.134, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.0529719027818623, "volume": 146.35200000000003, "diffusion": 6.442, "adsorption": -43.155, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.2137061535734, "volume": 155.00000000000003, "diffusion": 6.116, "adsorption": -40.677, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.2413023740132547, "volume": 123.23200000000003, "diffusion": 5.957, "adsorption": -65.318, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2474337642470164, "volume": 157.35200000000003, "diffusion": 5.282, "adsorption": -37.642, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.247689726564377, "volume": 136.19200000000004, "diffusion": 5.238, "adsorption": -34.28, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.2540332098141151, "volume": 155.54400000000004, "diffusion": 6.348, "adsorption": -40.873, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2865260422189047, "volume": 147.88000000000002, "diffusion": 6.112, "adsorption": -38.368, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.294719712333506, "volume": 133.20800000000003, "diffusion": 5.909, "adsorption": -35.318, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.4429251832827075, "volume": 145.26400000000004, "diffusion": 6.117, "adsorption": -35.377, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.19255183583747154, "volume": 121.96800000000003, "diffusion": 4.897, "adsorption": -51.418, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.2017137516230267, "volume": 118.52800000000003, "diffusion": 5.022, "adsorption": -48.333, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23747930295781355, "volume": 110.35200000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.25212350808987516, "volume": 128.23200000000003, "diffusion": 5.347, "adsorption": -50.065, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5372389139996943, "volume": 134.03200000000004, "diffusion": 4.956, "adsorption": -55.32, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.15936973908951269, "volume": 110.57600000000002, "diffusion": 5.213, "adsorption": -51.696, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.22516303710949548, "volume": 121.87200000000003, "diffusion": 4.667, "adsorption": -48.981, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23801615547451582, "volume": 128.01600000000002, "diffusion": 5.087, "adsorption": -49.386, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3193182300691006, "volume": 119.02400000000003, "diffusion": 5.286, "adsorption": -47.34, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4684696551005892, "volume": 134.88000000000002, "diffusion": 4.634, "adsorption": -47.003, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2150936799759408, "volume": 110.60000000000002, "diffusion": 5.478, "adsorption": -51.724, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2552083291412595, "volume": 128.32800000000003, "diffusion": 5.306, "adsorption": -49.889, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.26891877251407126, "volume": 119.28000000000003, "diffusion": 5.063, "adsorption": -47.464, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.27972088418813734, "volume": 122.78400000000003, "diffusion": 5.058, "adsorption": -52.38, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4024468269987779, "volume": 134.30400000000003, "diffusion": 4.634, "adsorption": -48.018, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.14133669198692547, "volume": 110.18400000000003, "diffusion": 5.242, "adsorption": -51.356, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23023226867777882, "volume": 129.22400000000002, "diffusion": 5.079, "adsorption": -49.672, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.24910553912973987, "volume": 122.32800000000003, "diffusion": 4.668, "adsorption": -48.647, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2539109329979731, "volume": 134.21600000000004, "diffusion": 4.937, "adsorption": -50.626, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.2551391447988472, "volume": 119.19200000000002, "diffusion": 5.36, "adsorption": -48.688, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.13293851739674595, "volume": 110.50400000000003, "diffusion": 5.235, "adsorption": -51.191, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.24201071088686074, "volume": 119.08000000000003, "diffusion": 5.15, "adsorption": -48.178, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2750511362127085, "volume": 134.28000000000003, "diffusion": 4.93, "adsorption": -50.993, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.35017843614220195, "volume": 122.13600000000002, "diffusion": 4.628, "adsorption": -47.066, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6063932086384283, "volume": 128.64800000000002, "diffusion": 5.873, "adsorption": -45.997, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1441701706268557, "volume": 110.49600000000002, "diffusion": 5.242, "adsorption": -51.356, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2207660874706072, "volume": 123.07200000000003, "diffusion": 4.553, "adsorption": -49.623, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.24961120216068894, "volume": 118.86400000000003, "diffusion": 5.379, "adsorption": -48.795, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2695254372206681, "volume": 128.58400000000003, "diffusion": 5.35, "adsorption": -49.838, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4329528689074195, "volume": 134.25600000000003, "diffusion": 4.589, "adsorption": -52.524, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.11200000000002, "diffusion": 4.923, "adsorption": -50.089}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.1441701706268557, "volume": 110.49600000000002, "diffusion": 5.242, "adsorption": -51.356, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.2207660874706072, "volume": 123.07200000000003, "diffusion": 4.553, "adsorption": -49.623, "is_selected": false, "rank": 2}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.2695254372206681, "volume": 128.58400000000003, "diffusion": 5.35, "adsorption": -49.838, "is_selected": false, "rank": 4}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.4329528689074195, "volume": 134.25600000000003, "diffusion": 4.589, "adsorption": -52.524, "is_selected": false, "rank": 5}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.24961120216068894, "volume": 118.86400000000003, "diffusion": 5.379, "adsorption": -48.795, "is_selected": false, "rank": 3}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.2587945682196568, "volume": 129.36000000000004, "diffusion": 4.751, "adsorption": -49.27, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.26482170931886734, "volume": 127.34400000000004, "diffusion": 4.94, "adsorption": -48.338, "is_selected": false, "rank": 7}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.2743971557625621, "volume": 132.04000000000002, "diffusion": 5.126, "adsorption": -49.521, "is_selected": false, "rank": 8}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.2813373009577485, "volume": 126.84000000000003, "diffusion": 5.155, "adsorption": -48.715, "is_selected": false, "rank": 9}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.29771711051918986, "volume": 126.44000000000003, "diffusion": 5.633, "adsorption": -50.176, "is_selected": false, "rank": 10}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.3018667589155582, "volume": 134.11200000000002, "diffusion": 4.798, "adsorption": -51.066, "is_selected": false, "rank": 11}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.33322882906472606, "volume": 130.12800000000004, "diffusion": 5.668, "adsorption": -50.087, "is_selected": false, "rank": 12}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3388631780334638, "volume": 118.81600000000003, "diffusion": 5.96, "adsorption": -49.268, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3539386085754002, "volume": 139.24800000000005, "diffusion": 4.661, "adsorption": -50.692, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.35595193876753406, "volume": 128.25600000000003, "diffusion": 5.357, "adsorption": -51.809, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.35878437564255405, "volume": 126.54400000000003, "diffusion": 4.971, "adsorption": -53.425, "is_selected": false, "rank": 16}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.36180431143085423, "volume": 132.86400000000003, "diffusion": 5.357, "adsorption": -51.208, "is_selected": false, "rank": 17}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.36231977076010735, "volume": 127.44000000000003, "diffusion": 4.001, "adsorption": -49.794, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.3645020174369589, "volume": 121.60000000000002, "diffusion": 5.168, "adsorption": -53.602, "is_selected": false, "rank": 19}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.37280244054430567, "volume": 122.57600000000004, "diffusion": 4.748, "adsorption": -53.83, "is_selected": false, "rank": 20}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.3746169099000647, "volume": 131.56000000000003, "diffusion": 5.116, "adsorption": -52.437, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.37584687420748175, "volume": 126.48800000000003, "diffusion": 4.519, "adsorption": -52.511, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.3995656502894319, "volume": 125.92800000000003, "diffusion": 5.641, "adsorption": -48.251, "is_selected": false, "rank": 23}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.4004115621440193, "volume": 129.34400000000002, "diffusion": 5.764, "adsorption": -49.172, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.40408971161370366, "volume": 137.99200000000002, "diffusion": 5.21, "adsorption": -51.635, "is_selected": false, "rank": 25}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.4044544981611971, "volume": 147.12800000000004, "diffusion": 5.046, "adsorption": -49.366, "is_selected": false, "rank": 26}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4085113185958653, "volume": 127.96800000000003, "diffusion": 5.59, "adsorption": -48.238, "is_selected": false, "rank": 27}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.43061316226154567, "volume": 139.23200000000003, "diffusion": 5.419, "adsorption": -48.997, "is_selected": false, "rank": 28}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4346749576313079, "volume": 132.64000000000004, "diffusion": 5.282, "adsorption": -52.713, "is_selected": false, "rank": 29}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.44269764523175764, "volume": 119.32000000000002, "diffusion": 5.815, "adsorption": -47.119, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4599076312799889, "volume": 122.49600000000002, "diffusion": 4.803, "adsorption": -55.483, "is_selected": false, "rank": 31}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.46197738890315554, "volume": 135.69600000000003, "diffusion": 3.937, "adsorption": -49.599, "is_selected": false, "rank": 32}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4770955387671424, "volume": 128.41600000000003, "diffusion": 6.275, "adsorption": -50.694, "is_selected": false, "rank": 33}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.48038188857336683, "volume": 132.37600000000003, "diffusion": 5.027, "adsorption": -54.381, "is_selected": false, "rank": 34}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4840257911948989, "volume": 145.78400000000005, "diffusion": 5.006, "adsorption": -52.48, "is_selected": false, "rank": 35}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.48698185835832625, "volume": 126.37600000000003, "diffusion": 4.252, "adsorption": -46.7, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.4886303005539345, "volume": 149.46400000000003, "diffusion": 5.279, "adsorption": -51.073, "is_selected": false, "rank": 37}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.49298860562270086, "volume": 146.50400000000005, "diffusion": 5.656, "adsorption": -50.316, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5021410409212664, "volume": 150.72000000000003, "diffusion": 5.287, "adsorption": -49.097, "is_selected": false, "rank": 39}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5138998071569796, "volume": 119.50400000000003, "diffusion": 6.507, "adsorption": -48.305, "is_selected": false, "rank": 40}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5197606484227044, "volume": 132.45600000000002, "diffusion": 5.268, "adsorption": -45.969, "is_selected": false, "rank": 41}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.5269654896456687, "volume": 122.76800000000003, "diffusion": 5.422, "adsorption": -55.276, "is_selected": false, "rank": 42}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5342154125113269, "volume": 154.06400000000005, "diffusion": 5.255, "adsorption": -48.96, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.5382014847050536, "volume": 144.62400000000002, "diffusion": 5.873, "adsorption": -49.558, "is_selected": false, "rank": 44}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5395643083308458, "volume": 135.60000000000002, "diffusion": 5.771, "adsorption": -47.821, "is_selected": false, "rank": 45}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5503883554824945, "volume": 126.49600000000002, "diffusion": 4.8, "adsorption": -56.377, "is_selected": false, "rank": 46}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.552064828866855, "volume": 119.16000000000003, "diffusion": 6.204, "adsorption": -46.588, "is_selected": false, "rank": 47}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5521004622637912, "volume": 146.20000000000005, "diffusion": 5.674, "adsorption": -51.288, "is_selected": false, "rank": 48}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.553427923323045, "volume": 129.07200000000003, "diffusion": 5.146, "adsorption": -55.698, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.5578612595720194, "volume": 138.53600000000003, "diffusion": 5.547, "adsorption": -52.977, "is_selected": false, "rank": 50}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5617044715698183, "volume": 137.84000000000003, "diffusion": 5.582, "adsorption": -47.15, "is_selected": false, "rank": 51}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5723190944680823, "volume": 144.80800000000002, "diffusion": 5.645, "adsorption": -48.243, "is_selected": false, "rank": 52}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5813308528754626, "volume": 126.55200000000004, "diffusion": 5.524, "adsorption": -44.914, "is_selected": false, "rank": 53}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5892909287578341, "volume": 129.27200000000002, "diffusion": 4.142, "adsorption": -45.804, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6032770131420273, "volume": 154.80800000000005, "diffusion": 4.81, "adsorption": -47.177, "is_selected": false, "rank": 55}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6204736435117911, "volume": 133.44800000000004, "diffusion": 6.124, "adsorption": -52.837, "is_selected": false, "rank": 56}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6227543147226183, "volume": 134.90400000000002, "diffusion": 5.218, "adsorption": -55.727, "is_selected": false, "rank": 57}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6273315577267184, "volume": 143.44000000000003, "diffusion": 5.088, "adsorption": -45.228, "is_selected": false, "rank": 58}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.6283548286021424, "volume": 142.54400000000004, "diffusion": 5.259, "adsorption": -54.523, "is_selected": false, "rank": 59}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6366506740488219, "volume": 146.81600000000003, "diffusion": 5.289, "adsorption": -46.266, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.6383551504139532, "volume": 142.77600000000004, "diffusion": 6.436, "adsorption": -49.515, "is_selected": false, "rank": 61}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6434925493417147, "volume": 123.36000000000003, "diffusion": 4.284, "adsorption": -43.521, "is_selected": false, "rank": 62}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6461095028510501, "volume": 129.63200000000003, "diffusion": 6.777, "adsorption": -48.549, "is_selected": false, "rank": 63}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.646355617196279, "volume": 146.20000000000005, "diffusion": 5.096, "adsorption": -54.822, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.6638024262137029, "volume": 139.44000000000003, "diffusion": 4.67, "adsorption": -55.867, "is_selected": false, "rank": 65}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.664069216814779, "volume": 130.72800000000004, "diffusion": 5.757, "adsorption": -44.956, "is_selected": false, "rank": 66}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6650550257207213, "volume": 139.55200000000002, "diffusion": 6.407, "adsorption": -48.482, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.6770973992536966, "volume": 130.60800000000003, "diffusion": 6.331, "adsorption": -46.667, "is_selected": false, "rank": 68}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7094691381412687, "volume": 133.84800000000004, "diffusion": 5.366, "adsorption": -43.345, "is_selected": false, "rank": 69}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.7100079340185232, "volume": 140.26400000000004, "diffusion": 4.684, "adsorption": -43.617, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7215814022256359, "volume": 146.83200000000002, "diffusion": 6.182, "adsorption": -47.879, "is_selected": false, "rank": 71}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7405387545436373, "volume": 138.19200000000004, "diffusion": 4.817, "adsorption": -42.342, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.7409215486471432, "volume": 130.36800000000002, "diffusion": 6.893, "adsorption": -47.471, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7422398798776977, "volume": 129.28000000000003, "diffusion": 5.907, "adsorption": -56.238, "is_selected": false, "rank": 74}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.745235055088209, "volume": 139.22400000000005, "diffusion": 7.154, "adsorption": -50.551, "is_selected": false, "rank": 75}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7610515966685826, "volume": 138.36800000000002, "diffusion": 7.404, "adsorption": -50.097, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.7644051829911918, "volume": 138.07200000000003, "diffusion": 5.843, "adsorption": -44.686, "is_selected": false, "rank": 77}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7662999476247896, "volume": 156.42400000000004, "diffusion": 4.91, "adsorption": -44.361, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.77607358122261, "volume": 155.70400000000004, "diffusion": 4.988, "adsorption": -55.913, "is_selected": false, "rank": 79}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7805921975029062, "volume": 146.04800000000003, "diffusion": 5.789, "adsorption": -54.736, "is_selected": false, "rank": 80}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7959366676113626, "volume": 156.51200000000003, "diffusion": 6.314, "adsorption": -48.553, "is_selected": false, "rank": 81}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.8211040899021951, "volume": 139.47200000000004, "diffusion": 5.563, "adsorption": -43.002, "is_selected": false, "rank": 82}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.833015645303263, "volume": 129.24000000000004, "diffusion": 5.638, "adsorption": -58.672, "is_selected": false, "rank": 83}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8388028809309244, "volume": 136.34400000000002, "diffusion": 5.68, "adsorption": -42.629, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.8455710466220179, "volume": 134.76000000000002, "diffusion": 5.517, "adsorption": -58.455, "is_selected": false, "rank": 85}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8517144976806321, "volume": 146.08800000000002, "diffusion": 5.693, "adsorption": -56.243, "is_selected": false, "rank": 86}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8537611697973838, "volume": 133.68000000000004, "diffusion": 6.116, "adsorption": -43.454, "is_selected": false, "rank": 87}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8641263302353436, "volume": 127.48800000000003, "diffusion": 5.886, "adsorption": -58.616, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8896160825172887, "volume": 155.70400000000004, "diffusion": 5.465, "adsorption": -43.987, "is_selected": false, "rank": 89}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.8905715495911973, "volume": 154.61600000000004, "diffusion": 5.991, "adsorption": -45.59, "is_selected": false, "rank": 90}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.8921083853160175, "volume": 150.88000000000002, "diffusion": 4.962, "adsorption": -41.508, "is_selected": false, "rank": 91}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8963729430132702, "volume": 149.09600000000003, "diffusion": 5.725, "adsorption": -43.754, "is_selected": false, "rank": 92}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8994729989738911, "volume": 151.72000000000003, "diffusion": 5.786, "adsorption": -44.307, "is_selected": false, "rank": 93}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9126774767064073, "volume": 137.49600000000004, "diffusion": 5.544, "adsorption": -41.109, "is_selected": false, "rank": 94}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.9212760157749702, "volume": 143.40800000000004, "diffusion": 7.105, "adsorption": -53.022, "is_selected": false, "rank": 95}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.941663789791956, "volume": 135.04000000000002, "diffusion": 6.112, "adsorption": -42.179, "is_selected": false, "rank": 96}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9430629865271067, "volume": 134.64800000000002, "diffusion": 6.206, "adsorption": -42.415, "is_selected": false, "rank": 97}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.955228967365255, "volume": 146.32800000000003, "diffusion": 5.279, "adsorption": -59.339, "is_selected": false, "rank": 98}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0131040543315588, "volume": 138.23200000000003, "diffusion": 7.266, "adsorption": -45.384, "is_selected": false, "rank": 99}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0136803540337327, "volume": 140.21600000000004, "diffusion": 5.927, "adsorption": -41.134, "is_selected": false, "rank": 100}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.0529719027818623, "volume": 146.35200000000003, "diffusion": 6.442, "adsorption": -43.155, "is_selected": false, "rank": 101}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2137061535734, "volume": 155.00000000000003, "diffusion": 6.116, "adsorption": -40.677, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.2413023740132547, "volume": 123.23200000000003, "diffusion": 5.957, "adsorption": -65.318, "is_selected": false, "rank": 103}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2474337642470164, "volume": 157.35200000000003, "diffusion": 5.282, "adsorption": -37.642, "is_selected": false, "rank": 104}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.247689726564377, "volume": 136.19200000000004, "diffusion": 5.238, "adsorption": -34.28, "is_selected": false, "rank": 105}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.2540332098141151, "volume": 155.54400000000004, "diffusion": 6.348, "adsorption": -40.873, "is_selected": false, "rank": 106}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2865260422189047, "volume": 147.88000000000002, "diffusion": 6.112, "adsorption": -38.368, "is_selected": false, "rank": 107}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.294719712333506, "volume": 133.20800000000003, "diffusion": 5.909, "adsorption": -35.318, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.4429251832827075, "volume": 145.26400000000004, "diffusion": 6.117, "adsorption": -35.377, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770963624.246634}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: a6c5502c-b6ed-4782-9415-e699e4f0234d", "timestamp": 1770963624.3698652}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/a6c5502c-b6ed-4782-9415-e699e4f0234d.log", "timestamp": 1770963624.4206305}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770963625.0246193}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.1120, 扩散: 4.9230, 吸附: -50.0890", "timestamp": 1770963625.0498338}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770963625.1177144}, {"type": "log", "level": "info", "message": "远程日志已启动: a6c5502c-b6ed-4782-9415-e699e4f0234d", "timestamp": 1770963625.1205072}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963625.2093663}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770963625.2772236}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770963625.3409352}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.11, 扩散: 4.9230, 吸附: -50.0890", "timestamp": 1770963625.3971884}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770963625.4443147}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963625.4847379}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770963625.5107744}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.1120, 扩散: 4.9230, 吸附: -50.0890", "timestamp": 1770963625.5354955}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770963625.562906}, {"type": "log", "level": "info", "message": "远程日志已启动: a6c5502c-b6ed-4782-9415-e699e4f0234d", "timestamp": 1770963625.604392}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770963625.6249936}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770963625.6786213}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770963625.698346}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963625.7408798}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963625.7779343}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770963625.817204}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963625.857858}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770963625.8777432}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.04 秒", "timestamp": 1770963625.9644914}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770963626.0046365}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.04s, 速度: 4378.51 mol/s", "timestamp": 1770963626.0321274}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.039282798767089844, "speed": 4378.506761185696, "timestamp": 1770963626.0601041}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770963626.1313808}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770963626.1848419}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770963626.230983}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770963626.2503362}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770963626.2676644}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770963626.3112268}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770963636.3036942}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1604", "timestamp": 1770963636.3347125}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.16038688526794925, "timestamp": 1770963636.3642845}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963636.4240232}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963636.45216}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770963636.4751275}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963636.5368366}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1604", "timestamp": 1770963636.587558}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.69", "timestamp": 1770963636.6403854}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770963636.6896002}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770963636.730774}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.92s) ==========", "timestamp": 1770963636.800001}, {"type": "layer_end", "layer": 0, "duration": 10.92178463935852, "timestamp": 1770963636.8209777}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963636.860362}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963636.9028714}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770963636.951433}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963636.9899569}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770963637.0068746}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770963637.0443773}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770963637.077755}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 33060.73 mol/s", "timestamp": 1770963637.0969064}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0009074211120605469, "speed": 33060.72517078297, "timestamp": 1770963637.1158676}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770963637.1609821}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770963637.1776597}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770963637.192757}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770963637.2332287}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963638.972328}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H], Score: 0.1926", "timestamp": 1770963638.9947174}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "best_score": 0.19255183583747154, "timestamp": 1770963639.0127912}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963639.0475805}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963639.0663688}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770963639.0851462}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770963639.1239598}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1926", "timestamp": 1770963639.163199}, {"type": "log", "level": "info", "message": "INFO -     体积: 121.97", "timestamp": 1770963639.2026036}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.8970", "timestamp": 1770963639.243224}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.4180", "timestamp": 1770963639.2857122}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 2.31s) ==========", "timestamp": 1770963639.3125324}, {"type": "layer_end", "layer": 1, "duration": 2.3053576946258545, "timestamp": 1770963639.3345542}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963639.37839}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963639.4247181}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770963639.4695241}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963639.5028372}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770963639.5188713}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770963639.550372}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770963639.5804255}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 31803.94 mol/s", "timestamp": 1770963639.5956357}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0007860660552978516, "speed": 31803.94297846527, "timestamp": 1770963639.6095543}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770963639.6404428}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770963639.6575425}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770963639.6724114}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770963639.702302}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963641.1411114}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1594", "timestamp": 1770963641.1618733}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.15936973908951269, "timestamp": 1770963641.1791859}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963641.2137175}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963641.230217}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770963641.246793}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963641.282313}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1594", "timestamp": 1770963641.3241346}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.58", "timestamp": 1770963641.362336}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2130", "timestamp": 1770963641.4092817}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6960", "timestamp": 1770963641.4515853}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 1.96s) ==========", "timestamp": 1770963641.4793935}, {"type": "layer_end", "layer": 2, "duration": 1.9596374034881592, "timestamp": 1770963641.499264}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963641.5440233}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963641.5847135}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770963641.6294062}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963641.6774914}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770963641.6997192}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770963641.749075}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770963641.7951674}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 24672.38 mol/s", "timestamp": 1770963641.817916}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0008106231689453125, "speed": 24672.376470588235, "timestamp": 1770963641.8362734}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770963641.8853035}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770963641.9139397}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770963641.9415278}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770963642.0001855}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963643.2039022}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2151", "timestamp": 1770963643.2255754}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.2150936799759408, "timestamp": 1770963643.2474654}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963643.288993}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963643.3083994}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770963643.3298662}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963643.3716524}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2151", "timestamp": 1770963643.4148512}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.60", "timestamp": 1770963643.473481}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.4780", "timestamp": 1770963643.5212867}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.7240", "timestamp": 1770963643.5926437}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 1.92s) ==========", "timestamp": 1770963643.6241145}, {"type": "layer_end", "layer": 3, "duration": 1.923818588256836, "timestamp": 1770963643.6552863}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963643.713327}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963643.767059}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770963643.8264492}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963643.8864496}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770963643.9130158}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770963643.972976}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770963644.0244055}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 13175.82 mol/s", "timestamp": 1770963644.050924}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.001138448715209961, "speed": 13175.824083769634, "timestamp": 1770963644.0733576}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770963644.1330523}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770963644.1613955}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770963644.1875005}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770963644.2483354}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963645.2260315}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1413", "timestamp": 1770963645.260516}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.14133669198692547, "timestamp": 1770963645.2915587}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963645.3842409}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963645.4201622}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770963645.4538863}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963645.540512}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1413", "timestamp": 1770963645.6112883}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.18", "timestamp": 1770963645.678755}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770963645.7436616}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770963645.8043838}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 1.93s) ==========", "timestamp": 1770963645.844841}, {"type": "layer_end", "layer": 4, "duration": 1.9311020374298096, "timestamp": 1770963645.8803275}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963645.9658196}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963646.041936}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770963646.1077743}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963646.1935122}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770963646.2243226}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770963646.290349}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770963646.3572316}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 13438.97 mol/s", "timestamp": 1770963646.3850338}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0007441043853759766, "speed": 13438.974687600128, "timestamp": 1770963646.4097717}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770963646.4923882}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770963646.5350401}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770963646.5730872}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770963646.6574388}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963647.3795786}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1329", "timestamp": 1770963647.4009373}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.13293851739674595, "timestamp": 1770963647.4207745}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963647.4564126}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963647.4777534}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770963647.4984212}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963647.5359988}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1329", "timestamp": 1770963647.587803}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.50", "timestamp": 1770963647.6294885}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2350", "timestamp": 1770963647.6993122}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.1910", "timestamp": 1770963647.76723}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.58s) ==========", "timestamp": 1770963647.8088899}, {"type": "layer_end", "layer": 5, "duration": 1.5844783782958984, "timestamp": 1770963647.8447304}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963647.9215796}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963647.9968095}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770963648.06615}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963648.1466205}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770963648.1860876}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770963648.2625751}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770963648.3265948}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 9795.20 mol/s", "timestamp": 1770963648.365378}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0005104541778564453, "speed": 9795.198505371322, "timestamp": 1770963648.3926072}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770963648.468854}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770963648.4936013}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770963648.5167613}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770963648.5613954}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770963649.0177615}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1442", "timestamp": 1770963649.039096}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1441701706268557, "timestamp": 1770963649.0667648}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770963649.1257224}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770963649.1527953}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770963649.1772175}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963649.245729}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1442", "timestamp": 1770963649.3244612}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.50", "timestamp": 1770963649.396624}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770963649.4716628}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770963649.5377634}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.39s) ==========", "timestamp": 1770963649.5757496}, {"type": "layer_end", "layer": 6, "duration": 1.3895926475524902, "timestamp": 1770963649.6052625}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770963650.06355}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963650.1256058}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770963650.1883466}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 23.92 秒", "timestamp": 1770963650.2549627}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770963650.3177743}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770963650.3759856}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770963650.4415088}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770963650.50485}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2037, 体积: 123.13, 扩散: 4.9040, 吸附: -51.4520", "timestamp": 1770963650.561471}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963650.619973}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2203, 体积: 110.30, 扩散: 5.5460, 吸附: -51.6260", "timestamp": 1770963650.6751025}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963650.7342758}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2596, 体积: 127.50, 扩散: 5.3500, 吸附: -49.8380", "timestamp": 1770963650.8001065}, {"type": "log", "level": "info", "message": "INFO -   4. [H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770963650.8636272}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2945, 体积: 134.44, 扩散: 4.6700, 吸附: -50.4590", "timestamp": 1770963650.929129}, {"type": "log", "level": "info", "message": "INFO -   5. [H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770963650.9879}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3504, 体积: 118.06, 扩散: 5.4290, 吸附: -47.1590", "timestamp": 1770963651.051532}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 26.84s", "timestamp": 1770963651.082043}, {"type": "finish", "total_results": 5, "duration": 26.844427824020386, "timestamp": 1770963651.1143293}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/a6c5502c-b6ed-4782-9415-e699e4f0234d.json", "timestamp": 1770963651.161244}, {"type": "log", "level": "info", "message": "远程日志已上传: a6c5502c-b6ed-4782-9415-e699e4f0234d", "timestamp": 1770963651.2487113}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770963651.3188124}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770963651.393358}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770963651.4370213}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.1120, 扩散: 4.9230, 吸附: -50.0890", "timestamp": 1770963651.4713998}, {"type": "log", "level": "info", "message": "远程日志已启动: a6c5502c-b6ed-4782-9415-e699e4f0234d", "timestamp": 1770963651.5365274}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770963651.5582778}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 27.36s", "timestamp": 1770963651.5955644}, {"type": "finish", "total_results": 5, "duration": 27.358418941497803, "timestamp": 1770963651.6284416}]}