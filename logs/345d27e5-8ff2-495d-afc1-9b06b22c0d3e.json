{"metadata": {"log_name": "345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "start_time": 1770966902.174659, "end_time": 1770966934.531355, "duration": 32.356695890426636, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.65600000000002, "diffusion": 5.242, "adsorption": -51.356}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770966903.9989371, "end_time": 1770966915.153123, "duration": 11.154185771942139, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0145169680039118}, {"layer_num": 1, "start_time": 1770966915.5514517, "end_time": 1770966918.606239, "duration": 3.0547873973846436, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18894224803412735}, {"layer_num": 2, "start_time": 1770966919.0691156, "end_time": 1770966922.3251264, "duration": 3.2560107707977295, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18124740759972843}, {"layer_num": 3, "start_time": 1770966922.9610362, "end_time": 1770966925.3365781, "duration": 2.375541925430298, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0041208791208790195}, {"layer_num": 4, "start_time": 1770966925.672115, "end_time": 1770966927.9077322, "duration": 2.235617160797119, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07528360742196041}, {"layer_num": 5, "start_time": 1770966928.265714, "end_time": 1770966930.1177115, "duration": 1.8519976139068604, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1838509281221134}, {"layer_num": 6, "start_time": 1770966930.5072827, "end_time": 1770966932.1237175, "duration": 1.6164348125457764, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.09888920028391784}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0145169680039118, "volume": 110.36000000000003, "diffusion": 5.228, "adsorption": -51.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.24062888450425202, "volume": 126.57600000000004, "diffusion": 4.834, "adsorption": -51.68, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24500041371045825, "volume": 126.70400000000004, "diffusion": 4.8, "adsorption": -51.088, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.28563781094186763, "volume": 133.44000000000003, "diffusion": 4.922, "adsorption": -51.036, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.29009417704079743, "volume": 132.51200000000003, "diffusion": 5.377, "adsorption": -50.212, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.30058442794364537, "volume": 126.38400000000003, "diffusion": 5.165, "adsorption": -48.895, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.3045935579518058, "volume": 129.72000000000003, "diffusion": 5.178, "adsorption": -53.412, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3051840470134347, "volume": 129.68000000000004, "diffusion": 5.612, "adsorption": -50.283, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3090142716722333, "volume": 118.82400000000003, "diffusion": 5.804, "adsorption": -49.165, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.31155561425373596, "volume": 134.77600000000004, "diffusion": 4.986, "adsorption": -50.59, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3125437134369976, "volume": 119.46400000000003, "diffusion": 5.256, "adsorption": -47.414, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.31413221451893053, "volume": 126.36800000000002, "diffusion": 5.727, "adsorption": -49.993, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.328484359828056, "volume": 127.55200000000004, "diffusion": 4.612, "adsorption": -52.308, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.34885205363165805, "volume": 122.26400000000002, "diffusion": 5.472, "adsorption": -54.781, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.3499399924180279, "volume": 137.37600000000003, "diffusion": 5.186, "adsorption": -53.03, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.36293676925895935, "volume": 129.20000000000002, "diffusion": 6.244, "adsorption": -51.428, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3658742869478817, "volume": 121.58400000000003, "diffusion": 4.671, "adsorption": -48.648, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.3760262534454094, "volume": 137.53600000000003, "diffusion": 5.557, "adsorption": -52.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.3795120140154124, "volume": 127.92000000000003, "diffusion": 5.212, "adsorption": -55.084, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3832195642290812, "volume": 125.77600000000002, "diffusion": 6.063, "adsorption": -49.816, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.39205269662293335, "volume": 127.64800000000002, "diffusion": 4.095, "adsorption": -51.693, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3924834621098661, "volume": 122.84000000000003, "diffusion": 5.603, "adsorption": -55.011, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.401523985461964, "volume": 128.89600000000004, "diffusion": 5.239, "adsorption": -55.398, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.40881362519077397, "volume": 134.24800000000002, "diffusion": 4.66, "adsorption": -49.908, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4175103198565414, "volume": 138.38400000000004, "diffusion": 4.69, "adsorption": -50.301, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.4214041881754849, "volume": 142.38400000000004, "diffusion": 5.944, "adsorption": -51.343, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4267128580155567, "volume": 146.00000000000003, "diffusion": 5.464, "adsorption": -50.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.42828153396480284, "volume": 139.86400000000003, "diffusion": 5.965, "adsorption": -50.904, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4300588087202711, "volume": 131.40000000000003, "diffusion": 5.009, "adsorption": -54.748, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4301975847350141, "volume": 118.77600000000002, "diffusion": 5.815, "adsorption": -47.119, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4304481613641168, "volume": 127.40000000000003, "diffusion": 4.947, "adsorption": -47.541, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4437385886137609, "volume": 150.61600000000004, "diffusion": 5.375, "adsorption": -50.376, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.453706319372139, "volume": 138.95200000000003, "diffusion": 5.3, "adsorption": -48.156, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.47520509465835875, "volume": 133.73600000000002, "diffusion": 5.951, "adsorption": -49.107, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.47549147850603946, "volume": 134.85600000000002, "diffusion": 5.242, "adsorption": -55.752, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.478628774934781, "volume": 122.80000000000003, "diffusion": 4.761, "adsorption": -56.1, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4854474439387778, "volume": 131.69600000000003, "diffusion": 5.064, "adsorption": -55.83, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.49122435298311196, "volume": 139.59200000000004, "diffusion": 5.922, "adsorption": -49.644, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.49774757467771313, "volume": 136.17600000000004, "diffusion": 5.388, "adsorption": -47.26, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4993413771008058, "volume": 155.05600000000004, "diffusion": 5.079, "adsorption": -50.209, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.503160957443282, "volume": 128.64000000000004, "diffusion": 5.064, "adsorption": -56.606, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.508927307469544, "volume": 146.13600000000002, "diffusion": 5.838, "adsorption": -50.079, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5137232030107168, "volume": 119.99200000000003, "diffusion": 6.356, "adsorption": -47.644, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.5241555092047807, "volume": 135.08000000000004, "diffusion": 5.635, "adsorption": -55.267, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.5274249765498895, "volume": 146.53600000000003, "diffusion": 5.091, "adsorption": -48.371, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5311006588877275, "volume": 118.93600000000002, "diffusion": 6.043, "adsorption": -46.161, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.5411136112882132, "volume": 145.06400000000002, "diffusion": 5.811, "adsorption": -53.438, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5471030133284213, "volume": 128.97600000000003, "diffusion": 4.442, "adsorption": -47.437, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5563532381959171, "volume": 139.76000000000002, "diffusion": 6.396, "adsorption": -50.103, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.5567963586369697, "volume": 142.52000000000004, "diffusion": 5.038, "adsorption": -55.292, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.5610582782506858, "volume": 151.05600000000004, "diffusion": 5.824, "adsorption": -49.902, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5656681611257768, "volume": 146.20800000000003, "diffusion": 5.244, "adsorption": -55.533, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5692971587748638, "volume": 127.40000000000003, "diffusion": 5.192, "adsorption": -44.364, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.5731293191344053, "volume": 139.30400000000003, "diffusion": 5.326, "adsorption": -46.251, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5865737981740062, "volume": 128.64800000000002, "diffusion": 5.878, "adsorption": -46.175, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6005454951490714, "volume": 132.16800000000003, "diffusion": 4.566, "adsorption": -46.611, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6016332919514404, "volume": 129.15200000000004, "diffusion": 6.521, "adsorption": -48.095, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.6019158470199046, "volume": 126.34400000000004, "diffusion": 5.675, "adsorption": -44.893, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6048289914754865, "volume": 135.56800000000004, "diffusion": 3.97, "adsorption": -49.01, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.6129383806682285, "volume": 130.24000000000004, "diffusion": 5.769, "adsorption": -45.614, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.6144188598807393, "volume": 145.97600000000003, "diffusion": 5.878, "adsorption": -48.379, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6165533145923641, "volume": 146.50400000000005, "diffusion": 4.663, "adsorption": -48.238, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.636652152594338, "volume": 145.90400000000002, "diffusion": 5.734, "adsorption": -47.517, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.6391026071389042, "volume": 130.18400000000003, "diffusion": 6.347, "adsorption": -47.045, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.6393325753729575, "volume": 140.16800000000003, "diffusion": 4.67, "adsorption": -55.867, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.6451392622722188, "volume": 128.85600000000002, "diffusion": 5.759, "adsorption": -57.896, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.6726762442313308, "volume": 126.43200000000003, "diffusion": 4.987, "adsorption": -59.598, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6796342175178645, "volume": 143.66400000000004, "diffusion": 5.08, "adsorption": -45.357, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.695088928345865, "volume": 122.77600000000002, "diffusion": 4.38, "adsorption": -44.147, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.6985955168717886, "volume": 137.66400000000004, "diffusion": 7.082, "adsorption": -49.584, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.7098223491756185, "volume": 121.08800000000002, "diffusion": 5.139, "adsorption": -61.557, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.7187284967172075, "volume": 136.24000000000004, "diffusion": 5.659, "adsorption": -44.372, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.7264191699422411, "volume": 146.61600000000004, "diffusion": 5.992, "adsorption": -55.779, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.7313310627489246, "volume": 154.22400000000005, "diffusion": 5.011, "adsorption": -56.381, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7370021160456552, "volume": 139.20000000000005, "diffusion": 7.168, "adsorption": -49.445, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7410284306811157, "volume": 155.00000000000003, "diffusion": 5.237, "adsorption": -45.547, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.746086263438411, "volume": 128.51200000000003, "diffusion": 5.73, "adsorption": -59.772, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.7579561923575289, "volume": 155.69600000000003, "diffusion": 5.541, "adsorption": -46.325, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7593501542891059, "volume": 150.41600000000003, "diffusion": 5.856, "adsorption": -46.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7734106829113859, "volume": 146.96000000000004, "diffusion": 5.91, "adsorption": -45.914, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7777809841282889, "volume": 141.03200000000004, "diffusion": 6.429, "adsorption": -46.617, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7872915106158596, "volume": 134.00000000000003, "diffusion": 5.754, "adsorption": -43.162, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7996614782913933, "volume": 131.46400000000003, "diffusion": 5.807, "adsorption": -42.731, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.8079757579981695, "volume": 155.86400000000003, "diffusion": 5.798, "adsorption": -46.334, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.8198492822289856, "volume": 143.21600000000004, "diffusion": 6.814, "adsorption": -47.492, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8249251101070169, "volume": 131.12000000000003, "diffusion": 6.314, "adsorption": -43.901, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.8338968708168454, "volume": 151.18400000000003, "diffusion": 5.255, "adsorption": -43.393, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.8375707816460365, "volume": 130.68800000000005, "diffusion": 6.541, "adsorption": -44.359, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.8403475173748954, "volume": 137.14400000000003, "diffusion": 5.889, "adsorption": -43.181, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8494231163653163, "volume": 138.60800000000003, "diffusion": 5.145, "adsorption": -41.456, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8499799099324903, "volume": 155.88800000000003, "diffusion": 5.453, "adsorption": -44.492, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8523665367455936, "volume": 133.90400000000002, "diffusion": 6.175, "adsorption": -43.408, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8572502690985289, "volume": 133.20800000000003, "diffusion": 5.427, "adsorption": -40.774, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.8664626001897773, "volume": 146.56000000000003, "diffusion": 6.711, "adsorption": -46.875, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8770234519193898, "volume": 136.44800000000004, "diffusion": 5.571, "adsorption": -41.407, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.884113252323269, "volume": 144.28800000000004, "diffusion": 5.396, "adsorption": -60.785, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.9319627190957404, "volume": 139.53600000000003, "diffusion": 4.545, "adsorption": -42.146, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9355940137283372, "volume": 136.45600000000005, "diffusion": 5.693, "adsorption": -40.804, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.95951099981713, "volume": 157.43200000000004, "diffusion": 5.352, "adsorption": -42.526, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9885752986656515, "volume": 134.52800000000002, "diffusion": 5.995, "adsorption": -40.585, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0453319953864306, "volume": 147.50400000000005, "diffusion": 5.736, "adsorption": -40.775, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.0472340412572554, "volume": 151.06400000000005, "diffusion": 5.31, "adsorption": -39.902, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 1.0686234000864365, "volume": 123.35200000000003, "diffusion": 5.932, "adsorption": -65.432, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.1613185169292755, "volume": 155.28800000000004, "diffusion": 5.598, "adsorption": -39.543, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.2178276110602653, "volume": 136.28800000000004, "diffusion": 5.371, "adsorption": -34.895, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.2347688501266594, "volume": 155.11200000000002, "diffusion": 6.338, "adsorption": -40.675, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.272954921352539, "volume": 146.40800000000004, "diffusion": 6.32, "adsorption": -38.616, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2932646683397686, "volume": 147.51200000000003, "diffusion": 6.839, "adsorption": -40.134, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.293500122364419, "volume": 157.39200000000002, "diffusion": 5.584, "adsorption": -37.56, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.18894224803412735, "volume": 110.23200000000003, "diffusion": 4.885, "adsorption": -49.353, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.28121453485795667, "volume": 130.88000000000002, "diffusion": 5.173, "adsorption": -49.896, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2891105401507667, "volume": 126.08800000000004, "diffusion": 4.87, "adsorption": -52.703, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.336402316378456, "volume": 133.17600000000004, "diffusion": 5.298, "adsorption": -53.448, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.4018113122954136, "volume": 126.34400000000004, "diffusion": 4.954, "adsorption": -54.867, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.18124740759972843, "volume": 110.45600000000003, "diffusion": 5.081, "adsorption": -48.81, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.29872084273664784, "volume": 126.99200000000003, "diffusion": 5.003, "adsorption": -53.162, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3412442201970992, "volume": 132.63200000000003, "diffusion": 5.253, "adsorption": -48.95, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.40481577775878375, "volume": 132.67200000000003, "diffusion": 5.449, "adsorption": -54.204, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7500641053444723, "volume": 126.92000000000003, "diffusion": 4.331, "adsorption": -44.007, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.0041208791208790195, "volume": 110.20000000000003, "diffusion": 5.242, "adsorption": -51.356, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.29685678318905057, "volume": 126.31200000000003, "diffusion": 4.889, "adsorption": -52.863, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3596818701086778, "volume": 132.96800000000002, "diffusion": 5.406, "adsorption": -49.186, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3947246669557217, "volume": 126.60800000000003, "diffusion": 4.932, "adsorption": -54.633, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4256825669162412, "volume": 132.21600000000004, "diffusion": 5.561, "adsorption": -54.266, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07528360742196041, "volume": 110.48800000000003, "diffusion": 5.546, "adsorption": -51.626, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.26448981343487943, "volume": 126.42400000000004, "diffusion": 4.883, "adsorption": -52.272, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2919847984502781, "volume": 132.24800000000002, "diffusion": 5.046, "adsorption": -50.338, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3210187366795563, "volume": 133.09600000000003, "diffusion": 5.268, "adsorption": -49.417, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3805004150006116, "volume": 126.76000000000003, "diffusion": 4.917, "adsorption": -54.317, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.1838509281221134, "volume": 111.04800000000003, "diffusion": 4.946, "adsorption": -49.236, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2814039118324421, "volume": 132.46400000000003, "diffusion": 5.033, "adsorption": -50.595, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3094205877325287, "volume": 126.70400000000004, "diffusion": 4.989, "adsorption": -53.344, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3193081189136358, "volume": 132.77600000000004, "diffusion": 5.17, "adsorption": -49.547, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7474614448470751, "volume": 126.63200000000003, "diffusion": 4.331, "adsorption": -44.007, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.09888920028391784, "volume": 109.67200000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.29342070855670255, "volume": 132.25600000000003, "diffusion": 5.056, "adsorption": -50.282, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.29663989481426745, "volume": 126.28800000000003, "diffusion": 4.889, "adsorption": -52.863, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.3003007118388704, "volume": 132.99200000000002, "diffusion": 5.173, "adsorption": -49.896, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.3291541706381106, "volume": 126.04000000000003, "diffusion": 4.923, "adsorption": -53.569, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.65600000000002, "diffusion": 5.242, "adsorption": -51.356}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.09888920028391784, "volume": 109.67200000000003, "diffusion": 5.553, "adsorption": -51.881, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 6, "score": 0.3291541706381106, "volume": 126.04000000000003, "diffusion": 4.923, "adsorption": -53.569, "is_selected": false, "rank": 5}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.29663989481426745, "volume": 126.28800000000003, "diffusion": 4.889, "adsorption": -52.863, "is_selected": false, "rank": 3}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.29342070855670255, "volume": 132.25600000000003, "diffusion": 5.056, "adsorption": -50.282, "is_selected": false, "rank": 2}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 6, "score": 0.3003007118388704, "volume": 132.99200000000002, "diffusion": 5.173, "adsorption": -49.896, "is_selected": false, "rank": 4}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0, "score": 0.30058442794364537, "volume": 126.38400000000003, "diffusion": 5.165, "adsorption": -48.895, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.3045935579518058, "volume": 129.72000000000003, "diffusion": 5.178, "adsorption": -53.412, "is_selected": false, "rank": 7}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3051840470134347, "volume": 129.68000000000004, "diffusion": 5.612, "adsorption": -50.283, "is_selected": false, "rank": 8}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3090142716722333, "volume": 118.82400000000003, "diffusion": 5.804, "adsorption": -49.165, "is_selected": false, "rank": 9}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.31155561425373596, "volume": 134.77600000000004, "diffusion": 4.986, "adsorption": -50.59, "is_selected": false, "rank": 10}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3125437134369976, "volume": 119.46400000000003, "diffusion": 5.256, "adsorption": -47.414, "is_selected": false, "rank": 11}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.31413221451893053, "volume": 126.36800000000002, "diffusion": 5.727, "adsorption": -49.993, "is_selected": false, "rank": 12}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.328484359828056, "volume": 127.55200000000004, "diffusion": 4.612, "adsorption": -52.308, "is_selected": false, "rank": 13}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.34885205363165805, "volume": 122.26400000000002, "diffusion": 5.472, "adsorption": -54.781, "is_selected": false, "rank": 14}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.3499399924180279, "volume": 137.37600000000003, "diffusion": 5.186, "adsorption": -53.03, "is_selected": false, "rank": 15}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.36293676925895935, "volume": 129.20000000000002, "diffusion": 6.244, "adsorption": -51.428, "is_selected": false, "rank": 16}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0, "score": 0.3658742869478817, "volume": 121.58400000000003, "diffusion": 4.671, "adsorption": -48.648, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.3760262534454094, "volume": 137.53600000000003, "diffusion": 5.557, "adsorption": -52.606, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.3795120140154124, "volume": 127.92000000000003, "diffusion": 5.212, "adsorption": -55.084, "is_selected": false, "rank": 19}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3832195642290812, "volume": 125.77600000000002, "diffusion": 6.063, "adsorption": -49.816, "is_selected": false, "rank": 20}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.39205269662293335, "volume": 127.64800000000002, "diffusion": 4.095, "adsorption": -51.693, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.3924834621098661, "volume": 122.84000000000003, "diffusion": 5.603, "adsorption": -55.011, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.401523985461964, "volume": 128.89600000000004, "diffusion": 5.239, "adsorption": -55.398, "is_selected": false, "rank": 23}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.40881362519077397, "volume": 134.24800000000002, "diffusion": 4.66, "adsorption": -49.908, "is_selected": false, "rank": 24}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4175103198565414, "volume": 138.38400000000004, "diffusion": 4.69, "adsorption": -50.301, "is_selected": false, "rank": 25}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.4214041881754849, "volume": 142.38400000000004, "diffusion": 5.944, "adsorption": -51.343, "is_selected": false, "rank": 26}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4267128580155567, "volume": 146.00000000000003, "diffusion": 5.464, "adsorption": -50.244, "is_selected": false, "rank": 27}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.42828153396480284, "volume": 139.86400000000003, "diffusion": 5.965, "adsorption": -50.904, "is_selected": false, "rank": 28}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4300588087202711, "volume": 131.40000000000003, "diffusion": 5.009, "adsorption": -54.748, "is_selected": false, "rank": 29}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4301975847350141, "volume": 118.77600000000002, "diffusion": 5.815, "adsorption": -47.119, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4304481613641168, "volume": 127.40000000000003, "diffusion": 4.947, "adsorption": -47.541, "is_selected": false, "rank": 31}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4437385886137609, "volume": 150.61600000000004, "diffusion": 5.375, "adsorption": -50.376, "is_selected": false, "rank": 32}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.453706319372139, "volume": 138.95200000000003, "diffusion": 5.3, "adsorption": -48.156, "is_selected": false, "rank": 33}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.47520509465835875, "volume": 133.73600000000002, "diffusion": 5.951, "adsorption": -49.107, "is_selected": false, "rank": 34}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.47549147850603946, "volume": 134.85600000000002, "diffusion": 5.242, "adsorption": -55.752, "is_selected": false, "rank": 35}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.478628774934781, "volume": 122.80000000000003, "diffusion": 4.761, "adsorption": -56.1, "is_selected": false, "rank": 36}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4854474439387778, "volume": 131.69600000000003, "diffusion": 5.064, "adsorption": -55.83, "is_selected": false, "rank": 37}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.49122435298311196, "volume": 139.59200000000004, "diffusion": 5.922, "adsorption": -49.644, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.49774757467771313, "volume": 136.17600000000004, "diffusion": 5.388, "adsorption": -47.26, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4993413771008058, "volume": 155.05600000000004, "diffusion": 5.079, "adsorption": -50.209, "is_selected": false, "rank": 40}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.503160957443282, "volume": 128.64000000000004, "diffusion": 5.064, "adsorption": -56.606, "is_selected": false, "rank": 41}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.508927307469544, "volume": 146.13600000000002, "diffusion": 5.838, "adsorption": -50.079, "is_selected": false, "rank": 42}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5137232030107168, "volume": 119.99200000000003, "diffusion": 6.356, "adsorption": -47.644, "is_selected": false, "rank": 43}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.5241555092047807, "volume": 135.08000000000004, "diffusion": 5.635, "adsorption": -55.267, "is_selected": false, "rank": 44}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.5274249765498895, "volume": 146.53600000000003, "diffusion": 5.091, "adsorption": -48.371, "is_selected": false, "rank": 45}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5311006588877275, "volume": 118.93600000000002, "diffusion": 6.043, "adsorption": -46.161, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5411136112882132, "volume": 145.06400000000002, "diffusion": 5.811, "adsorption": -53.438, "is_selected": false, "rank": 47}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5471030133284213, "volume": 128.97600000000003, "diffusion": 4.442, "adsorption": -47.437, "is_selected": false, "rank": 48}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5563532381959171, "volume": 139.76000000000002, "diffusion": 6.396, "adsorption": -50.103, "is_selected": false, "rank": 49}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5567963586369697, "volume": 142.52000000000004, "diffusion": 5.038, "adsorption": -55.292, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5610582782506858, "volume": 151.05600000000004, "diffusion": 5.824, "adsorption": -49.902, "is_selected": false, "rank": 51}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5656681611257768, "volume": 146.20800000000003, "diffusion": 5.244, "adsorption": -55.533, "is_selected": false, "rank": 52}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5692971587748638, "volume": 127.40000000000003, "diffusion": 5.192, "adsorption": -44.364, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.5731293191344053, "volume": 139.30400000000003, "diffusion": 5.326, "adsorption": -46.251, "is_selected": false, "rank": 54}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5865737981740062, "volume": 128.64800000000002, "diffusion": 5.878, "adsorption": -46.175, "is_selected": false, "rank": 55}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6005454951490714, "volume": 132.16800000000003, "diffusion": 4.566, "adsorption": -46.611, "is_selected": false, "rank": 56}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6016332919514404, "volume": 129.15200000000004, "diffusion": 6.521, "adsorption": -48.095, "is_selected": false, "rank": 57}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.6019158470199046, "volume": 126.34400000000004, "diffusion": 5.675, "adsorption": -44.893, "is_selected": false, "rank": 58}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6048289914754865, "volume": 135.56800000000004, "diffusion": 3.97, "adsorption": -49.01, "is_selected": false, "rank": 59}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.6129383806682285, "volume": 130.24000000000004, "diffusion": 5.769, "adsorption": -45.614, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.6144188598807393, "volume": 145.97600000000003, "diffusion": 5.878, "adsorption": -48.379, "is_selected": false, "rank": 61}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6165533145923641, "volume": 146.50400000000005, "diffusion": 4.663, "adsorption": -48.238, "is_selected": false, "rank": 62}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.636652152594338, "volume": 145.90400000000002, "diffusion": 5.734, "adsorption": -47.517, "is_selected": false, "rank": 63}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.6391026071389042, "volume": 130.18400000000003, "diffusion": 6.347, "adsorption": -47.045, "is_selected": false, "rank": 64}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.6393325753729575, "volume": 140.16800000000003, "diffusion": 4.67, "adsorption": -55.867, "is_selected": false, "rank": 65}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.6451392622722188, "volume": 128.85600000000002, "diffusion": 5.759, "adsorption": -57.896, "is_selected": false, "rank": 66}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.6726762442313308, "volume": 126.43200000000003, "diffusion": 4.987, "adsorption": -59.598, "is_selected": false, "rank": 67}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6796342175178645, "volume": 143.66400000000004, "diffusion": 5.08, "adsorption": -45.357, "is_selected": false, "rank": 68}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.695088928345865, "volume": 122.77600000000002, "diffusion": 4.38, "adsorption": -44.147, "is_selected": false, "rank": 69}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6985955168717886, "volume": 137.66400000000004, "diffusion": 7.082, "adsorption": -49.584, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.7098223491756185, "volume": 121.08800000000002, "diffusion": 5.139, "adsorption": -61.557, "is_selected": false, "rank": 71}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7187284967172075, "volume": 136.24000000000004, "diffusion": 5.659, "adsorption": -44.372, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7264191699422411, "volume": 146.61600000000004, "diffusion": 5.992, "adsorption": -55.779, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7313310627489246, "volume": 154.22400000000005, "diffusion": 5.011, "adsorption": -56.381, "is_selected": false, "rank": 74}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7370021160456552, "volume": 139.20000000000005, "diffusion": 7.168, "adsorption": -49.445, "is_selected": false, "rank": 75}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7410284306811157, "volume": 155.00000000000003, "diffusion": 5.237, "adsorption": -45.547, "is_selected": false, "rank": 76}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.746086263438411, "volume": 128.51200000000003, "diffusion": 5.73, "adsorption": -59.772, "is_selected": false, "rank": 77}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.7579561923575289, "volume": 155.69600000000003, "diffusion": 5.541, "adsorption": -46.325, "is_selected": false, "rank": 78}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7593501542891059, "volume": 150.41600000000003, "diffusion": 5.856, "adsorption": -46.513, "is_selected": false, "rank": 79}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7734106829113859, "volume": 146.96000000000004, "diffusion": 5.91, "adsorption": -45.914, "is_selected": false, "rank": 80}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7777809841282889, "volume": 141.03200000000004, "diffusion": 6.429, "adsorption": -46.617, "is_selected": false, "rank": 81}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7872915106158596, "volume": 134.00000000000003, "diffusion": 5.754, "adsorption": -43.162, "is_selected": false, "rank": 82}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7996614782913933, "volume": 131.46400000000003, "diffusion": 5.807, "adsorption": -42.731, "is_selected": false, "rank": 83}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.8079757579981695, "volume": 155.86400000000003, "diffusion": 5.798, "adsorption": -46.334, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.8198492822289856, "volume": 143.21600000000004, "diffusion": 6.814, "adsorption": -47.492, "is_selected": false, "rank": 85}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8249251101070169, "volume": 131.12000000000003, "diffusion": 6.314, "adsorption": -43.901, "is_selected": false, "rank": 86}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.8338968708168454, "volume": 151.18400000000003, "diffusion": 5.255, "adsorption": -43.393, "is_selected": false, "rank": 87}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.8375707816460365, "volume": 130.68800000000005, "diffusion": 6.541, "adsorption": -44.359, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.8403475173748954, "volume": 137.14400000000003, "diffusion": 5.889, "adsorption": -43.181, "is_selected": false, "rank": 89}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8494231163653163, "volume": 138.60800000000003, "diffusion": 5.145, "adsorption": -41.456, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8499799099324903, "volume": 155.88800000000003, "diffusion": 5.453, "adsorption": -44.492, "is_selected": false, "rank": 91}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8523665367455936, "volume": 133.90400000000002, "diffusion": 6.175, "adsorption": -43.408, "is_selected": false, "rank": 92}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8572502690985289, "volume": 133.20800000000003, "diffusion": 5.427, "adsorption": -40.774, "is_selected": false, "rank": 93}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8664626001897773, "volume": 146.56000000000003, "diffusion": 6.711, "adsorption": -46.875, "is_selected": false, "rank": 94}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8770234519193898, "volume": 136.44800000000004, "diffusion": 5.571, "adsorption": -41.407, "is_selected": false, "rank": 95}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.884113252323269, "volume": 144.28800000000004, "diffusion": 5.396, "adsorption": -60.785, "is_selected": false, "rank": 96}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.9319627190957404, "volume": 139.53600000000003, "diffusion": 4.545, "adsorption": -42.146, "is_selected": false, "rank": 97}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9355940137283372, "volume": 136.45600000000005, "diffusion": 5.693, "adsorption": -40.804, "is_selected": false, "rank": 98}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.95951099981713, "volume": 157.43200000000004, "diffusion": 5.352, "adsorption": -42.526, "is_selected": false, "rank": 99}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9885752986656515, "volume": 134.52800000000002, "diffusion": 5.995, "adsorption": -40.585, "is_selected": false, "rank": 100}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0453319953864306, "volume": 147.50400000000005, "diffusion": 5.736, "adsorption": -40.775, "is_selected": false, "rank": 101}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.0472340412572554, "volume": 151.06400000000005, "diffusion": 5.31, "adsorption": -39.902, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0686234000864365, "volume": 123.35200000000003, "diffusion": 5.932, "adsorption": -65.432, "is_selected": false, "rank": 103}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.1613185169292755, "volume": 155.28800000000004, "diffusion": 5.598, "adsorption": -39.543, "is_selected": false, "rank": 104}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.2178276110602653, "volume": 136.28800000000004, "diffusion": 5.371, "adsorption": -34.895, "is_selected": false, "rank": 105}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.2347688501266594, "volume": 155.11200000000002, "diffusion": 6.338, "adsorption": -40.675, "is_selected": false, "rank": 106}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.272954921352539, "volume": 146.40800000000004, "diffusion": 6.32, "adsorption": -38.616, "is_selected": false, "rank": 107}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2932646683397686, "volume": 147.51200000000003, "diffusion": 6.839, "adsorption": -40.134, "is_selected": false, "rank": 108}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.293500122364419, "volume": 157.39200000000002, "diffusion": 5.584, "adsorption": -37.56, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770966902.1794622}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "timestamp": 1770966902.2998765}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/345d27e5-8ff2-495d-afc1-9b06b22c0d3e.log", "timestamp": 1770966902.3703623}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966902.7366564}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6560, 扩散: 5.2420, 吸附: -51.3560", "timestamp": 1770966902.7677999}, {"type": "log", "level": "info", "message": "远程日志已启动: 345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "timestamp": 1770966902.8112624}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770966902.8605711}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966902.951998}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770966903.0423622}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770966903.136722}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.66, 扩散: 5.2420, 吸附: -51.3560", "timestamp": 1770966903.2394512}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770966903.3339636}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966903.4254515}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966903.4448109}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6560, 扩散: 5.2420, 吸附: -51.3560", "timestamp": 1770966903.4620876}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770966903.4818013}, {"type": "log", "level": "info", "message": "远程日志已启动: 345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "timestamp": 1770966903.5040374}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770966903.564332}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770966903.6327631}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770966903.6482804}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966903.7352629}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966903.8225183}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770966903.9060469}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966903.9826484}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770966903.9994934}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770966904.108187}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770966904.1897566}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.03s, 速度: 5349.36 mol/s", "timestamp": 1770966904.2111402}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.03215336799621582, "speed": 5349.3618466420985, "timestamp": 1770966904.2302403}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770966904.3443923}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770966904.4241016}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770966904.5031571}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770966904.5220742}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770966904.5397923}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770966904.618371}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770966914.5722728}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0145", "timestamp": 1770966914.590847}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0145169680039118, "timestamp": 1770966914.6075516}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966914.6850073}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966914.7043684}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]"], "timestamp": 1770966914.7274008}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966914.8106635}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0145", "timestamp": 1770966914.8958797}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.36", "timestamp": 1770966914.9728143}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2280", "timestamp": 1770966915.0484712}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.5130", "timestamp": 1770966915.1273122}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 11.15s) ==========", "timestamp": 1770966915.1536536}, {"type": "layer_end", "layer": 0, "duration": 11.154185771942139, "timestamp": 1770966915.1755192}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966915.2542238}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966915.3571417}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770966915.445632}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966915.5301435}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770966915.5517886}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770966915.6495388}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770966915.7325742}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 21126.45 mol/s", "timestamp": 1770966915.7534916}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0014200210571289062, "speed": 21126.447280053726, "timestamp": 1770966915.7715452}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770966915.8627374}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770966915.8886936}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770966915.909138}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770966915.9932296}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966917.9217877}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1889", "timestamp": 1770966917.9419785}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18894224803412735, "timestamp": 1770966917.955671}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966918.0391815}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966918.0587723}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]"], "timestamp": 1770966918.0824823}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966918.178432}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1889", "timestamp": 1770966918.273699}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.23", "timestamp": 1770966918.3761032}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.8850", "timestamp": 1770966918.488046}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.3530", "timestamp": 1770966918.5826814}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 3.05s) ==========", "timestamp": 1770966918.6067438}, {"type": "layer_end", "layer": 1, "duration": 3.0547873973846436, "timestamp": 1770966918.6262708}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966918.7219179}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966918.8425136}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770966918.9571085}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966919.0445175}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770966919.0693734}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770966919.1743214}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770966919.2935455}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 14200.65 mol/s", "timestamp": 1770966919.3489602}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0017604827880859375, "speed": 14200.650054171181, "timestamp": 1770966919.37935}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770966919.5474703}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770966919.5729802}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770966919.601487}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770966919.7433794}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966921.4661}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1812", "timestamp": 1770966921.4881728}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.18124740759972843, "timestamp": 1770966921.5136697}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966921.6024985}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966921.624245}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770966921.647402}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966921.7419138}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1812", "timestamp": 1770966921.8791769}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.46", "timestamp": 1770966922.0130243}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0810", "timestamp": 1770966922.1266694}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.8100", "timestamp": 1770966922.2891016}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 3.26s) ==========", "timestamp": 1770966922.3260994}, {"type": "layer_end", "layer": 2, "duration": 3.2560107707977295, "timestamp": 1770966922.3496976}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966922.5579333}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966922.7247033}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770966922.8631382}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966922.942236}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770966922.961254}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770966923.0298977}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770966923.119387}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 33288.13 mol/s", "timestamp": 1770966923.1442533}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.0006008148193359375, "speed": 33288.12698412698, "timestamp": 1770966923.1659534}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770966923.2497828}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770966923.2719932}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770966923.290366}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770966923.37459}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966924.7377193}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0041", "timestamp": 1770966924.7568269}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.0041208791208790195, "timestamp": 1770966924.777097}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966924.8682756}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966924.887079}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]"], "timestamp": 1770966924.9081948}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966924.9867423}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0041", "timestamp": 1770966925.0603814}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.20", "timestamp": 1770966925.133889}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770966925.2296073}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770966925.312242}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 2.38s) ==========", "timestamp": 1770966925.3370733}, {"type": "layer_end", "layer": 3, "duration": 2.375541925430298, "timestamp": 1770966925.3587344}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966925.4375606}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966925.5108793}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770966925.584216}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966925.6540337}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770966925.672436}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770966925.7606745}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770966925.8340774}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 29289.83 mol/s", "timestamp": 1770966925.854274}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0005121231079101562, "speed": 29289.832402234635, "timestamp": 1770966925.8716931}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770966925.963551}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770966925.981015}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770966925.9974241}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770966926.0825822}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966927.0827525}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0753", "timestamp": 1770966927.104604}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07528360742196041, "timestamp": 1770966927.1273594}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966927.2466626}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966927.269248}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]"], "timestamp": 1770966927.294587}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966927.4043367}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0753", "timestamp": 1770966927.61294}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.49", "timestamp": 1770966927.7044451}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5460", "timestamp": 1770966927.8025568}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.6260", "timestamp": 1770966927.88543}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 2.24s) ==========", "timestamp": 1770966927.9083967}, {"type": "layer_end", "layer": 4, "duration": 2.235617160797119, "timestamp": 1770966927.9287987}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966928.0151472}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966928.0905747}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770966928.162}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966928.24572}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770966928.26602}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770966928.358612}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770966928.4461174}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 22795.13 mol/s", "timestamp": 1770966928.4632764}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.000438690185546875, "speed": 22795.130434782608, "timestamp": 1770966928.4840536}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770966928.5666754}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770966928.5880013}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770966928.6097863}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770966928.6908927}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966929.4575076}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1839", "timestamp": 1770966929.476098}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.1838509281221134, "timestamp": 1770966929.4973214}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966929.5808606}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966929.600459}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770966929.6237533}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966929.7152786}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1839", "timestamp": 1770966929.826887}, {"type": "log", "level": "info", "message": "INFO -     体积: 111.05", "timestamp": 1770966929.9155276}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9460", "timestamp": 1770966930.0091102}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.2360", "timestamp": 1770966930.0962732}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.85s) ==========", "timestamp": 1770966930.1185217}, {"type": "layer_end", "layer": 5, "duration": 1.8519976139068604, "timestamp": 1770966930.137699}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966930.221411}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966930.3099465}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770966930.4080985}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966930.4900203}, {"type": "log", "level": "info", "message": "========== 第 7 层开始 ==========", "timestamp": 1770966930.5075135}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770966930.5899968}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770966930.6652718}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 26412.49 mol/s", "timestamp": 1770966930.6874633}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.00018930435180664062, "speed": 26412.493702770782, "timestamp": 1770966930.7072213}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770966930.7964466}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770966930.8207996}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770966930.8448813}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770966930.9311068}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770966931.45978}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0989", "timestamp": 1770966931.480984}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.09888920028391784, "timestamp": 1770966931.5045903}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770966931.5895703}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770966931.6099215}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]"], "timestamp": 1770966931.6324375}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966931.7300806}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0989", "timestamp": 1770966931.8330395}, {"type": "log", "level": "info", "message": "INFO -     体积: 109.67", "timestamp": 1770966931.9159672}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770966931.986906}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770966932.1043723}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.62s) ==========", "timestamp": 1770966932.1243765}, {"type": "layer_end", "layer": 6, "duration": 1.6164348125457764, "timestamp": 1770966932.1408696}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770966932.629597}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966932.7138393}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770966932.7948477}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 28.49 秒", "timestamp": 1770966932.9232492}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770966933.0159948}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770966933.0919743}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770966933.15913}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966933.2327507}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.0026, 体积: 110.37, 扩散: 5.2420, 吸附: -51.3560", "timestamp": 1770966933.3114088}, {"type": "log", "level": "info", "message": "INFO -   2. [H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "timestamp": 1770966933.4054391}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2885, 体积: 131.90, 扩散: 5.4690, 吸附: -52.2680", "timestamp": 1770966933.5030518}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "timestamp": 1770966933.6014857}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3093, 体积: 126.06, 扩散: 4.9830, 吸附: -53.4210", "timestamp": 1770966933.6946783}, {"type": "log", "level": "info", "message": "INFO -   4. [H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "timestamp": 1770966933.7833467}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3189, 体积: 132.10, 扩散: 5.3540, 吸附: -49.5810", "timestamp": 1770966933.8691046}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770966933.9709265}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.6233, 体积: 126.92, 扩散: 4.2530, 吸附: -46.4320", "timestamp": 1770966934.0556118}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 31.90s", "timestamp": 1770966934.0735464}, {"type": "finish", "total_results": 5, "duration": 31.89864993095398, "timestamp": 1770966934.0910106}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/345d27e5-8ff2-495d-afc1-9b06b22c0d3e.json", "timestamp": 1770966934.1179802}, {"type": "log", "level": "info", "message": "远程日志已上传: 345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "timestamp": 1770966934.161779}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770966934.2493773}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770966934.3445375}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770966934.3629436}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.6560, 扩散: 5.2420, 吸附: -51.3560", "timestamp": 1770966934.3816638}, {"type": "log", "level": "info", "message": "远程日志已启动: 345d27e5-8ff2-495d-afc1-9b06b22c0d3e", "timestamp": 1770966934.4627385}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770966934.504625}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 32.36s", "timestamp": 1770966934.5317376}, {"type": "finish", "total_results": 5, "duration": 32.356695890426636, "timestamp": 1770966934.5603347}]}