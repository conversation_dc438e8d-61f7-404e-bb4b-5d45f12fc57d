{"metadata": {"log_name": "81814875-328e-4243-a436-f91efc3f0908", "start_time": 1770944533.9518228, "end_time": 1770944558.4069328, "duration": 24.4551100730896, "input_smiles": "SC1NCCCN1", "orig_properties": {"volume": 110.03200000000002, "diffusion": 5.213, "adsorption": -51.696}, "config": {"top_num": 5, "top_num_last": 10}}, "layers": [{"layer_num": 0, "start_time": 1770944535.2074835, "end_time": 1770944545.8205974, "duration": 10.61311388015747, "total_generated": 172, "unique_mols": 172, "filtered_mols": 158, "scored_mols": 109, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07821129155555415}, {"layer_num": 1, "start_time": 1770944545.9785106, "end_time": 1770944548.3011394, "duration": 2.3226287364959717, "total_generated": 30, "unique_mols": 30, "filtered_mols": 30, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07857482216483137}, {"layer_num": 2, "start_time": 1770944548.485899, "end_time": 1770944550.5657218, "duration": 2.0798227787017822, "total_generated": 25, "unique_mols": 25, "filtered_mols": 25, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.029995913789251965}, {"layer_num": 3, "start_time": 1770944550.760302, "end_time": 1770944552.5432396, "duration": 1.7829375267028809, "total_generated": 20, "unique_mols": 20, "filtered_mols": 20, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20142286785516123}, {"layer_num": 4, "start_time": 1770944552.7430594, "end_time": 1770944554.308284, "duration": 1.5652246475219727, "total_generated": 15, "unique_mols": 15, "filtered_mols": 15, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.028929055149885072}, {"layer_num": 5, "start_time": 1770944554.5156305, "end_time": 1770944555.830621, "duration": 1.314990520477295, "total_generated": 10, "unique_mols": 10, "filtered_mols": 10, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.19681877906055686}, {"layer_num": 6, "start_time": 1770944556.0169873, "end_time": 1770944557.0164645, "duration": 0.9994771480560303, "total_generated": 5, "unique_mols": 5, "filtered_mols": 5, "scored_mols": 5, "top_n": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.02905073420513099}], "candidates": {"0": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07821129155555415, "volume": 110.28000000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.18936282213094224, "volume": 122.91200000000003, "diffusion": 5.056, "adsorption": -52.423, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.2488831032822292, "volume": 127.52800000000003, "diffusion": 5.235, "adsorption": -50.22, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.25733973375436603, "volume": 131.16800000000003, "diffusion": 5.143, "adsorption": -52.589, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2723372062243398, "volume": 126.32800000000003, "diffusion": 4.87, "adsorption": -52.703, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": true, "rank": 5}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "score": 0.27672002141169433, "volume": 137.32800000000003, "diffusion": 5.274, "adsorption": -51.988, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 6}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "score": 0.2820109180720762, "volume": 122.91200000000003, "diffusion": 4.606, "adsorption": -52.532, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 7}, {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.2904521872532988, "volume": 131.80000000000004, "diffusion": 4.925, "adsorption": -51.052, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 8}, {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2913280775086811, "volume": 118.87200000000003, "diffusion": 5.955, "adsorption": -50.513, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 9}, {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.29232956661861476, "volume": 119.16800000000003, "diffusion": 5.382, "adsorption": -48.648, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 10}, {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.2966700820010382, "volume": 129.56000000000003, "diffusion": 5.743, "adsorption": -51.998, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 11}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "score": 0.3122197851489951, "volume": 127.25600000000003, "diffusion": 4.615, "adsorption": -52.402, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 12}, {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.3171546118447952, "volume": 121.48800000000003, "diffusion": 4.748, "adsorption": -53.83, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 13}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "score": 0.3186912999184926, "volume": 135.06400000000002, "diffusion": 4.917, "adsorption": -51.103, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 14}, {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.3269156039678503, "volume": 126.69600000000003, "diffusion": 5.621, "adsorption": -50.021, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 15}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "score": 0.33810173036320895, "volume": 127.97600000000003, "diffusion": 5.02, "adsorption": -54.074, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 16}, {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.34257020070953925, "volume": 133.09600000000003, "diffusion": 5.17, "adsorption": -49.547, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 17}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "score": 0.34527386320459075, "volume": 138.05600000000004, "diffusion": 5.383, "adsorption": -52.695, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 18}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "score": 0.3748142554688088, "volume": 123.21600000000002, "diffusion": 5.619, "adsorption": -54.748, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 19}, {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.37560598468488354, "volume": 144.13600000000002, "diffusion": 5.025, "adsorption": -52.206, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 20}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "score": 0.39718908448538326, "volume": 134.40000000000003, "diffusion": 5.085, "adsorption": -54.301, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 21}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "score": 0.40153897750416717, "volume": 129.07200000000003, "diffusion": 4.75, "adsorption": -49.289, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 22}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4098719988402465, "volume": 126.56800000000003, "diffusion": 4.993, "adsorption": -47.95, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 23}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "score": 0.4150102254939805, "volume": 126.28000000000003, "diffusion": 4.752, "adsorption": -48.613, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 24}, {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.4221359694560636, "volume": 128.90400000000002, "diffusion": 6.256, "adsorption": -50.825, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 25}, {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.4337992416341916, "volume": 131.16800000000003, "diffusion": 5.134, "adsorption": -55.6, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 26}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "score": 0.4429657289054881, "volume": 126.69600000000003, "diffusion": 5.545, "adsorption": -47.77, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 27}, {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.4486096210121821, "volume": 144.82400000000004, "diffusion": 5.464, "adsorption": -50.244, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 28}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4496146383202766, "volume": 139.65600000000003, "diffusion": 4.698, "adsorption": -50.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 29}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "score": 0.4595945118092918, "volume": 128.92000000000004, "diffusion": 5.5, "adsorption": -55.709, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 30}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.4644036171765398, "volume": 155.64800000000002, "diffusion": 5.011, "adsorption": -51.505, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 31}, {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.46552718272986676, "volume": 145.28000000000003, "diffusion": 5.014, "adsorption": -49.852, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 32}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "score": 0.47572582430691257, "volume": 147.02400000000003, "diffusion": 5.63, "adsorption": -50.67, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 33}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "score": 0.4815377286456288, "volume": 128.35200000000003, "diffusion": 5.073, "adsorption": -56.662, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 34}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "score": 0.49658421445278667, "volume": 142.13600000000002, "diffusion": 5.332, "adsorption": -54.832, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 35}, {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5125145280931382, "volume": 129.17600000000004, "diffusion": 5.218, "adsorption": -45.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 36}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "score": 0.5207426541309508, "volume": 139.44800000000004, "diffusion": 4.599, "adsorption": -54.033, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 37}, {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5208150992064196, "volume": 127.64800000000002, "diffusion": 5.242, "adsorption": -45.576, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 38}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "score": 0.5302218700068078, "volume": 135.93600000000004, "diffusion": 5.658, "adsorption": -55.305, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 39}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "score": 0.5318101345964882, "volume": 126.44000000000003, "diffusion": 4.763, "adsorption": -56.803, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 40}, {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.533938478907177, "volume": 133.64000000000004, "diffusion": 5.948, "adsorption": -48.622, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 41}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "score": 0.5433007152571829, "volume": 146.10400000000004, "diffusion": 5.743, "adsorption": -53.657, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 42}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "score": 0.547210764790756, "volume": 146.62400000000002, "diffusion": 5.625, "adsorption": -54.033, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 43}, {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5495652643708153, "volume": 118.61600000000003, "diffusion": 6.23, "adsorption": -46.932, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 44}, {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5546254967194846, "volume": 138.93600000000004, "diffusion": 5.729, "adsorption": -48.371, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 45}, {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5569024004558525, "volume": 133.95200000000003, "diffusion": 4.809, "adsorption": -47.181, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 46}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "score": 0.5620781264177257, "volume": 130.10400000000004, "diffusion": 6.14, "adsorption": -48.218, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 47}, {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.5623291563037797, "volume": 127.50400000000003, "diffusion": 3.944, "adsorption": -48.937, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 48}, {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.5646219275358839, "volume": 117.90400000000002, "diffusion": 5.847, "adsorption": -45.295, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 49}, {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5675379911334202, "volume": 132.41600000000003, "diffusion": 5.163, "adsorption": -45.587, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 50}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "score": 0.5785294177071334, "volume": 135.97600000000003, "diffusion": 5.832, "adsorption": -47.836, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 51}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "score": 0.594604297741982, "volume": 155.91200000000003, "diffusion": 5.177, "adsorption": -54.638, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 52}, {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.5948355425004883, "volume": 120.09600000000003, "diffusion": 6.627, "adsorption": -47.696, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 53}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "score": 0.5984170727220162, "volume": 130.12800000000004, "diffusion": 5.752, "adsorption": -46.313, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 54}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "score": 0.6036982068999014, "volume": 142.55200000000002, "diffusion": 6.378, "adsorption": -53.155, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 55}, {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.6128483845695744, "volume": 145.60000000000002, "diffusion": 5.273, "adsorption": -56.488, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 56}, {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6159717783199093, "volume": 154.20000000000005, "diffusion": 5.754, "adsorption": -49.787, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 57}, {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6561436359501764, "volume": 124.45600000000003, "diffusion": 5.847, "adsorption": -44.744, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 58}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "score": 0.6631020610472169, "volume": 137.16000000000003, "diffusion": 5.318, "adsorption": -44.865, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 59}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "score": 0.6695177299033925, "volume": 128.84800000000004, "diffusion": 5.728, "adsorption": -58.584, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 60}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "score": 0.6868065764969952, "volume": 121.62400000000002, "diffusion": 5.188, "adsorption": -61.633, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 61}, {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6896498350675557, "volume": 123.40000000000003, "diffusion": 4.282, "adsorption": -44.983, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 62}, {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6923648192948453, "volume": 135.70400000000004, "diffusion": 4.037, "adsorption": -47.673, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 63}, {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.692807148013203, "volume": 138.11200000000002, "diffusion": 5.638, "adsorption": -45.56, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 64}, {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "score": 0.6948221706881711, "volume": 145.79200000000003, "diffusion": 5.399, "adsorption": -57.454, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 65}, {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "score": 0.696601073066211, "volume": 146.75200000000004, "diffusion": 4.882, "adsorption": -46.537, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 66}, {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.6989243558334522, "volume": 128.03200000000004, "diffusion": 5.733, "adsorption": -59.202, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 67}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "score": 0.7011662175488694, "volume": 130.28800000000004, "diffusion": 6.346, "adsorption": -46.531, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 68}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "score": 0.7183357702999391, "volume": 150.94400000000005, "diffusion": 5.531, "adsorption": -46.776, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 69}, {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.7365510601652624, "volume": 137.74400000000003, "diffusion": 7.076, "adsorption": -49.502, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 70}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "score": 0.7397276327576585, "volume": 146.60000000000002, "diffusion": 6.182, "adsorption": -47.879, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 71}, {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7428413278929518, "volume": 134.10400000000004, "diffusion": 5.222, "adsorption": -42.695, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 72}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "score": 0.7487050251815144, "volume": 155.85600000000002, "diffusion": 5.685, "adsorption": -47.531, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 73}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "score": 0.7840720491566737, "volume": 145.45600000000005, "diffusion": 6.111, "adsorption": -46.701, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 74}, {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.7881394489503134, "volume": 138.73600000000005, "diffusion": 7.222, "adsorption": -49.251, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 75}, {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.7941357370962023, "volume": 130.67200000000003, "diffusion": 6.205, "adsorption": -44.523, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 76}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.8178564136426184, "volume": 135.60000000000002, "diffusion": 5.665, "adsorption": -43.101, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 77}, {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8194628878854562, "volume": 143.85600000000002, "diffusion": 4.883, "adsorption": -43.963, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 78}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "score": 0.8323440157610564, "volume": 155.97600000000003, "diffusion": 5.752, "adsorption": -46.33, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 79}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "score": 0.8435704229080144, "volume": 150.54400000000004, "diffusion": 6.299, "adsorption": -47.094, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 80}, {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.8463840049972639, "volume": 133.40800000000004, "diffusion": 6.162, "adsorption": -43.909, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 81}, {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.852252299627293, "volume": 146.84000000000003, "diffusion": 5.974, "adsorption": -45.29, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 82}, {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8537962601734341, "volume": 131.12800000000004, "diffusion": 5.821, "adsorption": -42.297, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 83}, {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8558205479433622, "volume": 138.31200000000004, "diffusion": 6.789, "adsorption": -46.587, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 84}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "score": 0.859174935717202, "volume": 151.48000000000005, "diffusion": 5.459, "adsorption": -44.195, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 85}, {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.8844324666241792, "volume": 138.29600000000002, "diffusion": 5.4, "adsorption": -41.5, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 86}, {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 0.901803225491355, "volume": 140.44800000000004, "diffusion": 4.845, "adsorption": -42.136, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 87}, {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9033584943438908, "volume": 130.26400000000004, "diffusion": 7.069, "adsorption": -45.433, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 88}, {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "score": 0.9121166977016457, "volume": 123.10400000000003, "diffusion": 5.715, "adsorption": -63.707, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 89}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "score": 0.9165583548698937, "volume": 155.94400000000005, "diffusion": 5.697, "adsorption": -44.692, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 90}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "score": 0.9302842896468388, "volume": 138.27200000000002, "diffusion": 6.005, "adsorption": -42.706, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 91}, {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9432198699047066, "volume": 137.42400000000004, "diffusion": 5.559, "adsorption": -40.876, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 92}, {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9488562557230436, "volume": 128.16000000000003, "diffusion": 5.837, "adsorption": -40.247, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 93}, {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 0.9589932023273517, "volume": 155.94400000000005, "diffusion": 5.22, "adsorption": -42.384, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 94}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "score": 0.9607310663195279, "volume": 142.05600000000004, "diffusion": 6.856, "adsorption": -45.587, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 95}, {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9671071044832668, "volume": 132.36800000000002, "diffusion": 5.387, "adsorption": -39.104, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 96}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "score": 0.9717693534026572, "volume": 136.73600000000002, "diffusion": 6.206, "adsorption": -42.415, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 97}, {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.975201293249412, "volume": 139.90400000000002, "diffusion": 6.278, "adsorption": -43.09, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 98}, {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.9954546473367671, "volume": 149.20800000000003, "diffusion": 6.245, "adsorption": -44.089, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 99}, {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0325504367695728, "volume": 134.16800000000003, "diffusion": 6.054, "adsorption": -40.463, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 100}, {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.0414980176119322, "volume": 140.78400000000002, "diffusion": 6.208, "adsorption": -41.854, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 101}, {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "score": 1.0510723354956668, "volume": 152.10400000000004, "diffusion": 5.101, "adsorption": -40.543, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 102}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "score": 1.056121726580545, "volume": 155.39200000000002, "diffusion": 6.271, "adsorption": -44.098, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 103}, {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.0751532923765865, "volume": 147.56000000000003, "diffusion": 5.736, "adsorption": -40.775, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 104}, {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1638072604253118, "volume": 157.32000000000005, "diffusion": 5.249, "adsorption": -39.166, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 105}, {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "score": 1.1886779056256729, "volume": 136.46400000000003, "diffusion": 5.34, "adsorption": -35.772, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 106}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "score": 1.2124925484756188, "volume": 147.07200000000003, "diffusion": 6.767, "adsorption": -41.74, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 107}, {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3366503626230894, "volume": 146.35200000000003, "diffusion": 6.614, "adsorption": -38.982, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 108}, {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 1.3616229133041093, "volume": 155.20000000000005, "diffusion": 6.014, "adsorption": -37.954, "layer": 0, "parent_smiles": "SC1NCCCN1", "is_selected": false, "rank": 109}], "1": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.07857482216483137, "volume": 109.74400000000003, "diffusion": 5.553, "adsorption": -51.881, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.23126308225810818, "volume": 126.99200000000003, "diffusion": 4.866, "adsorption": -51.514, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.377438080109443, "volume": 127.16800000000003, "diffusion": 5.224, "adsorption": -47.912, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4021730433307738, "volume": 130.28000000000003, "diffusion": 5.029, "adsorption": -54.847, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.49825329732019263, "volume": 122.94400000000003, "diffusion": 4.628, "adsorption": -47.066, "layer": 1, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "2": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.029995913789251965, "volume": 110.26400000000002, "diffusion": 5.241, "adsorption": -51.308, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.22968104709168252, "volume": 126.05600000000003, "diffusion": 5.224, "adsorption": -50.284, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.3607018710095223, "volume": 131.08800000000002, "diffusion": 5.223, "adsorption": -54.581, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.4891259181224679, "volume": 122.46400000000003, "diffusion": 4.612, "adsorption": -47.201, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.760554268236253, "volume": 126.32000000000004, "diffusion": 4.313, "adsorption": -44.116, "layer": 2, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "3": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.20142286785516123, "volume": 110.79200000000003, "diffusion": 4.945, "adsorption": -49.23, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.22285168494841867, "volume": 121.80800000000004, "diffusion": 4.899, "adsorption": -50.738, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.26624588475702193, "volume": 126.72800000000004, "diffusion": 4.8, "adsorption": -51.088, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.36678612244423764, "volume": 127.24800000000003, "diffusion": 5.199, "adsorption": -48.118, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.38254365003911417, "volume": 131.20000000000002, "diffusion": 5.053, "adsorption": -54.444, "layer": 3, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "4": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.028929055149885072, "volume": 110.43200000000003, "diffusion": 5.242, "adsorption": -51.356, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.2185893754227988, "volume": 122.09600000000003, "diffusion": 4.747, "adsorption": -51.359, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24663481281817484, "volume": 126.52000000000002, "diffusion": 4.883, "adsorption": -52.273, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.3695131128271979, "volume": 126.29600000000003, "diffusion": 5.224, "adsorption": -47.912, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.5054561973980128, "volume": 131.75200000000004, "diffusion": 5.146, "adsorption": -56.783, "layer": 4, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "5": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.19681877906055686, "volume": 110.71200000000003, "diffusion": 5.072, "adsorption": -48.877, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.24793830120001253, "volume": 126.40800000000003, "diffusion": 4.811, "adsorption": -51.317, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.2654262321002821, "volume": 131.51200000000003, "diffusion": 5.314, "adsorption": -50.82, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.30109761198279184, "volume": 127.24000000000002, "diffusion": 5.09, "adsorption": -49.609, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.3644718024589877, "volume": 122.96800000000003, "diffusion": 4.553, "adsorption": -49.623, "layer": 5, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}], "6": [{"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.02905073420513099, "volume": 110.16000000000003, "diffusion": 5.241, "adsorption": -51.308, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 1}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "score": 0.20864492587580444, "volume": 121.55200000000004, "diffusion": 4.777, "adsorption": -51.346, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 2}, {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "score": 0.24515301239344706, "volume": 127.14400000000003, "diffusion": 5.228, "adsorption": -50.201, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 3}, {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "score": 0.4486872144358555, "volume": 131.92000000000004, "diffusion": 5.066, "adsorption": -55.514, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 4}, {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "score": 0.743405042128785, "volume": 126.35200000000003, "diffusion": 4.282, "adsorption": -44.519, "layer": 6, "parent_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "is_selected": true, "rank": 5}]}, "tree": {"root": "SC1NCCCN1", "nodes": {"SC1NCCCN1": {"smiles": "SC1NCCCN1", "layer": 0, "properties": {"volume": 110.03200000000002, "diffusion": 5.213, "adsorption": -51.696}, "is_root": true}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.02905073420513099, "volume": 110.16000000000003, "diffusion": 5.241, "adsorption": -51.308, "is_selected": false, "rank": 1}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6, "score": 0.20864492587580444, "volume": 121.55200000000004, "diffusion": 4.777, "adsorption": -51.346, "is_selected": false, "rank": 2}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6, "score": 0.24515301239344706, "volume": 127.14400000000003, "diffusion": 5.228, "adsorption": -50.201, "is_selected": false, "rank": 3}, "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6, "score": 0.4486872144358555, "volume": 131.92000000000004, "diffusion": 5.066, "adsorption": -55.514, "is_selected": false, "rank": 4}, "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6, "score": 0.743405042128785, "volume": 126.35200000000003, "diffusion": 4.282, "adsorption": -44.519, "is_selected": false, "rank": 5}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.27672002141169433, "volume": 137.32800000000003, "diffusion": 5.274, "adsorption": -51.988, "is_selected": false, "rank": 6}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.2820109180720762, "volume": 122.91200000000003, "diffusion": 4.606, "adsorption": -52.532, "is_selected": false, "rank": 7}, "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.2904521872532988, "volume": 131.80000000000004, "diffusion": 4.925, "adsorption": -51.052, "is_selected": false, "rank": 8}, "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.2913280775086811, "volume": 118.87200000000003, "diffusion": 5.955, "adsorption": -50.513, "is_selected": false, "rank": 9}, "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.29232956661861476, "volume": 119.16800000000003, "diffusion": 5.382, "adsorption": -48.648, "is_selected": false, "rank": 10}, "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.2966700820010382, "volume": 129.56000000000003, "diffusion": 5.743, "adsorption": -51.998, "is_selected": false, "rank": 11}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0, "score": 0.3122197851489951, "volume": 127.25600000000003, "diffusion": 4.615, "adsorption": -52.402, "is_selected": false, "rank": 12}, "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.3171546118447952, "volume": 121.48800000000003, "diffusion": 4.748, "adsorption": -53.83, "is_selected": false, "rank": 13}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0, "score": 0.3186912999184926, "volume": 135.06400000000002, "diffusion": 4.917, "adsorption": -51.103, "is_selected": false, "rank": 14}, "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.3269156039678503, "volume": 126.69600000000003, "diffusion": 5.621, "adsorption": -50.021, "is_selected": false, "rank": 15}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0, "score": 0.33810173036320895, "volume": 127.97600000000003, "diffusion": 5.02, "adsorption": -54.074, "is_selected": false, "rank": 16}, "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.34257020070953925, "volume": 133.09600000000003, "diffusion": 5.17, "adsorption": -49.547, "is_selected": false, "rank": 17}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0, "score": 0.34527386320459075, "volume": 138.05600000000004, "diffusion": 5.383, "adsorption": -52.695, "is_selected": false, "rank": 18}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0, "score": 0.3748142554688088, "volume": 123.21600000000002, "diffusion": 5.619, "adsorption": -54.748, "is_selected": false, "rank": 19}, "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.37560598468488354, "volume": 144.13600000000002, "diffusion": 5.025, "adsorption": -52.206, "is_selected": false, "rank": 20}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.39718908448538326, "volume": 134.40000000000003, "diffusion": 5.085, "adsorption": -54.301, "is_selected": false, "rank": 21}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0, "score": 0.40153897750416717, "volume": 129.07200000000003, "diffusion": 4.75, "adsorption": -49.289, "is_selected": false, "rank": 22}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4098719988402465, "volume": 126.56800000000003, "diffusion": 4.993, "adsorption": -47.95, "is_selected": false, "rank": 23}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0, "score": 0.4150102254939805, "volume": 126.28000000000003, "diffusion": 4.752, "adsorption": -48.613, "is_selected": false, "rank": 24}, "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.4221359694560636, "volume": 128.90400000000002, "diffusion": 6.256, "adsorption": -50.825, "is_selected": false, "rank": 25}, "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.4337992416341916, "volume": 131.16800000000003, "diffusion": 5.134, "adsorption": -55.6, "is_selected": false, "rank": 26}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0, "score": 0.4429657289054881, "volume": 126.69600000000003, "diffusion": 5.545, "adsorption": -47.77, "is_selected": false, "rank": 27}, "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.4486096210121821, "volume": 144.82400000000004, "diffusion": 5.464, "adsorption": -50.244, "is_selected": false, "rank": 28}, "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4496146383202766, "volume": 139.65600000000003, "diffusion": 4.698, "adsorption": -50.29, "is_selected": false, "rank": 29}, "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4595945118092918, "volume": 128.92000000000004, "diffusion": 5.5, "adsorption": -55.709, "is_selected": false, "rank": 30}, "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.4644036171765398, "volume": 155.64800000000002, "diffusion": 5.011, "adsorption": -51.505, "is_selected": false, "rank": 31}, "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.46552718272986676, "volume": 145.28000000000003, "diffusion": 5.014, "adsorption": -49.852, "is_selected": false, "rank": 32}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.47572582430691257, "volume": 147.02400000000003, "diffusion": 5.63, "adsorption": -50.67, "is_selected": false, "rank": 33}, "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0, "score": 0.4815377286456288, "volume": 128.35200000000003, "diffusion": 5.073, "adsorption": -56.662, "is_selected": false, "rank": 34}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.49658421445278667, "volume": 142.13600000000002, "diffusion": 5.332, "adsorption": -54.832, "is_selected": false, "rank": 35}, "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5125145280931382, "volume": 129.17600000000004, "diffusion": 5.218, "adsorption": -45.879, "is_selected": false, "rank": 36}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.5207426541309508, "volume": 139.44800000000004, "diffusion": 4.599, "adsorption": -54.033, "is_selected": false, "rank": 37}, "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5208150992064196, "volume": 127.64800000000002, "diffusion": 5.242, "adsorption": -45.576, "is_selected": false, "rank": 38}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0, "score": 0.5302218700068078, "volume": 135.93600000000004, "diffusion": 5.658, "adsorption": -55.305, "is_selected": false, "rank": 39}, "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0, "score": 0.5318101345964882, "volume": 126.44000000000003, "diffusion": 4.763, "adsorption": -56.803, "is_selected": false, "rank": 40}, "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.533938478907177, "volume": 133.64000000000004, "diffusion": 5.948, "adsorption": -48.622, "is_selected": false, "rank": 41}, "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.5433007152571829, "volume": 146.10400000000004, "diffusion": 5.743, "adsorption": -53.657, "is_selected": false, "rank": 42}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0, "score": 0.547210764790756, "volume": 146.62400000000002, "diffusion": 5.625, "adsorption": -54.033, "is_selected": false, "rank": 43}, "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5495652643708153, "volume": 118.61600000000003, "diffusion": 6.23, "adsorption": -46.932, "is_selected": false, "rank": 44}, "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5546254967194846, "volume": 138.93600000000004, "diffusion": 5.729, "adsorption": -48.371, "is_selected": false, "rank": 45}, "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5569024004558525, "volume": 133.95200000000003, "diffusion": 4.809, "adsorption": -47.181, "is_selected": false, "rank": 46}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0, "score": 0.5620781264177257, "volume": 130.10400000000004, "diffusion": 6.14, "adsorption": -48.218, "is_selected": false, "rank": 47}, "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.5623291563037797, "volume": 127.50400000000003, "diffusion": 3.944, "adsorption": -48.937, "is_selected": false, "rank": 48}, "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.5646219275358839, "volume": 117.90400000000002, "diffusion": 5.847, "adsorption": -45.295, "is_selected": false, "rank": 49}, "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5675379911334202, "volume": 132.41600000000003, "diffusion": 5.163, "adsorption": -45.587, "is_selected": false, "rank": 50}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.5785294177071334, "volume": 135.97600000000003, "diffusion": 5.832, "adsorption": -47.836, "is_selected": false, "rank": 51}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.594604297741982, "volume": 155.91200000000003, "diffusion": 5.177, "adsorption": -54.638, "is_selected": false, "rank": 52}, "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.5948355425004883, "volume": 120.09600000000003, "diffusion": 6.627, "adsorption": -47.696, "is_selected": false, "rank": 53}, "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0, "score": 0.5984170727220162, "volume": 130.12800000000004, "diffusion": 5.752, "adsorption": -46.313, "is_selected": false, "rank": 54}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0, "score": 0.6036982068999014, "volume": 142.55200000000002, "diffusion": 6.378, "adsorption": -53.155, "is_selected": false, "rank": 55}, "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.6128483845695744, "volume": 145.60000000000002, "diffusion": 5.273, "adsorption": -56.488, "is_selected": false, "rank": 56}, "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6159717783199093, "volume": 154.20000000000005, "diffusion": 5.754, "adsorption": -49.787, "is_selected": false, "rank": 57}, "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6561436359501764, "volume": 124.45600000000003, "diffusion": 5.847, "adsorption": -44.744, "is_selected": false, "rank": 58}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0, "score": 0.6631020610472169, "volume": 137.16000000000003, "diffusion": 5.318, "adsorption": -44.865, "is_selected": false, "rank": 59}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0, "score": 0.6695177299033925, "volume": 128.84800000000004, "diffusion": 5.728, "adsorption": -58.584, "is_selected": false, "rank": 60}, "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0, "score": 0.6868065764969952, "volume": 121.62400000000002, "diffusion": 5.188, "adsorption": -61.633, "is_selected": false, "rank": 61}, "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6896498350675557, "volume": 123.40000000000003, "diffusion": 4.282, "adsorption": -44.983, "is_selected": false, "rank": 62}, "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6923648192948453, "volume": 135.70400000000004, "diffusion": 4.037, "adsorption": -47.673, "is_selected": false, "rank": 63}, "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.692807148013203, "volume": 138.11200000000002, "diffusion": 5.638, "adsorption": -45.56, "is_selected": false, "rank": 64}, "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]": {"smiles": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0, "score": 0.6948221706881711, "volume": 145.79200000000003, "diffusion": 5.399, "adsorption": -57.454, "is_selected": false, "rank": 65}, "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]": {"smiles": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0, "score": 0.696601073066211, "volume": 146.75200000000004, "diffusion": 4.882, "adsorption": -46.537, "is_selected": false, "rank": 66}, "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.6989243558334522, "volume": 128.03200000000004, "diffusion": 5.733, "adsorption": -59.202, "is_selected": false, "rank": 67}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0, "score": 0.7011662175488694, "volume": 130.28800000000004, "diffusion": 6.346, "adsorption": -46.531, "is_selected": false, "rank": 68}, "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.7183357702999391, "volume": 150.94400000000005, "diffusion": 5.531, "adsorption": -46.776, "is_selected": false, "rank": 69}, "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7365510601652624, "volume": 137.74400000000003, "diffusion": 7.076, "adsorption": -49.502, "is_selected": false, "rank": 70}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0, "score": 0.7397276327576585, "volume": 146.60000000000002, "diffusion": 6.182, "adsorption": -47.879, "is_selected": false, "rank": 71}, "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7428413278929518, "volume": 134.10400000000004, "diffusion": 5.222, "adsorption": -42.695, "is_selected": false, "rank": 72}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0, "score": 0.7487050251815144, "volume": 155.85600000000002, "diffusion": 5.685, "adsorption": -47.531, "is_selected": false, "rank": 73}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0, "score": 0.7840720491566737, "volume": 145.45600000000005, "diffusion": 6.111, "adsorption": -46.701, "is_selected": false, "rank": 74}, "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]": {"smiles": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.7881394489503134, "volume": 138.73600000000005, "diffusion": 7.222, "adsorption": -49.251, "is_selected": false, "rank": 75}, "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.7941357370962023, "volume": 130.67200000000003, "diffusion": 6.205, "adsorption": -44.523, "is_selected": false, "rank": 76}, "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.8178564136426184, "volume": 135.60000000000002, "diffusion": 5.665, "adsorption": -43.101, "is_selected": false, "rank": 77}, "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8194628878854562, "volume": 143.85600000000002, "diffusion": 4.883, "adsorption": -43.963, "is_selected": false, "rank": 78}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0, "score": 0.8323440157610564, "volume": 155.97600000000003, "diffusion": 5.752, "adsorption": -46.33, "is_selected": false, "rank": 79}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0, "score": 0.8435704229080144, "volume": 150.54400000000004, "diffusion": 6.299, "adsorption": -47.094, "is_selected": false, "rank": 80}, "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.8463840049972639, "volume": 133.40800000000004, "diffusion": 6.162, "adsorption": -43.909, "is_selected": false, "rank": 81}, "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.852252299627293, "volume": 146.84000000000003, "diffusion": 5.974, "adsorption": -45.29, "is_selected": false, "rank": 82}, "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8537962601734341, "volume": 131.12800000000004, "diffusion": 5.821, "adsorption": -42.297, "is_selected": false, "rank": 83}, "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8558205479433622, "volume": 138.31200000000004, "diffusion": 6.789, "adsorption": -46.587, "is_selected": false, "rank": 84}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0, "score": 0.859174935717202, "volume": 151.48000000000005, "diffusion": 5.459, "adsorption": -44.195, "is_selected": false, "rank": 85}, "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.8844324666241792, "volume": 138.29600000000002, "diffusion": 5.4, "adsorption": -41.5, "is_selected": false, "rank": 86}, "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 0.901803225491355, "volume": 140.44800000000004, "diffusion": 4.845, "adsorption": -42.136, "is_selected": false, "rank": 87}, "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9033584943438908, "volume": 130.26400000000004, "diffusion": 7.069, "adsorption": -45.433, "is_selected": false, "rank": 88}, "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9121166977016457, "volume": 123.10400000000003, "diffusion": 5.715, "adsorption": -63.707, "is_selected": false, "rank": 89}, "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0, "score": 0.9165583548698937, "volume": 155.94400000000005, "diffusion": 5.697, "adsorption": -44.692, "is_selected": false, "rank": 90}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0, "score": 0.9302842896468388, "volume": 138.27200000000002, "diffusion": 6.005, "adsorption": -42.706, "is_selected": false, "rank": 91}, "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9432198699047066, "volume": 137.42400000000004, "diffusion": 5.559, "adsorption": -40.876, "is_selected": false, "rank": 92}, "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9488562557230436, "volume": 128.16000000000003, "diffusion": 5.837, "adsorption": -40.247, "is_selected": false, "rank": 93}, "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 0.9589932023273517, "volume": 155.94400000000005, "diffusion": 5.22, "adsorption": -42.384, "is_selected": false, "rank": 94}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0, "score": 0.9607310663195279, "volume": 142.05600000000004, "diffusion": 6.856, "adsorption": -45.587, "is_selected": false, "rank": 95}, "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9671071044832668, "volume": 132.36800000000002, "diffusion": 5.387, "adsorption": -39.104, "is_selected": false, "rank": 96}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0, "score": 0.9717693534026572, "volume": 136.73600000000002, "diffusion": 6.206, "adsorption": -42.415, "is_selected": false, "rank": 97}, "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.975201293249412, "volume": 139.90400000000002, "diffusion": 6.278, "adsorption": -43.09, "is_selected": false, "rank": 98}, "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 0.9954546473367671, "volume": 149.20800000000003, "diffusion": 6.245, "adsorption": -44.089, "is_selected": false, "rank": 99}, "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0325504367695728, "volume": 134.16800000000003, "diffusion": 6.054, "adsorption": -40.463, "is_selected": false, "rank": 100}, "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.0414980176119322, "volume": 140.78400000000002, "diffusion": 6.208, "adsorption": -41.854, "is_selected": false, "rank": 101}, "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]": {"smiles": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0, "score": 1.0510723354956668, "volume": 152.10400000000004, "diffusion": 5.101, "adsorption": -40.543, "is_selected": false, "rank": 102}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0, "score": 1.056121726580545, "volume": 155.39200000000002, "diffusion": 6.271, "adsorption": -44.098, "is_selected": false, "rank": 103}, "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.0751532923765865, "volume": 147.56000000000003, "diffusion": 5.736, "adsorption": -40.775, "is_selected": false, "rank": 104}, "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1638072604253118, "volume": 157.32000000000005, "diffusion": 5.249, "adsorption": -39.166, "is_selected": false, "rank": 105}, "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]": {"smiles": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0, "score": 1.1886779056256729, "volume": 136.46400000000003, "diffusion": 5.34, "adsorption": -35.772, "is_selected": false, "rank": 106}, "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]": {"smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0, "score": 1.2124925484756188, "volume": 147.07200000000003, "diffusion": 6.767, "adsorption": -41.74, "is_selected": false, "rank": 107}, "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3366503626230894, "volume": 146.35200000000003, "diffusion": 6.614, "adsorption": -38.982, "is_selected": false, "rank": 108}, "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]": {"smiles": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0, "score": 1.3616229133041093, "volume": 155.20000000000005, "diffusion": 6.014, "adsorption": -37.954, "is_selected": false, "rank": 109}}, "edges": [{"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=C([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[N+]#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S([H])=S)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=C([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C#N", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([N+]#N)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(S[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)N([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N([H])N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([N+]#N)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(SC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(SC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]ON1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])=O)N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(S[N+]#N)N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N(C([H])([H])[H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C([H])=O)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)N([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(S[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C(=S)C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])N([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(N([H])[H])C1([H])C([H])([H])N([H])C([H])(S[H])N([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=C(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=O", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)N1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1SC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N([H])C(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N(C([H])([H])[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(N=C([H])C([H])([H])[H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CN1C([H])(S[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SSC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N=[C]C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)C1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N=C([H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C([H])=C([H])SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N(SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=O)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C(=S)C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])(C(=O)OC([H])([H])[H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C([H])=C([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C([H])=C([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SN(C([H])([H])[H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)N([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C#CC1(S[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]OC(=O)SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(N=C([H])C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)N([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]C(=NSC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H])C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)OC([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SC(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]N1C([H])(SOC([H])([H])[H])N([H])C([H])([H])C([H])([H])C1([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1C(=O)C([H])([H])[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)C([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "SC1NCCCN1", "to": "[H]SC1(C(=O)OC([H])([H])[H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 0}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 1}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 2}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 3}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 4}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 5}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "layer": 6}, {"from": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "to": "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "layer": 6}]}, "messages": [{"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770944533.9535885}, {"type": "log", "level": "info", "message": "INFO - [Min<PERSON>] 远程日志已启用 - bucket: molecule-gen, run_id: 81814875-328e-4243-a436-f91efc3f0908", "timestamp": 1770944533.9895508}, {"type": "log", "level": "info", "message": "INFO - [File] 文件日志已启用 - logs/81814875-328e-4243-a436-f91efc3f0908.log", "timestamp": 1770944534.015954}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770944534.3813906}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770944534.4078832}, {"type": "log", "level": "info", "message": "INFO - [MinIO] mol_logger 已初始化并设置输入", "timestamp": 1770944534.4362195}, {"type": "log", "level": "info", "message": "已挂载到 logger: mol_generator", "timestamp": 1770944534.4598243}, {"type": "log", "level": "info", "message": "远程日志已启动: 81814875-328e-4243-a436-f91efc3f0908", "timestamp": 1770944534.4618337}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944534.4815893}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944534.5030937}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770944534.564666}, {"type": "log", "level": "info", "message": "INFO - 开始分子生成流程", "timestamp": 1770944534.5843987}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770944534.6034489}, {"type": "log", "level": "info", "message": "INFO - 骨架分子: SC1NCCCN1", "timestamp": 1770944534.622751}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770944534.6420233}, {"type": "log", "level": "info", "message": "INFO - 原始属性 - 体积: 110.03, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770944534.6620705}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770944534.6834664}, {"type": "log", "level": "info", "message": "INFO - 配置参数 - 中间层保留数: 5, 最终保留数: 10", "timestamp": 1770944534.705472}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944534.735775}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944534.7597647}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770944534.7903335}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770944534.8186178}, {"type": "log", "level": "info", "message": "配置: {'top_num': 5, 'top_num_last': 10}", "timestamp": 1770944534.840282}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770944534.8774738}, {"type": "log", "level": "info", "message": "远程日志已启动: 81814875-328e-4243-a436-f91efc3f0908", "timestamp": 1770944534.8804545}, {"type": "log", "level": "info", "message": "INFO - 识别到 7 个可替换位点", "timestamp": 1770944534.901429}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770944534.923856}, {"type": "log", "level": "info", "message": "INFO - 可替换原子索引: [0, 1, 2, 3, 4, 5, 6]", "timestamp": 1770944534.990096}, {"type": "log", "level": "info", "message": "共 7 个可替换位点", "timestamp": 1770944535.009772}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944535.0347664}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944535.0584877}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944535.0822065}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944535.1044562}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770944535.1233413}, {"type": "log", "level": "info", "message": "INFO - 第 1/7 层生成", "timestamp": 1770944535.14202}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944535.160179}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944535.18565}, {"type": "log", "level": "info", "message": "========== 第 0 层开始 ==========", "timestamp": 1770944535.2077088}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770944535.2589967}, {"type": "log", "level": "info", "message": "INFO - 第 0 层生成 186 个分子，去重后 172 个，耗时 0.03 秒", "timestamp": 1770944535.2828033}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770944535.3075125}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 172 个新分子", "timestamp": 1770944535.3283544}, {"type": "log", "level": "info", "message": "生成: 172 个分子, 去重后 172 个, 耗时 0.03s, 速度: 5711.23 mol/s", "timestamp": 1770944535.3476133}, {"type": "generation", "layer": 0, "total_generated": 172, "unique_mols": 172, "elapsed": 0.03011608123779297, "speed": 5711.23442794262, "timestamp": 1770944535.3670855}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770944535.4046886}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因分子量过滤: 14 个 (Max: 177.08)", "timestamp": 1770944535.4232483}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770944535.4441323}, {"type": "log", "level": "info", "message": "INFO -     [筛选详情] 因 LogP 过滤: 0 个 (Min: -3.00)", "timestamp": 1770944535.4659038}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770944535.4922235}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 158 个分子 (过滤掉 14 个)", "timestamp": 1770944535.5094357}, {"type": "log", "level": "info", "message": "筛选: 剩余 158 个分子 （移除 14 个）", "timestamp": 1770944535.5250757}, {"type": "filtering", "layer": 0, "filtered_count": 158, "removed": 14, "timestamp": 1770944535.5429783}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770944535.5635607}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 158 个分子的性质...", "timestamp": 1770944535.581385}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770944545.455857}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 109 个分子的性质预测", "timestamp": 1770944545.4806836}, {"type": "log", "level": "info", "message": "评分: 完成 109 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0782", "timestamp": 1770944545.506081}, {"type": "scoring", "layer": 0, "scored_count": 109, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07821129155555415, "timestamp": 1770944545.5353234}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944545.5582056}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944545.5751638}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944545.5948644}, {"type": "selection", "layer": 0, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770944545.6191547}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944545.6411688}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944545.6591465}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0782", "timestamp": 1770944545.6797132}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0782", "timestamp": 1770944545.6966147}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.28", "timestamp": 1770944545.7134857}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.28", "timestamp": 1770944545.7295427}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770944545.7492976}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770944545.767172}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770944545.7846715}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770944545.8023267}, {"type": "log", "level": "info", "message": "========== 第 0 层完成 (耗时 10.61s) ==========", "timestamp": 1770944545.82091}, {"type": "layer_end", "layer": 0, "duration": 10.61311388015747, "timestamp": 1770944545.8362448}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944545.852877}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944545.8685694}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944545.8842661}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944545.8989172}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770944545.91409}, {"type": "log", "level": "info", "message": "INFO - 第 2/7 层生成", "timestamp": 1770944545.932507}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944545.9468513}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944545.9630742}, {"type": "log", "level": "info", "message": "========== 第 1 层开始 ==========", "timestamp": 1770944545.9786816}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770944545.9978046}, {"type": "log", "level": "info", "message": "INFO - 第 1 层生成 30 个分子，去重后 30 个，耗时 0.00 秒", "timestamp": 1770944546.0153203}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770944546.0322578}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 30 个新分子", "timestamp": 1770944546.0527093}, {"type": "log", "level": "info", "message": "生成: 30 个分子, 去重后 30 个, 耗时 0.00s, 速度: 35514.85 mol/s", "timestamp": 1770944546.0758426}, {"type": "generation", "layer": 1, "total_generated": 30, "unique_mols": 30, "elapsed": 0.0008447170257568359, "speed": 35514.85182049111, "timestamp": 1770944546.1003273}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770944546.1299238}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 30 个分子 (过滤掉 0 个)", "timestamp": 1770944546.1567423}, {"type": "log", "level": "info", "message": "筛选: 剩余 30 个分子 （移除 0 个）", "timestamp": 1770944546.1859019}, {"type": "filtering", "layer": 1, "filtered_count": 30, "removed": 0, "timestamp": 1770944546.2081757}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770944546.2289531}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 30 个分子的性质...", "timestamp": 1770944546.2498574}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944547.9104962}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944547.936748}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0786", "timestamp": 1770944547.9610496}, {"type": "scoring", "layer": 1, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.07857482216483137, "timestamp": 1770944547.9788082}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944547.9998937}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944548.0198505}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944548.0406456}, {"type": "selection", "layer": 1, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770944548.064214}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944548.0855927}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944548.1066637}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0786", "timestamp": 1770944548.128154}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0786", "timestamp": 1770944548.1501698}, {"type": "log", "level": "info", "message": "INFO -     体积: 109.74", "timestamp": 1770944548.1736515}, {"type": "log", "level": "info", "message": "INFO -     体积: 109.74", "timestamp": 1770944548.1970043}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770944548.2197378}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.5530", "timestamp": 1770944548.2404509}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770944548.260549}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.8810", "timestamp": 1770944548.280564}, {"type": "log", "level": "info", "message": "========== 第 1 层完成 (耗时 2.32s) ==========", "timestamp": 1770944548.3017266}, {"type": "layer_end", "layer": 1, "duration": 2.3226287364959717, "timestamp": 1770944548.319951}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944548.3374922}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944548.3571281}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944548.3756082}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944548.3924491}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770944548.410394}, {"type": "log", "level": "info", "message": "INFO - 第 3/7 层生成", "timestamp": 1770944548.4272754}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944548.4468062}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944548.4647355}, {"type": "log", "level": "info", "message": "========== 第 2 层开始 ==========", "timestamp": 1770944548.4861124}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770944548.507649}, {"type": "log", "level": "info", "message": "INFO - 第 2 层生成 25 个分子，去重后 25 个，耗时 0.00 秒", "timestamp": 1770944548.5253363}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770944548.5428708}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 25 个新分子", "timestamp": 1770944548.5626967}, {"type": "log", "level": "info", "message": "生成: 25 个分子, 去重后 25 个, 耗时 0.00s, 速度: 33890.63 mol/s", "timestamp": 1770944548.5814168}, {"type": "generation", "layer": 2, "total_generated": 25, "unique_mols": 25, "elapsed": 0.0007376670837402344, "speed": 33890.62702003879, "timestamp": 1770944548.604368}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770944548.6332686}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 25 个分子 (过滤掉 0 个)", "timestamp": 1770944548.6531243}, {"type": "log", "level": "info", "message": "筛选: 剩余 25 个分子 （移除 0 个）", "timestamp": 1770944548.6721928}, {"type": "filtering", "layer": 2, "filtered_count": 25, "removed": 0, "timestamp": 1770944548.6940541}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770944548.7100258}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 25 个分子的性质...", "timestamp": 1770944548.7277906}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944550.198157}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944550.215332}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0300", "timestamp": 1770944550.2349102}, {"type": "scoring", "layer": 2, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.029995913789251965, "timestamp": 1770944550.253139}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944550.2719014}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944550.2926674}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944550.3196094}, {"type": "selection", "layer": 2, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770944550.3418374}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944550.3641732}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944550.3866937}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0300", "timestamp": 1770944550.4105878}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0300", "timestamp": 1770944550.428931}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.26", "timestamp": 1770944550.4467556}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.26", "timestamp": 1770944550.4636276}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770944550.4836512}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770944550.5085063}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770944550.5300846}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770944550.5471683}, {"type": "log", "level": "info", "message": "========== 第 2 层完成 (耗时 2.08s) ==========", "timestamp": 1770944550.5666473}, {"type": "layer_end", "layer": 2, "duration": 2.0798227787017822, "timestamp": 1770944550.5836885}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944550.6019819}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944550.621934}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944550.642523}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944550.6640282}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770944550.6839921}, {"type": "log", "level": "info", "message": "INFO - 第 4/7 层生成", "timestamp": 1770944550.7032928}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944550.7242222}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944550.741907}, {"type": "log", "level": "info", "message": "========== 第 3 层开始 ==========", "timestamp": 1770944550.7605238}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770944550.780298}, {"type": "log", "level": "info", "message": "INFO - 第 3 层生成 20 个分子，去重后 20 个，耗时 0.00 秒", "timestamp": 1770944550.8015065}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770944550.8210227}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 20 个新分子", "timestamp": 1770944550.8380756}, {"type": "log", "level": "info", "message": "生成: 20 个分子, 去重后 20 个, 耗时 0.00s, 速度: 26479.19 mol/s", "timestamp": 1770944550.85608}, {"type": "generation", "layer": 3, "total_generated": 20, "unique_mols": 20, "elapsed": 0.00075531005859375, "speed": 26479.19191919192, "timestamp": 1770944550.8750455}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770944550.893046}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 20 个分子 (过滤掉 0 个)", "timestamp": 1770944550.9084873}, {"type": "log", "level": "info", "message": "筛选: 剩余 20 个分子 （移除 0 个）", "timestamp": 1770944550.923664}, {"type": "filtering", "layer": 3, "filtered_count": 20, "removed": 0, "timestamp": 1770944550.9386013}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770944550.95408}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 20 个分子的性质...", "timestamp": 1770944550.97271}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944552.1784575}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944552.1981468}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.2014", "timestamp": 1770944552.2149446}, {"type": "scoring", "layer": 3, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.20142286785516123, "timestamp": 1770944552.22963}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944552.248187}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944552.263448}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944552.2832625}, {"type": "selection", "layer": 3, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770944552.2989094}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944552.3173344}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944552.3315074}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2014", "timestamp": 1770944552.3493607}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.2014", "timestamp": 1770944552.3674626}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.79", "timestamp": 1770944552.3936796}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.79", "timestamp": 1770944552.4183419}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9450", "timestamp": 1770944552.445648}, {"type": "log", "level": "info", "message": "INFO -     扩散: 4.9450", "timestamp": 1770944552.476644}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.2300", "timestamp": 1770944552.499114}, {"type": "log", "level": "info", "message": "INFO -     吸附: -49.2300", "timestamp": 1770944552.5199616}, {"type": "log", "level": "info", "message": "========== 第 3 层完成 (耗时 1.78s) ==========", "timestamp": 1770944552.5436893}, {"type": "layer_end", "layer": 3, "duration": 1.7829375267028809, "timestamp": 1770944552.5622146}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944552.582247}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944552.601137}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944552.6184516}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944552.6463516}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770944552.6648793}, {"type": "log", "level": "info", "message": "INFO - 第 5/7 层生成", "timestamp": 1770944552.6854138}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944552.7040412}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944552.723582}, {"type": "log", "level": "info", "message": "========== 第 4 层开始 ==========", "timestamp": 1770944552.7433064}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770944552.764817}, {"type": "log", "level": "info", "message": "INFO - 第 4 层生成 15 个分子，去重后 15 个，耗时 0.00 秒", "timestamp": 1770944552.785467}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770944552.802543}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 15 个新分子", "timestamp": 1770944552.823865}, {"type": "log", "level": "info", "message": "生成: 15 个分子, 去重后 15 个, 耗时 0.00s, 速度: 14796.46 mol/s", "timestamp": 1770944552.8491607}, {"type": "generation", "layer": 4, "total_generated": 15, "unique_mols": 15, "elapsed": 0.0010137557983398438, "speed": 14796.462841015993, "timestamp": 1770944552.868077}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770944552.8893206}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 15 个分子 (过滤掉 0 个)", "timestamp": 1770944552.9087126}, {"type": "log", "level": "info", "message": "筛选: 剩余 15 个分子 （移除 0 个）", "timestamp": 1770944552.9269931}, {"type": "filtering", "layer": 4, "filtered_count": 15, "removed": 0, "timestamp": 1770944552.9480548}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770944552.9682872}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 15 个分子的性质...", "timestamp": 1770944552.9895165}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944553.9300468}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944553.9580646}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0289", "timestamp": 1770944553.9794738}, {"type": "scoring", "layer": 4, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.028929055149885072, "timestamp": 1770944553.9985216}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944554.018248}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944554.037782}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944554.0608392}, {"type": "selection", "layer": 4, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]"], "timestamp": 1770944554.0812092}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944554.1004472}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944554.1217225}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0289", "timestamp": 1770944554.1425493}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0289", "timestamp": 1770944554.1612904}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.43", "timestamp": 1770944554.1814837}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.43", "timestamp": 1770944554.2027874}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770944554.2222383}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2420", "timestamp": 1770944554.2419689}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770944554.2626374}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3560", "timestamp": 1770944554.283692}, {"type": "log", "level": "info", "message": "========== 第 4 层完成 (耗时 1.57s) ==========", "timestamp": 1770944554.3091352}, {"type": "layer_end", "layer": 4, "duration": 1.5652246475219727, "timestamp": 1770944554.3295884}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944554.3492665}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944554.368422}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944554.3898773}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944554.4110353}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770944554.4316375}, {"type": "log", "level": "info", "message": "INFO - 第 6/7 层生成", "timestamp": 1770944554.451455}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944554.4754028}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944554.4974933}, {"type": "log", "level": "info", "message": "========== 第 5 层开始 ==========", "timestamp": 1770944554.5158696}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770944554.5367644}, {"type": "log", "level": "info", "message": "INFO - 第 5 层生成 10 个分子，去重后 10 个，耗时 0.00 秒", "timestamp": 1770944554.5576289}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770944554.5783007}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 10 个新分子", "timestamp": 1770944554.6060164}, {"type": "log", "level": "info", "message": "生成: 10 个分子, 去重后 10 个, 耗时 0.00s, 速度: 23750.31 mol/s", "timestamp": 1770944554.6254668}, {"type": "generation", "layer": 5, "total_generated": 10, "unique_mols": 10, "elapsed": 0.0004210472106933594, "speed": 23750.30577576444, "timestamp": 1770944554.6451545}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770944554.6638193}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 10 个分子 (过滤掉 0 个)", "timestamp": 1770944554.68724}, {"type": "log", "level": "info", "message": "筛选: 剩余 10 个分子 （移除 0 个）", "timestamp": 1770944554.708528}, {"type": "filtering", "layer": 5, "filtered_count": 10, "removed": 0, "timestamp": 1770944554.7270832}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770944554.7461157}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 10 个分子的性质...", "timestamp": 1770944554.7641962}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944555.4945917}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944555.5162177}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.1968", "timestamp": 1770944555.5323365}, {"type": "scoring", "layer": 5, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.19681877906055686, "timestamp": 1770944555.553999}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944555.5724902}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944555.5871964}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944555.6045587}, {"type": "selection", "layer": 5, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]"], "timestamp": 1770944555.624725}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944555.6443682}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944555.660676}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1968", "timestamp": 1770944555.681648}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.1968", "timestamp": 1770944555.7024796}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.71", "timestamp": 1770944555.7174065}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.71", "timestamp": 1770944555.73408}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0720", "timestamp": 1770944555.7557223}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.0720", "timestamp": 1770944555.7742329}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.8770", "timestamp": 1770944555.791943}, {"type": "log", "level": "info", "message": "INFO -     吸附: -48.8770", "timestamp": 1770944555.8124983}, {"type": "log", "level": "info", "message": "========== 第 5 层完成 (耗时 1.31s) ==========", "timestamp": 1770944555.830866}, {"type": "layer_end", "layer": 5, "duration": 1.314990520477295, "timestamp": 1770944555.8509877}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944555.8691676}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944555.8882427}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944555.9050875}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944555.9245791}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770944555.9439545}, {"type": "log", "level": "info", "message": "INFO - 第 7/7 层生成", "timestamp": 1770944555.9624975}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944555.9801219}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944555.9989564}, {"type": "log", "level": "info", "message": "========== 第 6 层开始 ==========", "timestamp": 1770944556.0173106}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770944556.0486112}, {"type": "log", "level": "info", "message": "INFO - 第 6 层生成 5 个分子，去重后 5 个，耗时 0.00 秒", "timestamp": 1770944556.0743244}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770944556.0946567}, {"type": "log", "level": "info", "message": "INFO - ✓ 生成 5 个新分子", "timestamp": 1770944556.1138315}, {"type": "log", "level": "info", "message": "生成: 5 个分子, 去重后 5 个, 耗时 0.00s, 速度: 13565.02 mol/s", "timestamp": 1770944556.1317313}, {"type": "generation", "layer": 6, "total_generated": 5, "unique_mols": 5, "elapsed": 0.0003685951232910156, "speed": 13565.019404915913, "timestamp": 1770944556.1526852}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770944556.1702394}, {"type": "log", "level": "info", "message": "INFO - ✓ 筛选后剩余 5 个分子 (过滤掉 0 个)", "timestamp": 1770944556.1846237}, {"type": "log", "level": "info", "message": "筛选: 剩余 5 个分子 （移除 0 个）", "timestamp": 1770944556.204688}, {"type": "filtering", "layer": 6, "filtered_count": 5, "removed": 0, "timestamp": 1770944556.22163}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770944556.2370417}, {"type": "log", "level": "info", "message": "INFO - ⏳ 正在批量预测 5 个分子的性质...", "timestamp": 1770944556.254833}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944556.6700032}, {"type": "log", "level": "info", "message": "INFO - ✓ 完成 5 个分子的性质预测", "timestamp": 1770944556.6893919}, {"type": "log", "level": "info", "message": "评分: 完成 5 个分子, 最佳: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H], Score: 0.0291", "timestamp": 1770944556.7058923}, {"type": "scoring", "layer": 6, "scored_count": 5, "best_smiles": "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "best_score": 0.02905073420513099, "timestamp": 1770944556.7256305}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944556.7430844}, {"type": "log", "level": "info", "message": "INFO - ✓ 选择 Top 5 个候选分子进入下一层", "timestamp": 1770944556.7629745}, {"type": "log", "level": "info", "message": "选择: Top 5 个候选分子", "timestamp": 1770944556.7824163}, {"type": "selection", "layer": 6, "top_n": 5, "selected_smiles": ["[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "[H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "[H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "[H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]"], "timestamp": 1770944556.8028011}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944556.8187923}, {"type": "log", "level": "info", "message": "INFO -   最佳候选: [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944556.8366632}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0291", "timestamp": 1770944556.8564675}, {"type": "log", "level": "info", "message": "INFO -     Score: 0.0291", "timestamp": 1770944556.8739252}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.16", "timestamp": 1770944556.8928533}, {"type": "log", "level": "info", "message": "INFO -     体积: 110.16", "timestamp": 1770944556.916081}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770944556.935008}, {"type": "log", "level": "info", "message": "INFO -     扩散: 5.2410", "timestamp": 1770944556.9545097}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770944556.9763994}, {"type": "log", "level": "info", "message": "INFO -     吸附: -51.3080", "timestamp": 1770944556.9972277}, {"type": "log", "level": "info", "message": "========== 第 6 层完成 (耗时 1.00s) ==========", "timestamp": 1770944557.017}, {"type": "layer_end", "layer": 6, "duration": 0.9994771480560303, "timestamp": 1770944557.0363414}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944557.4371824}, {"type": "log", "level": "info", "message": "INFO - ", "timestamp": 1770944557.4553092}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944557.4821444}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944557.4996102}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770944557.5187972}, {"type": "log", "level": "info", "message": "INFO - 分子生成完成！", "timestamp": 1770944557.5357165}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 22.02 秒", "timestamp": 1770944557.5530846}, {"type": "log", "level": "info", "message": "INFO - 总耗时: 22.02 秒", "timestamp": 1770944557.5703845}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770944557.5875428}, {"type": "log", "level": "info", "message": "INFO - 最终结果数: 5 个分子", "timestamp": 1770944557.6062608}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944557.6259682}, {"type": "log", "level": "info", "message": "INFO - ============================================================", "timestamp": 1770944557.6465025}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770944557.6656678}, {"type": "log", "level": "info", "message": "INFO - \nTop 5 候选分子:", "timestamp": 1770944557.6863875}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944557.7041292}, {"type": "log", "level": "info", "message": "INFO -   1. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944557.7212071}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.0848, 体积: 111.01, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770944557.738308}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.0848, 体积: 111.01, 扩散: 5.5530, 吸附: -51.8810", "timestamp": 1770944557.757649}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770944557.776241}, {"type": "log", "level": "info", "message": "INFO -   2. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])(C([H])([H])[H])N1[H]", "timestamp": 1770944557.799089}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2474, 体积: 127.37, 扩散: 5.2350, 吸附: -50.2200", "timestamp": 1770944557.8204045}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2474, 体积: 127.37, 扩散: 5.2350, 吸附: -50.2200", "timestamp": 1770944557.8406026}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944557.8620062}, {"type": "log", "level": "info", "message": "INFO -   3. [H]SC1([N+]#N)N([H])C([H])([H])C([H])([H])C([H])([H])N1[H]", "timestamp": 1770944557.881936}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2656, 体积: 126.66, 扩散: 4.8000, 吸附: -51.0880", "timestamp": 1770944557.9027057}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2656, 体积: 126.66, 扩散: 4.8000, 吸附: -51.0880", "timestamp": 1770944557.924755}, {"type": "log", "level": "info", "message": "INFO -   4. [H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770944557.9425983}, {"type": "log", "level": "info", "message": "INFO -   4. [H]N=[C]C1([H])N([H])C([H])(S[H])N([H])C([H])([H])C1([H])[H]", "timestamp": 1770944557.9624817}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2703, 体积: 131.94, 扩散: 5.3100, 吸附: -50.7900", "timestamp": 1770944557.9794655}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.2703, 体积: 131.94, 扩散: 5.3100, 吸附: -50.7900", "timestamp": 1770944558.0004349}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770944558.03331}, {"type": "log", "level": "info", "message": "INFO -   5. [H]SC1([H])N([H])C([H])([H])C([H])([H])C([H])([H])N1N([H])[H]", "timestamp": 1770944558.054686}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3899, 体积: 122.15, 扩散: 4.6640, 吸附: -48.6900", "timestamp": 1770944558.0774887}, {"type": "log", "level": "info", "message": "INFO -      Score: 0.3899, 体积: 122.15, 扩散: 4.6640, 吸附: -48.6900", "timestamp": 1770944558.0984116}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 24.17s", "timestamp": 1770944558.1202135}, {"type": "finish", "total_results": 5, "duration": 24.168079614639282, "timestamp": 1770944558.1403236}, {"type": "log", "level": "info", "message": "JSON 日志已保存到: logs/81814875-328e-4243-a436-f91efc3f0908.json", "timestamp": 1770944558.1650107}, {"type": "log", "level": "info", "message": "远程日志已上传: 81814875-328e-4243-a436-f91efc3f0908", "timestamp": 1770944558.203388}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770944558.2253768}, {"type": "log", "level": "info", "message": "INFO - [DEBUG] mol_logger is None: False", "timestamp": 1770944558.2466779}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770944558.2637854}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 开始保存日志到 MinIO...", "timestamp": 1770944558.282365}, {"type": "log", "level": "info", "message": "输入分子: SC1NCCCN1", "timestamp": 1770944558.3054833}, {"type": "log", "level": "info", "message": "原始属性 - 体积: 110.0320, 扩散: 5.2130, 吸附: -51.6960", "timestamp": 1770944558.3261192}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770944558.3465078}, {"type": "log", "level": "info", "message": "INFO - [MinIO] 已设置输入信息", "timestamp": 1770944558.3718922}, {"type": "log", "level": "success", "message": "生成完成！共 5 个结果，总耗时 24.46s", "timestamp": 1770944558.4072466}, {"type": "log", "level": "info", "message": "远程日志已启动: 81814875-328e-4243-a436-f91efc3f0908", "timestamp": 1770944558.418551}, {"type": "finish", "total_results": 5, "duration": 24.4551100730896, "timestamp": 1770944558.482758}]}