Metadata-Version: 2.4
Name: wandb
Version: 0.24.2
Summary: A CLI and library for interacting with the Weights & Biases API.
Project-URL: Source, https://github.com/wandb/wandb
Project-URL: Bug Reports, https://github.com/wandb/wandb/issues
Project-URL: Documentation, https://docs.wandb.ai/
Author-email: Weights & Biases <<EMAIL>>
License: MIT License
        
        Copyright (c) 2021 Weights and Biases, Inc.
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Go
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Logging
Classifier: Topic :: System :: Monitoring
Requires-Python: >=3.8
Requires-Dist: click>=8.0.1
Requires-Dist: eval-type-backport; python_version < '3.10'
Requires-Dist: gitpython!=3.1.29,>=1.0.0
Requires-Dist: packaging
Requires-Dist: platformdirs
Requires-Dist: protobuf!=4.21.0,!=5.28.0,<7,>=3.12.0; python_version < '3.9' and sys_platform == 'linux'
Requires-Dist: protobuf!=4.21.0,!=5.28.0,<7,>=3.15.0; python_version == '3.9' and sys_platform == 'linux'
Requires-Dist: protobuf!=4.21.0,!=5.28.0,<7,>=3.19.0; python_version > '3.9' and sys_platform == 'linux'
Requires-Dist: protobuf!=4.21.0,!=5.28.0,<7,>=3.19.0; sys_platform != 'linux'
Requires-Dist: pydantic<3
Requires-Dist: pyyaml
Requires-Dist: requests<3,>=2.0.0
Requires-Dist: sentry-sdk>=2.0.0
Requires-Dist: typing-extensions<5,>=4.8
Provides-Extra: aws
Requires-Dist: boto3; extra == 'aws'
Requires-Dist: botocore>=1.5.76; extra == 'aws'
Provides-Extra: azure
Requires-Dist: azure-identity; extra == 'azure'
Requires-Dist: azure-storage-blob; extra == 'azure'
Provides-Extra: gcp
Requires-Dist: google-cloud-storage; extra == 'gcp'
Provides-Extra: importers
Requires-Dist: filelock; extra == 'importers'
Requires-Dist: mlflow; extra == 'importers'
Requires-Dist: polars<=1.2.1; extra == 'importers'
Requires-Dist: rich; extra == 'importers'
Requires-Dist: tenacity; extra == 'importers'
Provides-Extra: kubeflow
Requires-Dist: google-cloud-storage; extra == 'kubeflow'
Requires-Dist: kubernetes; extra == 'kubeflow'
Requires-Dist: minio; extra == 'kubeflow'
Requires-Dist: sh; extra == 'kubeflow'
Provides-Extra: launch
Requires-Dist: awscli; extra == 'launch'
Requires-Dist: azure-containerregistry; extra == 'launch'
Requires-Dist: azure-identity; extra == 'launch'
Requires-Dist: azure-storage-blob; extra == 'launch'
Requires-Dist: boto3; extra == 'launch'
Requires-Dist: botocore>=1.5.76; extra == 'launch'
Requires-Dist: chardet; extra == 'launch'
Requires-Dist: google-auth; extra == 'launch'
Requires-Dist: google-cloud-aiplatform; extra == 'launch'
Requires-Dist: google-cloud-artifact-registry; extra == 'launch'
Requires-Dist: google-cloud-compute; extra == 'launch'
Requires-Dist: google-cloud-storage; extra == 'launch'
Requires-Dist: iso8601; extra == 'launch'
Requires-Dist: jsonschema; extra == 'launch'
Requires-Dist: kubernetes; extra == 'launch'
Requires-Dist: kubernetes-asyncio; extra == 'launch'
Requires-Dist: nbconvert; extra == 'launch'
Requires-Dist: nbformat; extra == 'launch'
Requires-Dist: optuna; extra == 'launch'
Requires-Dist: pydantic; extra == 'launch'
Requires-Dist: pyyaml>=6.0.0; extra == 'launch'
Requires-Dist: tomli; extra == 'launch'
Requires-Dist: tornado>=6.5.0; (python_version >= '3.9') and extra == 'launch'
Requires-Dist: typing-extensions; extra == 'launch'
Provides-Extra: media
Requires-Dist: bokeh; extra == 'media'
Requires-Dist: imageio>=2.28.1; extra == 'media'
Requires-Dist: moviepy>=1.0.0; extra == 'media'
Requires-Dist: numpy; extra == 'media'
Requires-Dist: pillow; extra == 'media'
Requires-Dist: plotly>=5.18.0; extra == 'media'
Requires-Dist: rdkit; extra == 'media'
Requires-Dist: soundfile; extra == 'media'
Provides-Extra: models
Requires-Dist: cloudpickle; extra == 'models'
Provides-Extra: perf
Requires-Dist: orjson; extra == 'perf'
Provides-Extra: sweeps
Requires-Dist: sweeps>=0.2.0; extra == 'sweeps'
Provides-Extra: workspaces
Requires-Dist: wandb-workspaces; extra == 'workspaces'
Description-Content-Type: text/markdown

<div align="center">
  <img src="https://i.imgur.com/dQLeGCc.png" width="600" /><br><br>
</div>

<p align="center">
<a href="https://pypi.python.org/pypi/wandb"><img src="https://img.shields.io/pypi/v/wandb" /></a>
<a href="https://anaconda.org/conda-forge/wandb"><img src="https://img.shields.io/conda/vn/conda-forge/wandb" /></a>
<a href="https://pypi.python.org/pypi/wandb"><img src="https://img.shields.io/pypi/pyversions/wandb" /></a>
<a href="https://circleci.com/gh/wandb/wandb"><img src="https://img.shields.io/circleci/build/github/wandb/wandb/main" /></a>
<a href="https://codecov.io/gh/wandb/wandb"><img src="https://img.shields.io/codecov/c/gh/wandb/wandb" /></a>
</p>
<p align='center'>
<a href="https://colab.research.google.com/github/wandb/examples/blob/master/colabs/intro/Intro_to_Weights_%26_Biases.ipynb"><img src="https://colab.research.google.com/assets/colab-badge.svg" /></a>
</p>

Use W&B to build better models faster. Track and visualize all the pieces of your machine learning pipeline, from datasets to production machine learning models. Get started with W&B today, [sign up for a W&B account](https://wandb.com?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=readme)!

<br>

Building an LLM app? Track, debug, evaluate, and monitor LLM apps with [Weave](https://wandb.github.io/weave?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=readme), our new suite of tools for GenAI.

&nbsp;

# Documentation

See the [W&B Developer Guide](https://docs.wandb.ai/?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=documentation) and [API Reference Guide](https://docs.wandb.ai/training/api-reference#api-overview?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=documentation) for a full technical description of the W&B platform.

&nbsp;

# Quickstart

Install W&B to track, visualize, and manage machine learning experiments of any size.

## Install the wandb library

```shell
pip install wandb
```

## Sign up and create an API key

Sign up for a [W&B account](https://wandb.ai/login?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=quickstart). Optionally, use the `wandb login` CLI to configure an API key on your machine. You can skip this step -- W&B will prompt you for an API key the first time you use it.

## Create a machine learning training experiment

In your Python script or notebook, initialize a W&B run with `wandb.init()`.
Specify hyperparameters and log metrics and other information to W&B.

```python
import wandb

# Project that the run is recorded to
project = "my-awesome-project"

# Dictionary with hyperparameters
config = {"epochs" : 1337, "lr" : 3e-4}

# The `with` syntax marks the run as finished upon exiting the `with` block,
# and it marks the run "failed" if there's an exception.
#
# In a notebook, it may be more convenient to write `run = wandb.init()`
# and manually call `run.finish()` instead of using a `with` block.
with wandb.init(project=project, config=config) as run:
    # Training code here

    # Log values to W&B with run.log()
    run.log({"accuracy": 0.9, "loss": 0.1})
```

Visit [wandb.ai/home](https://wandb.ai/home) to view recorded metrics such as accuracy and loss and how they changed during each training step. Each run object appears in the Runs column with generated names.

&nbsp;

# Integrations

W&B [integrates](https://docs.wandb.ai/models/integrations) with popular ML frameworks and libraries making it fast and easy to set up experiment tracking and data versioning inside existing projects.

For developers adding W&B to a new framework, follow the [W&B Developer Guide](https://docs.wandb.ai/models/integrations/add-wandb-to-any-library).

&nbsp;

# W&B Hosting Options

Weights & Biases is available in the cloud or installed on your private infrastructure. Set up a W&B Server in a production environment in one of three ways:

1. [Multi-tenant Cloud](https://docs.wandb.ai/platform/hosting/hosting-options/multi_tenant_cloud?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=hosting): Fully managed platform deployed in W&B’s Google Cloud Platform (GCP) account in GCP’s North America regions.
2. [Dedicated Cloud](https://docs.wandb.ai/platform/hosting/hosting-options/dedicated_cloud?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=hosting): Single-tenant, fully managed platform deployed in W&B’s AWS, GCP, or Azure cloud accounts. Each Dedicated Cloud instance has its own isolated network, compute and storage from other W&B Dedicated Cloud instances.
3. [Self-Managed](https://docs.wandb.ai/platform/hosting/hosting-options/self-managed?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=hosting): Deploy W&B Server on your AWS, GCP, or Azure cloud account or within your on-premises infrastructure.

See the [Hosting documentation](https://docs.wandb.ai/guides/hosting?utm_source=github&utm_medium=code&utm_campaign=wandb&utm_content=hosting) in the W&B Developer Guide for more information.

&nbsp;

# Python Version Support

We are committed to supporting our minimum required Python version for _at least_ six months after its official end-of-life (EOL) date, as defined by the Python Software Foundation. You can find a list of Python EOL dates [here](https://devguide.python.org/versions/).

When we discontinue support for a Python version, we will increment the library’s minor version number to reflect this change.

&nbsp;

# Contribution guidelines

Weights & Biases ❤️ open source, and we welcome contributions from the community! See the [Contribution guide](https://github.com/wandb/wandb/blob/main/CONTRIBUTING.md) for more information on the development workflow and the internals of the wandb library. For wandb bugs and feature requests, visit [GitHub Issues](https://github.com/wandb/wandb/issues) <NAME_EMAIL>.

&nbsp;

# W&B Community

Be a part of the growing W&B Community and interact with the W&B team in our [Discord](https://wandb.me/discord). Stay connected with the latest ML updates and tutorials with [W&B Fully Connected](https://wandb.ai/fully-connected).

&nbsp;

# License

[MIT License](https://github.com/wandb/wandb/blob/main/LICENSE)
