# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field
from typing_extensions import Literal

from wandb._pydantic import GQLId, GQLResult, Typename


class ApiKeyFragment(GQLResult):
    id: GQLId
    name: str
    description: Optional[str]


class CreatedProjectFragment(GQLResult):
    id: GQLId
    name: str
    entity_name: str = Field(alias="entityName")
    description: Optional[str]
    access: Optional[str]
    views: Optional[str]


class LegacySweepFragment(GQLResult):
    typename__: Typename[Literal["Sweep"]] = "Sweep"
    id: GQLId
    name: str
    state: str
    best_loss: Optional[float] = Field(alias="bestLoss")
    config: str


class PageInfoFragment(GQLResult):
    typename__: Typename[Literal["PageInfo"]] = "PageInfo"
    end_cursor: Optional[str] = Field(alias="endCursor")
    has_next_page: bool = Field(alias="hasNextPage")


class ProjectFragment(GQLResult):
    typename__: Typename[Literal["Project"]] = "Project"
    id: GQLId
    name: str
    entity_name: str = Field(alias="entityName")
    created_at: str = Field(alias="createdAt")
    is_benchmark: bool = Field(alias="isBenchmark")


class SweepFragment(GQLResult):
    typename__: Typename[Literal["Sweep"]] = "Sweep"
    id: GQLId
    name: str
    display_name: Optional[str] = Field(alias="displayName")
    method: str
    state: str
    description: Optional[str]
    best_loss: Optional[float] = Field(alias="bestLoss")
    config: str
    created_at: str = Field(alias="createdAt")
    updated_at: Optional[str] = Field(alias="updatedAt")
    run_count: int = Field(alias="runCount")
    run_count_expected: Optional[int] = Field(alias="runCountExpected")


class UserFragment(GQLResult):
    id: GQLId
    name: str
    username: Optional[str]
    email: Optional[str]
    admin: Optional[bool]
    flags: Optional[str]
    entity: Optional[str]
    deleted_at: Optional[str] = Field(alias="deletedAt")
    api_keys: Optional[UserFragmentApiKeys] = Field(alias="apiKeys")
    teams: Optional[UserFragmentTeams]


class UserFragmentApiKeys(GQLResult):
    edges: List[UserFragmentApiKeysEdges]


class UserFragmentApiKeysEdges(GQLResult):
    node: Optional[ApiKeyFragment]


class UserFragmentTeams(GQLResult):
    edges: List[UserFragmentTeamsEdges]


class UserFragmentTeamsEdges(GQLResult):
    node: Optional[UserFragmentTeamsEdgesNode]


class UserFragmentTeamsEdgesNode(GQLResult):
    name: str


class UserInfoFragment(GQLResult):
    id: GQLId
    name: str
    username: Optional[str]
    email: Optional[str]
    admin: Optional[bool]


ApiKeyFragment.model_rebuild()
CreatedProjectFragment.model_rebuild()
LegacySweepFragment.model_rebuild()
PageInfoFragment.model_rebuild()
ProjectFragment.model_rebuild()
SweepFragment.model_rebuild()
UserFragment.model_rebuild()
UserFragmentApiKeys.model_rebuild()
UserFragmentApiKeysEdges.model_rebuild()
ApiKeyFragment.model_rebuild()
UserFragmentTeams.model_rebuild()
UserFragmentTeamsEdges.model_rebuild()
UserFragmentTeamsEdgesNode.model_rebuild()
UserInfoFragment.model_rebuild()
