# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLResult

from .fragments import UserInfoFragment


class CreateUserFromAdmin(GQLResult):
    result: Optional[CreateUserFromAdminResult]


class CreateUserFromAdminResult(GQLResult):
    user: Optional[UserInfoFragment]


CreateUserFromAdmin.model_rebuild()
CreateUserFromAdminResult.model_rebuild()
