# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLResult

from .fragments import SweepFragment


class GetSweep(GQLResult):
    project: Optional[GetSweepProject]


class GetSweepProject(GQLResult):
    sweep: Optional[SweepFragment]


GetSweep.model_rebuild()
GetSweepProject.model_rebuild()
