# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import PageInfoFragment, SweepFragment


class GetSweeps(GQLResult):
    project: Optional[GetSweepsProject]


class GetSweepsProject(GQLResult):
    total_sweeps: int = Field(alias="totalSweeps")
    sweeps: Optional[GetSweepsProjectSweeps]


class GetSweepsProjectSweeps(GQLResult):
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[GetSweepsProjectSweepsEdges]


class GetSweepsProjectSweepsEdges(GQLResult):
    node: SweepFragment


GetSweeps.model_rebuild()
GetSweepsProject.model_rebuild()
GetSweepsProjectSweeps.model_rebuild()
GetSweepsProjectSweepsEdges.model_rebuild()
