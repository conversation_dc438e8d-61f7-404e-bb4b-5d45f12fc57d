# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLResult

from .fragments import LegacySweepFragment


class GetSweepLegacy(GQLResult):
    project: Optional[GetSweepLegacyProject]


class GetSweepLegacyProject(GQLResult):
    sweep: Optional[LegacySweepFragment]


GetSweepLegacy.model_rebuild()
GetSweepLegacyProject.model_rebuild()
