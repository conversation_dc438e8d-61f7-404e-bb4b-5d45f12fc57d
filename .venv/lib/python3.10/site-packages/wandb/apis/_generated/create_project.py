# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLResult

from .fragments import CreatedProjectFragment


class CreateProject(GQLResult):
    result: Optional[CreateProjectResult]


class CreateProjectResult(GQLResult):
    project: Optional[CreatedProjectFragment]
    model: Optional[CreatedProjectFragment]
    inserted: Optional[bool]


CreateProject.model_rebuild()
CreateProjectResult.model_rebuild()
