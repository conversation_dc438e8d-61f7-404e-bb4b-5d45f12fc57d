# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

__all__ = [
    "CREATE_INVITE_GQL",
    "CREATE_PROJECT_GQL",
    "CREATE_SERVICE_ACCOUNT_GQL",
    "CREATE_TEAM_GQL",
    "CREATE_USER_FROM_ADMIN_GQL",
    "DELETE_API_KEY_GQL",
    "DELETE_INVITE_GQL",
    "GENERATE_API_KEY_GQL",
    "GET_DEFAULT_ENTITY_GQL",
    "GET_PROJECTS_GQL",
    "GET_PROJECT_GQL",
    "GET_SWEEPS_GQL",
    "GET_SWEEP_GQL",
    "GET_SWEEP_LEGACY_GQL",
    "GET_TEAM_ENTITY_GQL",
    "GET_VIEWER_GQL",
    "SEARCH_USERS_GQL",
]

GET_PROJECTS_GQL = """
query GetProjects($entity: String, $cursor: String, $perPage: Int = 50) {
  models(entityName: $entity, after: $cursor, first: $perPage) {
    pageInfo {
      ...PageInfoFragment
    }
    edges {
      node {
        ...ProjectFragment
      }
    }
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectFragment on Project {
  __typename
  id
  name
  entityName
  createdAt
  isBenchmark
}
"""

GET_PROJECT_GQL = """
query GetProject($name: String!, $entity: String!) {
  project(name: $name, entityName: $entity) {
    ...ProjectFragment
  }
}

fragment ProjectFragment on Project {
  __typename
  id
  name
  entityName
  createdAt
  isBenchmark
}
"""

CREATE_PROJECT_GQL = """
mutation CreateProject($input: UpsertModelInput!) {
  result: upsertModel(input: $input) {
    project {
      ...CreatedProjectFragment
    }
    model {
      ...CreatedProjectFragment
    }
    inserted
  }
}

fragment CreatedProjectFragment on Project {
  id
  name
  entityName
  description
  access
  views
}
"""

GET_SWEEPS_GQL = """
query GetSweeps($project: String!, $entity: String!, $cursor: String, $perPage: Int = 50) {
  project(name: $project, entityName: $entity) {
    totalSweeps
    sweeps(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFragment
      }
      edges {
        node {
          ...SweepFragment
        }
      }
    }
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment SweepFragment on Sweep {
  __typename
  id
  name
  displayName
  method
  state
  description
  bestLoss
  config
  createdAt
  updatedAt
  runCount
  runCountExpected
}
"""

GET_SWEEP_GQL = """
query GetSweep($name: String!, $project: String, $entity: String) {
  project(name: $project, entityName: $entity) {
    sweep(sweepName: $name) {
      ...SweepFragment
    }
  }
}

fragment SweepFragment on Sweep {
  __typename
  id
  name
  displayName
  method
  state
  description
  bestLoss
  config
  createdAt
  updatedAt
  runCount
  runCountExpected
}
"""

GET_SWEEP_LEGACY_GQL = """
query GetSweepLegacy($name: String!, $project: String, $entity: String) {
  project(name: $project, entityName: $entity) {
    sweep(sweepName: $name) {
      ...LegacySweepFragment
    }
  }
}

fragment LegacySweepFragment on Sweep {
  __typename
  id
  name
  state
  bestLoss
  config
}
"""

GET_TEAM_ENTITY_GQL = """
query GetTeamEntity($name: String!) {
  entity(name: $name) {
    id
    name
    available
    photoUrl
    readOnly
    readOnlyAdmin
    isTeam
    privateOnly
    storageBytes
    codeSavingEnabled
    defaultAccess
    isPaid
    members {
      id
      admin
      pending
      email
      username
      name
      photoUrl
      accountType
      apiKey
    }
  }
}
"""

CREATE_TEAM_GQL = """
mutation CreateTeam($teamName: String!, $teamAdminUserName: String) {
  result: createTeam(
    input: {teamName: $teamName, teamAdminUserName: $teamAdminUserName}
  ) {
    entity {
      id
      name
      available
      photoUrl
      limits
    }
  }
}
"""

CREATE_INVITE_GQL = """
mutation CreateInvite($entity: String!, $email: String, $username: String, $admin: Boolean) {
  result: createInvite(
    input: {entityName: $entity, email: $email, username: $username, admin: $admin}
  ) {
    invite {
      id
      name
      email
      createdAt
      toUser {
        name
      }
    }
  }
}
"""

DELETE_INVITE_GQL = """
mutation DeleteInvite($id: String, $entity: String) {
  result: deleteInvite(input: {id: $id, entityName: $entity}) {
    success
  }
}
"""

CREATE_SERVICE_ACCOUNT_GQL = """
mutation CreateServiceAccount($entity: String!, $description: String!) {
  result: createServiceAccount(
    input: {description: $description, entityName: $entity}
  ) {
    user {
      id
    }
  }
}
"""

SEARCH_USERS_GQL = """
query SearchUsers($query: String) {
  users(query: $query) {
    edges {
      node {
        ...UserFragment
      }
    }
  }
}

fragment ApiKeyFragment on ApiKey {
  id
  name
  description
}

fragment UserFragment on User {
  id
  name
  username
  email
  admin
  flags
  entity
  deletedAt
  apiKeys {
    edges {
      node {
        ...ApiKeyFragment
      }
    }
  }
  teams {
    edges {
      node {
        name
      }
    }
  }
}
"""

GET_VIEWER_GQL = """
query GetViewer {
  viewer {
    ...UserFragment
  }
}

fragment ApiKeyFragment on ApiKey {
  id
  name
  description
}

fragment UserFragment on User {
  id
  name
  username
  email
  admin
  flags
  entity
  deletedAt
  apiKeys {
    edges {
      node {
        ...ApiKeyFragment
      }
    }
  }
  teams {
    edges {
      node {
        name
      }
    }
  }
}
"""

GET_DEFAULT_ENTITY_GQL = """
query GetDefaultEntity {
  viewer {
    id
    entity
  }
}
"""

CREATE_USER_FROM_ADMIN_GQL = """
mutation CreateUserFromAdmin($email: String!, $admin: Boolean) {
  result: createUser(input: {email: $email, admin: $admin}) {
    user {
      ...UserInfoFragment
    }
  }
}

fragment UserInfoFragment on User {
  id
  name
  username
  email
  admin
}
"""

DELETE_API_KEY_GQL = """
mutation DeleteApiKey($id: String!) {
  result: deleteApiKey(input: {id: $id}) {
    success
  }
}
"""

GENERATE_API_KEY_GQL = """
mutation GenerateApiKey($description: String) {
  result: generateApiKey(input: {description: $description}) {
    apiKey {
      ...ApiKeyFragment
    }
  }
}

fragment ApiKeyFragment on ApiKey {
  id
  name
  description
}
"""
