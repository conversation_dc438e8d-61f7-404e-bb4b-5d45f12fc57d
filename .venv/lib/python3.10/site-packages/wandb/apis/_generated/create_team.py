# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLId, GQLResult


class CreateTeam(GQLResult):
    result: Optional[CreateTeamResult]


class CreateTeamResult(GQLResult):
    entity: Optional[CreateTeamResultEntity]


class CreateTeamResultEntity(GQLResult):
    id: GQLId
    name: str
    available: Optional[bool]
    photo_url: Optional[str] = Field(alias="photoUrl")
    limits: Optional[str]


CreateTeam.model_rebuild()
CreateTeamResult.model_rebuild()
