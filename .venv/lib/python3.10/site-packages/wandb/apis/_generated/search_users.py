# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import List, Optional

from wandb._pydantic import GQLResult

from .fragments import UserFragment


class SearchUsers(GQLResult):
    users: Optional[SearchUsersUsers]


class SearchUsersUsers(GQLResult):
    edges: List[SearchUsersUsersEdges]


class SearchUsersUsersEdges(GQLResult):
    node: Optional[UserFragment]


SearchUsers.model_rebuild()
SearchUsersUsers.model_rebuild()
SearchUsersUsersEdges.model_rebuild()
