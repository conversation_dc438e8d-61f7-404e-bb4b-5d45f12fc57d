# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLId, GQLResult


class CreateInvite(GQLResult):
    result: Optional[CreateInviteResult]


class CreateInviteResult(GQLResult):
    invite: Optional[CreateInviteResultInvite]


class CreateInviteResultInvite(GQLResult):
    id: GQLId
    name: str
    email: Optional[str]
    created_at: Optional[str] = Field(alias="createdAt")
    to_user: Optional[CreateInviteResultInviteToUser] = Field(alias="toUser")


class CreateInviteResultInviteToUser(GQLResult):
    name: str


CreateInvite.model_rebuild()
CreateInviteResult.model_rebuild()
CreateInviteResultInvite.model_rebuild()
