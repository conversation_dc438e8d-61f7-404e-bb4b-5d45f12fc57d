# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLId, GQLResult


class CreateServiceAccount(GQLResult):
    result: Optional[CreateServiceAccountResult]


class CreateServiceAccountResult(GQLResult):
    user: Optional[CreateServiceAccountResultUser]


class CreateServiceAccountResultUser(GQLResult):
    id: GQLId


CreateServiceAccount.model_rebuild()
CreateServiceAccountResult.model_rebuild()
