# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ApiKeyFragment


class GenerateApiKey(GQLResult):
    result: Optional[GenerateApiKeyResult]


class GenerateApiKeyResult(GQLResult):
    api_key: Optional[ApiKeyFragment] = Field(alias="apiKey")


GenerateApiKey.model_rebuild()
GenerateApiKeyResult.model_rebuild()
