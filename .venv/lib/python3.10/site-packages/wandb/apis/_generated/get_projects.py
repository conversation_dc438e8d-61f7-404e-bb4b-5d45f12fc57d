# Generated by ariadne-codegen
# Source: tools/graphql_codegen/api/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import PageInfoFragment, ProjectFragment


class GetProjects(GQLResult):
    models: Optional[GetProjectsModels]


class GetProjectsModels(GQLResult):
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[GetProjectsModelsEdges]


class GetProjectsModelsEdges(GQLResult):
    node: Optional[ProjectFragment]


GetProjects.model_rebuild()
GetProjectsModels.model_rebuild()
GetProjectsModelsEdges.model_rebuild()
