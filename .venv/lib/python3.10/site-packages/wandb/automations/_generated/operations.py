# Generated by ariadne-codegen
# Source: tools/graphql_codegen/automations/

__all__ = [
    "CREATE_AUTOMATION_GQL",
    "CREATE_GENERIC_WEBHOOK_INTEGRATION_GQL",
    "DELETE_AUTOMATION_GQL",
    "GET_AUTOMATIONS_BY_ENTITY_GQL",
    "GET_AUTOMATIONS_GQL",
    "INTEGRATIONS_BY_ENTITY_GQL",
    "UPDATE_AUTOMATION_GQL",
]

GET_AUTOMATIONS_GQL = """
query GetAutomations($cursor: String, $perPage: Int) {
  scope: viewer {
    projects(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFields
      }
      edges {
        node {
          ...ProjectTriggersFields
        }
      }
    }
  }
}

fragment ArtifactPortfolioScopeFields on ArtifactPortfolio {
  __typename
  id
  name
}

fragment ArtifactSequenceScopeFields on ArtifactSequence {
  __typename
  id
  name
}

fragment FilterEventFields on FilterEventTriggeringCondition {
  __typename
  eventType
  filter
}

fragment Gene<PERSON>WebhookActionFields on GenericWebhookTriggeredAction {
  __typename
  integration {
    ...WebhookIntegrationFields
  }
  requestPayload
}

fragment NoOpActionFields on NoOpTriggeredAction {
  __typename
  noOp
}

fragment NotificationActionFields on NotificationTriggeredAction {
  __typename
  integration {
    ...SlackIntegrationFields
  }
  title
  message
  severity
}

fragment PageInfoFields on PageInfo {
  endCursor
  hasNextPage
}

fragment ProjectScopeFields on Project {
  __typename
  id
  name
}

fragment ProjectTriggersFields on Project {
  __typename
  triggers {
    ...TriggerFields
  }
}

fragment QueueJobActionFields on QueueJobTriggeredAction {
  __typename
  queue {
    id
    name
  }
  template
}

fragment SlackIntegrationFields on SlackIntegration {
  __typename
  id
  teamName
  channelName
}

fragment TriggerFields on Trigger {
  __typename
  id
  createdAt
  updatedAt
  name
  description
  enabled
  scope {
    ...ProjectScopeFields
    ...ArtifactPortfolioScopeFields
    ...ArtifactSequenceScopeFields
  }
  event: triggeringCondition {
    ...FilterEventFields
  }
  action: triggeredAction {
    ...QueueJobActionFields
    ...NotificationActionFields
    ...GenericWebhookActionFields
    ...NoOpActionFields
  }
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""

GET_AUTOMATIONS_BY_ENTITY_GQL = """
query GetAutomationsByEntity($entity: String!, $cursor: String, $perPage: Int) {
  scope: entity(name: $entity) {
    projects(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFields
      }
      edges {
        node {
          ...ProjectTriggersFields
        }
      }
    }
  }
}

fragment ArtifactPortfolioScopeFields on ArtifactPortfolio {
  __typename
  id
  name
}

fragment ArtifactSequenceScopeFields on ArtifactSequence {
  __typename
  id
  name
}

fragment FilterEventFields on FilterEventTriggeringCondition {
  __typename
  eventType
  filter
}

fragment GenericWebhookActionFields on GenericWebhookTriggeredAction {
  __typename
  integration {
    ...WebhookIntegrationFields
  }
  requestPayload
}

fragment NoOpActionFields on NoOpTriggeredAction {
  __typename
  noOp
}

fragment NotificationActionFields on NotificationTriggeredAction {
  __typename
  integration {
    ...SlackIntegrationFields
  }
  title
  message
  severity
}

fragment PageInfoFields on PageInfo {
  endCursor
  hasNextPage
}

fragment ProjectScopeFields on Project {
  __typename
  id
  name
}

fragment ProjectTriggersFields on Project {
  __typename
  triggers {
    ...TriggerFields
  }
}

fragment QueueJobActionFields on QueueJobTriggeredAction {
  __typename
  queue {
    id
    name
  }
  template
}

fragment SlackIntegrationFields on SlackIntegration {
  __typename
  id
  teamName
  channelName
}

fragment TriggerFields on Trigger {
  __typename
  id
  createdAt
  updatedAt
  name
  description
  enabled
  scope {
    ...ProjectScopeFields
    ...ArtifactPortfolioScopeFields
    ...ArtifactSequenceScopeFields
  }
  event: triggeringCondition {
    ...FilterEventFields
  }
  action: triggeredAction {
    ...QueueJobActionFields
    ...NotificationActionFields
    ...GenericWebhookActionFields
    ...NoOpActionFields
  }
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""

CREATE_AUTOMATION_GQL = """
mutation CreateAutomation($input: CreateFilterTriggerInput!) {
  result: createFilterTrigger(input: $input) {
    trigger {
      ...TriggerFields
    }
  }
}

fragment ArtifactPortfolioScopeFields on ArtifactPortfolio {
  __typename
  id
  name
}

fragment ArtifactSequenceScopeFields on ArtifactSequence {
  __typename
  id
  name
}

fragment FilterEventFields on FilterEventTriggeringCondition {
  __typename
  eventType
  filter
}

fragment GenericWebhookActionFields on GenericWebhookTriggeredAction {
  __typename
  integration {
    ...WebhookIntegrationFields
  }
  requestPayload
}

fragment NoOpActionFields on NoOpTriggeredAction {
  __typename
  noOp
}

fragment NotificationActionFields on NotificationTriggeredAction {
  __typename
  integration {
    ...SlackIntegrationFields
  }
  title
  message
  severity
}

fragment ProjectScopeFields on Project {
  __typename
  id
  name
}

fragment QueueJobActionFields on QueueJobTriggeredAction {
  __typename
  queue {
    id
    name
  }
  template
}

fragment SlackIntegrationFields on SlackIntegration {
  __typename
  id
  teamName
  channelName
}

fragment TriggerFields on Trigger {
  __typename
  id
  createdAt
  updatedAt
  name
  description
  enabled
  scope {
    ...ProjectScopeFields
    ...ArtifactPortfolioScopeFields
    ...ArtifactSequenceScopeFields
  }
  event: triggeringCondition {
    ...FilterEventFields
  }
  action: triggeredAction {
    ...QueueJobActionFields
    ...NotificationActionFields
    ...GenericWebhookActionFields
    ...NoOpActionFields
  }
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""

UPDATE_AUTOMATION_GQL = """
mutation UpdateAutomation($input: UpdateFilterTriggerInput!) {
  result: updateFilterTrigger(input: $input) {
    trigger {
      ...TriggerFields
    }
  }
}

fragment ArtifactPortfolioScopeFields on ArtifactPortfolio {
  __typename
  id
  name
}

fragment ArtifactSequenceScopeFields on ArtifactSequence {
  __typename
  id
  name
}

fragment FilterEventFields on FilterEventTriggeringCondition {
  __typename
  eventType
  filter
}

fragment GenericWebhookActionFields on GenericWebhookTriggeredAction {
  __typename
  integration {
    ...WebhookIntegrationFields
  }
  requestPayload
}

fragment NoOpActionFields on NoOpTriggeredAction {
  __typename
  noOp
}

fragment NotificationActionFields on NotificationTriggeredAction {
  __typename
  integration {
    ...SlackIntegrationFields
  }
  title
  message
  severity
}

fragment ProjectScopeFields on Project {
  __typename
  id
  name
}

fragment QueueJobActionFields on QueueJobTriggeredAction {
  __typename
  queue {
    id
    name
  }
  template
}

fragment SlackIntegrationFields on SlackIntegration {
  __typename
  id
  teamName
  channelName
}

fragment TriggerFields on Trigger {
  __typename
  id
  createdAt
  updatedAt
  name
  description
  enabled
  scope {
    ...ProjectScopeFields
    ...ArtifactPortfolioScopeFields
    ...ArtifactSequenceScopeFields
  }
  event: triggeringCondition {
    ...FilterEventFields
  }
  action: triggeredAction {
    ...QueueJobActionFields
    ...NotificationActionFields
    ...GenericWebhookActionFields
    ...NoOpActionFields
  }
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""

DELETE_AUTOMATION_GQL = """
mutation DeleteAutomation($id: ID!) {
  result: deleteTrigger(input: {triggerID: $id}) {
    success
  }
}
"""

INTEGRATIONS_BY_ENTITY_GQL = """
query IntegrationsByEntity($entity: String!, $cursor: String, $perPage: Int) {
  entity(name: $entity) {
    integrations(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFields
      }
      edges {
        node {
          __typename
          ...SlackIntegrationFields
          ...WebhookIntegrationFields
        }
      }
    }
  }
}

fragment PageInfoFields on PageInfo {
  endCursor
  hasNextPage
}

fragment SlackIntegrationFields on SlackIntegration {
  __typename
  id
  teamName
  channelName
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""

CREATE_GENERIC_WEBHOOK_INTEGRATION_GQL = """
mutation CreateGenericWebhookIntegration($input: CreateGenericWebhookIntegrationInput!) {
  createGenericWebhookIntegration(input: $input) {
    integration {
      __typename
      ...WebhookIntegrationFields
    }
  }
}

fragment WebhookIntegrationFields on GenericWebhookIntegration {
  __typename
  id
  name
  urlEndpoint
}
"""
