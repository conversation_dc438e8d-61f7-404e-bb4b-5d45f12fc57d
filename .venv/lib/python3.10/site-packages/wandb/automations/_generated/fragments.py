# Generated by ariadne-codegen
# Source: tools/graphql_codegen/automations/

from __future__ import annotations

from datetime import datetime
from typing import List, Optional, Union

from pydantic import Field
from typing_extensions import Literal

from wandb._pydantic import GQLId, GQLResult, Typename

from .enums import AlertSeverity, EventTriggeringConditionType


class ArtifactPortfolioScopeFields(GQLResult):
    typename__: Typename[Literal["ArtifactPortfolio"]] = "ArtifactPortfolio"
    id: GQLId
    name: str


class ArtifactSequenceScopeFields(GQLResult):
    typename__: Typename[Literal["ArtifactSequence"]] = "ArtifactSequence"
    id: GQLId
    name: str


class FilterEventFields(GQLResult):
    typename__: Typename[Literal["FilterEventTriggeringCondition"]] = (
        "FilterEventTriggeringCondition"
    )
    event_type: EventTriggeringConditionType = Field(alias="eventType")
    filter: str


class WebhookIntegrationFields(GQLResult):
    typename__: Typename[Literal["GenericWebhookIntegration"]] = (
        "GenericWebhookIntegration"
    )
    id: GQLId
    name: str
    url_endpoint: str = Field(alias="urlEndpoint")


class GenericWebhookActionFields(GQLResult):
    typename__: Typename[Literal["GenericWebhookTriggeredAction"]] = (
        "GenericWebhookTriggeredAction"
    )
    integration: Union[
        GenericWebhookActionFieldsIntegrationIntegration, WebhookIntegrationFields
    ] = Field(discriminator="typename__")
    request_payload: Optional[str] = Field(alias="requestPayload")


class GenericWebhookActionFieldsIntegrationIntegration(GQLResult):
    typename__: Typename[
        Literal["GitHubOAuthIntegration", "Integration", "SlackIntegration"]
    ]


class NoOpActionFields(GQLResult):
    typename__: Typename[Literal["NoOpTriggeredAction"]] = "NoOpTriggeredAction"
    no_op: Optional[bool] = Field(alias="noOp")


class SlackIntegrationFields(GQLResult):
    typename__: Typename[Literal["SlackIntegration"]] = "SlackIntegration"
    id: GQLId
    team_name: str = Field(alias="teamName")
    channel_name: str = Field(alias="channelName")


class NotificationActionFields(GQLResult):
    typename__: Typename[Literal["NotificationTriggeredAction"]] = (
        "NotificationTriggeredAction"
    )
    integration: Union[
        NotificationActionFieldsIntegrationIntegration, SlackIntegrationFields
    ] = Field(discriminator="typename__")
    title: Optional[str]
    message: Optional[str]
    severity: Optional[AlertSeverity]


class NotificationActionFieldsIntegrationIntegration(GQLResult):
    typename__: Typename[
        Literal["GenericWebhookIntegration", "GitHubOAuthIntegration", "Integration"]
    ]


class PageInfoFields(GQLResult):
    end_cursor: Optional[str] = Field(alias="endCursor")
    has_next_page: bool = Field(alias="hasNextPage")


class ProjectScopeFields(GQLResult):
    typename__: Typename[Literal["Project"]] = "Project"
    id: GQLId
    name: str


class QueueJobActionFields(GQLResult):
    typename__: Typename[Literal["QueueJobTriggeredAction"]] = "QueueJobTriggeredAction"
    queue: Optional[QueueJobActionFieldsQueue]
    template: str


class QueueJobActionFieldsQueue(GQLResult):
    id: GQLId
    name: str


class TriggerFields(GQLResult):
    typename__: Typename[Literal["Trigger"]] = "Trigger"
    id: GQLId
    created_at: datetime = Field(alias="createdAt")
    updated_at: Optional[datetime] = Field(alias="updatedAt")
    name: str
    description: Optional[str]
    enabled: bool
    scope: Union[
        ProjectScopeFields, ArtifactSequenceScopeFields, ArtifactPortfolioScopeFields
    ] = Field(discriminator="typename__")
    event: FilterEventFields
    action: Union[
        QueueJobActionFields,
        NotificationActionFields,
        GenericWebhookActionFields,
        NoOpActionFields,
    ] = Field(discriminator="typename__")


class ProjectTriggersFields(GQLResult):
    typename__: Typename[Literal["Project"]] = "Project"
    triggers: List[TriggerFields]


ArtifactPortfolioScopeFields.model_rebuild()
ArtifactSequenceScopeFields.model_rebuild()
FilterEventFields.model_rebuild()
WebhookIntegrationFields.model_rebuild()
GenericWebhookActionFields.model_rebuild()
GenericWebhookActionFieldsIntegrationIntegration.model_rebuild()
WebhookIntegrationFields.model_rebuild()
NoOpActionFields.model_rebuild()
SlackIntegrationFields.model_rebuild()
NotificationActionFields.model_rebuild()
NotificationActionFieldsIntegrationIntegration.model_rebuild()
SlackIntegrationFields.model_rebuild()
PageInfoFields.model_rebuild()
ProjectScopeFields.model_rebuild()
QueueJobActionFields.model_rebuild()
QueueJobActionFieldsQueue.model_rebuild()
TriggerFields.model_rebuild()
ProjectScopeFields.model_rebuild()
ArtifactSequenceScopeFields.model_rebuild()
ArtifactPortfolioScopeFields.model_rebuild()
FilterEventFields.model_rebuild()
QueueJobActionFields.model_rebuild()
NotificationActionFields.model_rebuild()
GenericWebhookActionFields.model_rebuild()
NoOpActionFields.model_rebuild()
ProjectTriggersFields.model_rebuild()
TriggerFields.model_rebuild()
