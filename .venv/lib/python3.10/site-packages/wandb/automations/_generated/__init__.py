# Generated by ariadne-codegen

__all__ = [
    "CREATE_AUTOMATION_GQL",
    "CREATE_GENERIC_WEBHOOK_INTEGRATION_GQL",
    "DELETE_AUTOMATION_GQL",
    "GET_AUTOMATIONS_BY_ENTITY_GQL",
    "GET_AUTOMATIONS_GQL",
    "INTEGRATIONS_BY_ENTITY_GQL",
    "UPDATE_AUTOMATION_GQL",
    "GetAutomations",
    "GetAutomationsByEntity",
    "CreateAutomation",
    "UpdateAutomation",
    "DeleteAutomation",
    "IntegrationsByEntity",
    "CreateGenericWebhookIntegration",
    "CreateFilterTriggerInput",
    "CreateGenericWebhookIntegrationInput",
    "GenericWebhookActionInput",
    "NoOpTriggeredActionInput",
    "NotificationActionInput",
    "QueueJobActionInput",
    "TriggeredActionConfig",
    "UpdateFilterTriggerInput",
    "ArtifactPortfolioScopeFields",
    "ArtifactSequenceScopeFields",
    "FilterEventFields",
    "GenericWebhookActionFields",
    "NoOpActionFields",
    "NotificationActionFields",
    "PageInfoFields",
    "ProjectScopeFields",
    "ProjectTriggersFields",
    "QueueJobActionFields",
    "SlackIntegrationFields",
    "TriggerFields",
    "WebhookIntegrationFields",
    "AlertSeverity",
    "EventTriggeringConditionType",
    "TriggerScopeType",
    "TriggeredActionType",
]
from .create_automation import CreateAutomation
from .create_generic_webhook_integration import CreateGenericWebhookIntegration
from .delete_automation import DeleteAutomation
from .enums import (
    AlertSeverity,
    EventTriggeringConditionType,
    TriggeredActionType,
    TriggerScopeType,
)
from .fragments import (
    ArtifactPortfolioScopeFields,
    ArtifactSequenceScopeFields,
    FilterEventFields,
    GenericWebhookActionFields,
    NoOpActionFields,
    NotificationActionFields,
    PageInfoFields,
    ProjectScopeFields,
    ProjectTriggersFields,
    QueueJobActionFields,
    SlackIntegrationFields,
    TriggerFields,
    WebhookIntegrationFields,
)
from .get_automations import GetAutomations
from .get_automations_by_entity import GetAutomationsByEntity
from .input_types import (
    CreateFilterTriggerInput,
    CreateGenericWebhookIntegrationInput,
    GenericWebhookActionInput,
    NoOpTriggeredActionInput,
    NotificationActionInput,
    QueueJobActionInput,
    TriggeredActionConfig,
    UpdateFilterTriggerInput,
)
from .integrations_by_entity import IntegrationsByEntity
from .operations import (
    CREATE_AUTOMATION_GQL,
    CREATE_GENERIC_WEBHOOK_INTEGRATION_GQL,
    DELETE_AUTOMATION_GQL,
    GET_AUTOMATIONS_BY_ENTITY_GQL,
    GET_AUTOMATIONS_GQL,
    INTEGRATIONS_BY_ENTITY_GQL,
    UPDATE_AUTOMATION_GQL,
)
from .update_automation import UpdateAutomation
