# Generated by ariadne-codegen
# Source: tools/graphql_codegen/automations/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import PageInfoFields, ProjectTriggersFields


class GetAutomationsByEntity(GQLResult):
    scope: Optional[GetAutomationsByEntityScope]


class GetAutomationsByEntityScope(GQLResult):
    projects: Optional[GetAutomationsByEntityScopeProjects]


class GetAutomationsByEntityScopeProjects(GQLResult):
    page_info: PageInfoFields = Field(alias="pageInfo")
    edges: List[GetAutomationsByEntityScopeProjectsEdges]


class GetAutomationsByEntityScopeProjectsEdges(GQLResult):
    node: Optional[ProjectTriggersFields]


GetAutomationsByEntity.model_rebuild()
GetAutomationsByEntityScope.model_rebuild()
GetAutomationsByEntityScopeProjects.model_rebuild()
GetAutomationsByEntityScopeProjectsEdges.model_rebuild()
