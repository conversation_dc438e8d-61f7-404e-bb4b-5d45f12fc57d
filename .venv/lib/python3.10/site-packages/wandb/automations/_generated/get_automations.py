# Generated by ariadne-codegen
# Source: tools/graphql_codegen/automations/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import PageInfoFields, ProjectTriggersFields


class GetAutomations(GQLResult):
    scope: Optional[GetAutomationsScope]


class GetAutomationsScope(GQLResult):
    projects: Optional[GetAutomationsScopeProjects]


class GetAutomationsScopeProjects(GQLResult):
    page_info: PageInfoFields = Field(alias="pageInfo")
    edges: List[GetAutomationsScopeProjectsEdges]


class GetAutomationsScopeProjectsEdges(GQLResult):
    node: Optional[ProjectTriggersFields]


GetAutomations.model_rebuild()
GetAutomationsScope.model_rebuild()
GetAutomationsScopeProjects.model_rebuild()
GetAutomationsScopeProjectsEdges.model_rebuild()
