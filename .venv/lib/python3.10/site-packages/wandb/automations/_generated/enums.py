# Generated by ariadne-codegen
# Source: core/api/graphql/schemas/schema-latest.graphql

from __future__ import annotations

from enum import Enum


class AlertSeverity(str, Enum):
    INFO = "INFO"
    WARN = "WARN"
    ERROR = "ERROR"


class TriggerScopeType(str, Enum):
    PROJECT = "PROJECT"
    ARTIFACT_COLLECTION = "ARTIFACT_COLLECTION"


class EventTriggeringConditionType(str, Enum):
    CREATE_ARTIFACT = "CREATE_ARTIFACT"
    UPDATE_ARTIFACT_ALIAS = "UPDATE_ARTIFACT_ALIAS"
    ADD_ARTIFACT_ALIAS = "ADD_ARTIFACT_ALIAS"
    ADD_ARTIFACT_TAG = "ADD_ARTIFACT_TAG"
    LINK_MODEL = "LINK_MODEL"
    RUN_METRIC = "RUN_METRIC"
    RUN_METRIC_CHANGE = "RUN_METRIC_CHANGE"
    RUN_STATE = "RUN_STATE"
    RUN_METRIC_ZSCORE = "RUN_METRIC_ZSCORE"


class TriggeredActionType(str, Enum):
    QUEUE_JOB = "QUEUE_JOB"
    NOTIFICATION = "NOTIFICATION"
    GENERIC_WEBHOOK = "GENERIC_WEBHOOK"
    NO_OP = "NO_OP"
