# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wandb/proto/wandb_api.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from wandb.proto import wandb_settings_pb2 as wandb_dot_proto_dot_wandb__settings__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bwandb/proto/wandb_api.proto\x12\x0ewandb_internal\x1a wandb/proto/wandb_settings.proto\"B\n\x14ServerApiInitRequest\x12*\n\x08settings\x18\x01 \x01(\x0b\x32\x18.wandb_internal.Settings\"b\n\nApiRequest\x12I\n\x18read_run_history_request\x18\x01 \x01(\x0b\x32%.wandb_internal.ReadRunHistoryRequestH\x00\x42\t\n\x07request\".\n\x15ServerApiInitResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\"\xfb\x01\n\x0b\x41piResponse\x12K\n\x19read_run_history_response\x18\x01 \x01(\x0b\x32&.wandb_internal.ReadRunHistoryResponseH\x00\x12S\n\x1d\x64ownload_run_history_response\x18\x03 \x01(\x0b\x32*.wandb_internal.DownloadRunHistoryResponseH\x00\x12>\n\x12\x61pi_error_response\x18\x02 \x01(\x0b\x32 .wandb_internal.ApiErrorResponseH\x00\x42\n\n\x08response\"f\n\x10\x41piErrorResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x32\n\nerror_type\x18\x02 \x01(\x0e\x32\x19.wandb_internal.ErrorTypeH\x00\x88\x01\x01\x42\r\n\x0b_error_type\"\xb2\x02\n\x15ReadRunHistoryRequest\x12\x43\n\x15scan_run_history_init\x18\x01 \x01(\x0b\x32\".wandb_internal.ScanRunHistoryInitH\x00\x12:\n\x10scan_run_history\x18\x02 \x01(\x0b\x32\x1e.wandb_internal.ScanRunHistoryH\x00\x12I\n\x18scan_run_history_cleanup\x18\x03 \x01(\x0b\x32%.wandb_internal.ScanRunHistoryCleanupH\x00\x12\x42\n\x14\x64ownload_run_history\x18\x04 \x01(\x0b\x32\".wandb_internal.DownloadRunHistoryH\x00\x42\t\n\x07request\"\xcb\x02\n\x16ReadRunHistoryResponse\x12K\n\x15scan_run_history_init\x18\x01 \x01(\x0b\x32*.wandb_internal.ScanRunHistoryInitResponseH\x00\x12\x39\n\x0brun_history\x18\x02 \x01(\x0b\x32\".wandb_internal.RunHistoryResponseH\x00\x12Q\n\x18scan_run_history_cleanup\x18\x03 \x01(\x0b\x32-.wandb_internal.ScanRunHistoryCleanupResponseH\x00\x12J\n\x14\x64ownload_run_history\x18\x04 \x01(\x0b\x32*.wandb_internal.DownloadRunHistoryResponseH\x00\x42\n\n\x08response\"f\n\x12ScanRunHistoryInit\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0f\n\x07project\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x0c\n\x04keys\x18\x04 \x03(\t\x12\x11\n\tuse_cache\x18\x05 \x01(\x08\"0\n\x1aScanRunHistoryInitResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\x05\"H\n\x0eScanRunHistory\x12\x10\n\x08min_step\x18\x01 \x01(\x03\x12\x10\n\x08max_step\x18\x02 \x01(\x03\x12\x12\n\nrequest_id\x18\x03 \x01(\x05\"F\n\x12RunHistoryResponse\x12\x30\n\x0chistory_rows\x18\x01 \x03(\x0b\x32\x1a.wandb_internal.HistoryRow\"G\n\nHistoryRow\x12\x39\n\rhistory_items\x18\x01 \x03(\x0b\x32\".wandb_internal.ParquetHistoryItem\"5\n\x12ParquetHistoryItem\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x12\n\nvalue_json\x18\x10 \x01(\t\"+\n\x15ScanRunHistoryCleanup\x12\x12\n\nrequest_id\x18\x01 \x01(\x05\"\x1f\n\x1dScanRunHistoryCleanupResponse\"}\n\x12\x44ownloadRunHistory\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0f\n\x07project\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x14\n\x0c\x64ownload_dir\x18\x04 \x01(\t\x12 \n\x18require_complete_history\x18\x05 \x01(\x08\"L\n\x1a\x44ownloadRunHistoryResponse\x12\x12\n\nfile_names\x18\x01 \x03(\t\x12\x1a\n\x12\x63ontains_live_data\x18\x02 \x01(\x08\"\x1b\n\x19IncompleteRunHistoryError*@\n\tErrorType\x12\x11\n\rUNKNOWN_ERROR\x10\x00\x12 \n\x1cINCOMPLETE_RUN_HISTORY_ERROR\x10\x01\x42\x1bZ\x19\x63ore/pkg/service_go_protob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'wandb.proto.wandb_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\031core/pkg/service_go_proto'
  _globals['_ERRORTYPE']._serialized_start=2038
  _globals['_ERRORTYPE']._serialized_end=2102
  _globals['_SERVERAPIINITREQUEST']._serialized_start=81
  _globals['_SERVERAPIINITREQUEST']._serialized_end=147
  _globals['_APIREQUEST']._serialized_start=149
  _globals['_APIREQUEST']._serialized_end=247
  _globals['_SERVERAPIINITRESPONSE']._serialized_start=249
  _globals['_SERVERAPIINITRESPONSE']._serialized_end=295
  _globals['_APIRESPONSE']._serialized_start=298
  _globals['_APIRESPONSE']._serialized_end=549
  _globals['_APIERRORRESPONSE']._serialized_start=551
  _globals['_APIERRORRESPONSE']._serialized_end=653
  _globals['_READRUNHISTORYREQUEST']._serialized_start=656
  _globals['_READRUNHISTORYREQUEST']._serialized_end=962
  _globals['_READRUNHISTORYRESPONSE']._serialized_start=965
  _globals['_READRUNHISTORYRESPONSE']._serialized_end=1296
  _globals['_SCANRUNHISTORYINIT']._serialized_start=1298
  _globals['_SCANRUNHISTORYINIT']._serialized_end=1400
  _globals['_SCANRUNHISTORYINITRESPONSE']._serialized_start=1402
  _globals['_SCANRUNHISTORYINITRESPONSE']._serialized_end=1450
  _globals['_SCANRUNHISTORY']._serialized_start=1452
  _globals['_SCANRUNHISTORY']._serialized_end=1524
  _globals['_RUNHISTORYRESPONSE']._serialized_start=1526
  _globals['_RUNHISTORYRESPONSE']._serialized_end=1596
  _globals['_HISTORYROW']._serialized_start=1598
  _globals['_HISTORYROW']._serialized_end=1669
  _globals['_PARQUETHISTORYITEM']._serialized_start=1671
  _globals['_PARQUETHISTORYITEM']._serialized_end=1724
  _globals['_SCANRUNHISTORYCLEANUP']._serialized_start=1726
  _globals['_SCANRUNHISTORYCLEANUP']._serialized_end=1769
  _globals['_SCANRUNHISTORYCLEANUPRESPONSE']._serialized_start=1771
  _globals['_SCANRUNHISTORYCLEANUPRESPONSE']._serialized_end=1802
  _globals['_DOWNLOADRUNHISTORY']._serialized_start=1804
  _globals['_DOWNLOADRUNHISTORY']._serialized_end=1929
  _globals['_DOWNLOADRUNHISTORYRESPONSE']._serialized_start=1931
  _globals['_DOWNLOADRUNHISTORYRESPONSE']._serialized_end=2007
  _globals['_INCOMPLETERUNHISTORYERROR']._serialized_start=2009
  _globals['_INCOMPLETERUNHISTORYERROR']._serialized_end=2036
# @@protoc_insertion_point(module_scope)
