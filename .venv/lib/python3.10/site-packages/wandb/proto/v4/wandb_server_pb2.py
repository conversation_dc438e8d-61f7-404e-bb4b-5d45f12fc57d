# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wandb/proto/wandb_server.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from wandb.proto import wandb_api_pb2 as wandb_dot_proto_dot_wandb__api__pb2
from wandb.proto import wandb_base_pb2 as wandb_dot_proto_dot_wandb__base__pb2
from wandb.proto import wandb_internal_pb2 as wandb_dot_proto_dot_wandb__internal__pb2
from wandb.proto import wandb_settings_pb2 as wandb_dot_proto_dot_wandb__settings__pb2
from wandb.proto import wandb_sync_pb2 as wandb_dot_proto_dot_wandb__sync__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1ewandb/proto/wandb_server.proto\x12\x0ewandb_internal\x1a\x1bwandb/proto/wandb_api.proto\x1a\x1cwandb/proto/wandb_base.proto\x1a wandb/proto/wandb_internal.proto\x1a wandb/proto/wandb_settings.proto\x1a\x1cwandb/proto/wandb_sync.proto\"k\n\x19ServerAuthenticateRequest\x12\x0f\n\x07\x61pi_key\x18\x01 \x01(\t\x12\x10\n\x08\x62\x61se_url\x18\x02 \x01(\t\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"w\n\x1aServerAuthenticateResponse\x12\x16\n\x0e\x64\x65\x66\x61ult_entity\x18\x01 \x01(\t\x12\x14\n\x0c\x65rror_status\x18\x02 \x01(\t\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"D\n\x15ServerShutdownRequest\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x18\n\x16ServerShutdownResponse\"B\n\x13ServerStatusRequest\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x16\n\x14ServerStatusResponse\"r\n\x17ServerInformInitRequest\x12*\n\x08settings\x18\x01 \x01(\x0b\x32\x18.wandb_internal.Settings\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x1a\n\x18ServerInformInitResponse\"H\n\x19ServerInformFinishRequest\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x1c\n\x1aServerInformFinishResponse\"H\n\x19ServerInformAttachRequest\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"u\n\x1aServerInformAttachResponse\x12*\n\x08settings\x18\x01 \x01(\x0b\x32\x18.wandb_internal.Settings\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"H\n\x19ServerInformDetachRequest\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x1c\n\x1aServerInformDetachResponse\"]\n\x1bServerInformTeardownRequest\x12\x11\n\texit_code\x18\x01 \x01(\x05\x12+\n\x05_info\x18\xc8\x01 \x01(\x0b\x32\x1b.wandb_internal._RecordInfo\"\x1e\n\x1cServerInformTeardownResponse\")\n\x13ServerCancelRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\"\x9a\x07\n\rServerRequest\x12\x12\n\nrequest_id\x18\n \x01(\t\x12\x35\n\x06\x63\x61ncel\x18\x10 \x01(\x0b\x32#.wandb_internal.ServerCancelRequestH\x00\x12\x30\n\x0erecord_publish\x18\x01 \x01(\x0b\x32\x16.wandb_internal.RecordH\x00\x12\x34\n\x12record_communicate\x18\x02 \x01(\x0b\x32\x16.wandb_internal.RecordH\x00\x12>\n\x0binform_init\x18\x03 \x01(\x0b\x32\'.wandb_internal.ServerInformInitRequestH\x00\x12\x42\n\rinform_finish\x18\x04 \x01(\x0b\x32).wandb_internal.ServerInformFinishRequestH\x00\x12\x42\n\rinform_attach\x18\x05 \x01(\x0b\x32).wandb_internal.ServerInformAttachRequestH\x00\x12\x42\n\rinform_detach\x18\x06 \x01(\x0b\x32).wandb_internal.ServerInformDetachRequestH\x00\x12\x46\n\x0finform_teardown\x18\x07 \x01(\x0b\x32+.wandb_internal.ServerInformTeardownRequestH\x00\x12\x41\n\x0c\x61uthenticate\x18\t \x01(\x0b\x32).wandb_internal.ServerAuthenticateRequestH\x00\x12:\n\tinit_sync\x18\x0b \x01(\x0b\x32%.wandb_internal.ServerInitSyncRequestH\x00\x12\x31\n\x04sync\x18\x0c \x01(\x0b\x32!.wandb_internal.ServerSyncRequestH\x00\x12>\n\x0bsync_status\x18\r \x01(\x0b\x32\'.wandb_internal.ServerSyncStatusRequestH\x00\x12@\n\x10\x61pi_init_request\x18\x0e \x01(\x0b\x32$.wandb_internal.ServerApiInitRequestH\x00\x12\x31\n\x0b\x61pi_request\x18\x0f \x01(\x0b\x32\x1a.wandb_internal.ApiRequestH\x00\x42\x15\n\x13server_request_typeJ\x04\x08\x08\x10\t\"\x91\x07\n\x0eServerResponse\x12\x12\n\nrequest_id\x18\n \x01(\t\x12\x34\n\x12result_communicate\x18\x02 \x01(\x0b\x32\x16.wandb_internal.ResultH\x00\x12H\n\x14inform_init_response\x18\x03 \x01(\x0b\x32(.wandb_internal.ServerInformInitResponseH\x00\x12L\n\x16inform_finish_response\x18\x04 \x01(\x0b\x32*.wandb_internal.ServerInformFinishResponseH\x00\x12L\n\x16inform_attach_response\x18\x05 \x01(\x0b\x32*.wandb_internal.ServerInformAttachResponseH\x00\x12L\n\x16inform_detach_response\x18\x06 \x01(\x0b\x32*.wandb_internal.ServerInformDetachResponseH\x00\x12P\n\x18inform_teardown_response\x18\x07 \x01(\x0b\x32,.wandb_internal.ServerInformTeardownResponseH\x00\x12K\n\x15\x61uthenticate_response\x18\t \x01(\x0b\x32*.wandb_internal.ServerAuthenticateResponseH\x00\x12\x44\n\x12init_sync_response\x18\x0b \x01(\x0b\x32&.wandb_internal.ServerInitSyncResponseH\x00\x12;\n\rsync_response\x18\x0c \x01(\x0b\x32\".wandb_internal.ServerSyncResponseH\x00\x12H\n\x14sync_status_response\x18\r \x01(\x0b\x32(.wandb_internal.ServerSyncStatusResponseH\x00\x12\x42\n\x11\x61pi_init_response\x18\x0e \x01(\x0b\x32%.wandb_internal.ServerApiInitResponseH\x00\x12\x33\n\x0c\x61pi_response\x18\x0f \x01(\x0b\x32\x1b.wandb_internal.ApiResponseH\x00\x42\x16\n\x14server_response_typeJ\x04\x08\x08\x10\tB\x1bZ\x19\x63ore/pkg/service_go_protob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'wandb.proto.wandb_server_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\031core/pkg/service_go_proto'
  _SERVERAUTHENTICATEREQUEST._serialized_start=207
  _SERVERAUTHENTICATEREQUEST._serialized_end=314
  _SERVERAUTHENTICATERESPONSE._serialized_start=316
  _SERVERAUTHENTICATERESPONSE._serialized_end=435
  _SERVERSHUTDOWNREQUEST._serialized_start=437
  _SERVERSHUTDOWNREQUEST._serialized_end=505
  _SERVERSHUTDOWNRESPONSE._serialized_start=507
  _SERVERSHUTDOWNRESPONSE._serialized_end=531
  _SERVERSTATUSREQUEST._serialized_start=533
  _SERVERSTATUSREQUEST._serialized_end=599
  _SERVERSTATUSRESPONSE._serialized_start=601
  _SERVERSTATUSRESPONSE._serialized_end=623
  _SERVERINFORMINITREQUEST._serialized_start=625
  _SERVERINFORMINITREQUEST._serialized_end=739
  _SERVERINFORMINITRESPONSE._serialized_start=741
  _SERVERINFORMINITRESPONSE._serialized_end=767
  _SERVERINFORMFINISHREQUEST._serialized_start=769
  _SERVERINFORMFINISHREQUEST._serialized_end=841
  _SERVERINFORMFINISHRESPONSE._serialized_start=843
  _SERVERINFORMFINISHRESPONSE._serialized_end=871
  _SERVERINFORMATTACHREQUEST._serialized_start=873
  _SERVERINFORMATTACHREQUEST._serialized_end=945
  _SERVERINFORMATTACHRESPONSE._serialized_start=947
  _SERVERINFORMATTACHRESPONSE._serialized_end=1064
  _SERVERINFORMDETACHREQUEST._serialized_start=1066
  _SERVERINFORMDETACHREQUEST._serialized_end=1138
  _SERVERINFORMDETACHRESPONSE._serialized_start=1140
  _SERVERINFORMDETACHRESPONSE._serialized_end=1168
  _SERVERINFORMTEARDOWNREQUEST._serialized_start=1170
  _SERVERINFORMTEARDOWNREQUEST._serialized_end=1263
  _SERVERINFORMTEARDOWNRESPONSE._serialized_start=1265
  _SERVERINFORMTEARDOWNRESPONSE._serialized_end=1295
  _SERVERCANCELREQUEST._serialized_start=1297
  _SERVERCANCELREQUEST._serialized_end=1338
  _SERVERREQUEST._serialized_start=1341
  _SERVERREQUEST._serialized_end=2263
  _SERVERRESPONSE._serialized_start=2266
  _SERVERRESPONSE._serialized_end=3179
# @@protoc_insertion_point(module_scope)
