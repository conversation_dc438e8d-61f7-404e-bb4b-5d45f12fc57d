# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wandb/proto/wandb_api.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from wandb.proto import wandb_settings_pb2 as wandb_dot_proto_dot_wandb__settings__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bwandb/proto/wandb_api.proto\x12\x0ewandb_internal\x1a wandb/proto/wandb_settings.proto\"B\n\x14ServerApiInitRequest\x12*\n\x08settings\x18\x01 \x01(\x0b\x32\x18.wandb_internal.Settings\"b\n\nApiRequest\x12I\n\x18read_run_history_request\x18\x01 \x01(\x0b\x32%.wandb_internal.ReadRunHistoryRequestH\x00\x42\t\n\x07request\".\n\x15ServerApiInitResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\"\xfb\x01\n\x0b\x41piResponse\x12K\n\x19read_run_history_response\x18\x01 \x01(\x0b\x32&.wandb_internal.ReadRunHistoryResponseH\x00\x12S\n\x1d\x64ownload_run_history_response\x18\x03 \x01(\x0b\x32*.wandb_internal.DownloadRunHistoryResponseH\x00\x12>\n\x12\x61pi_error_response\x18\x02 \x01(\x0b\x32 .wandb_internal.ApiErrorResponseH\x00\x42\n\n\x08response\"f\n\x10\x41piErrorResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x32\n\nerror_type\x18\x02 \x01(\x0e\x32\x19.wandb_internal.ErrorTypeH\x00\x88\x01\x01\x42\r\n\x0b_error_type\"\xb2\x02\n\x15ReadRunHistoryRequest\x12\x43\n\x15scan_run_history_init\x18\x01 \x01(\x0b\x32\".wandb_internal.ScanRunHistoryInitH\x00\x12:\n\x10scan_run_history\x18\x02 \x01(\x0b\x32\x1e.wandb_internal.ScanRunHistoryH\x00\x12I\n\x18scan_run_history_cleanup\x18\x03 \x01(\x0b\x32%.wandb_internal.ScanRunHistoryCleanupH\x00\x12\x42\n\x14\x64ownload_run_history\x18\x04 \x01(\x0b\x32\".wandb_internal.DownloadRunHistoryH\x00\x42\t\n\x07request\"\xcb\x02\n\x16ReadRunHistoryResponse\x12K\n\x15scan_run_history_init\x18\x01 \x01(\x0b\x32*.wandb_internal.ScanRunHistoryInitResponseH\x00\x12\x39\n\x0brun_history\x18\x02 \x01(\x0b\x32\".wandb_internal.RunHistoryResponseH\x00\x12Q\n\x18scan_run_history_cleanup\x18\x03 \x01(\x0b\x32-.wandb_internal.ScanRunHistoryCleanupResponseH\x00\x12J\n\x14\x64ownload_run_history\x18\x04 \x01(\x0b\x32*.wandb_internal.DownloadRunHistoryResponseH\x00\x42\n\n\x08response\"f\n\x12ScanRunHistoryInit\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0f\n\x07project\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x0c\n\x04keys\x18\x04 \x03(\t\x12\x11\n\tuse_cache\x18\x05 \x01(\x08\"0\n\x1aScanRunHistoryInitResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\x05\"H\n\x0eScanRunHistory\x12\x10\n\x08min_step\x18\x01 \x01(\x03\x12\x10\n\x08max_step\x18\x02 \x01(\x03\x12\x12\n\nrequest_id\x18\x03 \x01(\x05\"F\n\x12RunHistoryResponse\x12\x30\n\x0chistory_rows\x18\x01 \x03(\x0b\x32\x1a.wandb_internal.HistoryRow\"G\n\nHistoryRow\x12\x39\n\rhistory_items\x18\x01 \x03(\x0b\x32\".wandb_internal.ParquetHistoryItem\"5\n\x12ParquetHistoryItem\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x12\n\nvalue_json\x18\x10 \x01(\t\"+\n\x15ScanRunHistoryCleanup\x12\x12\n\nrequest_id\x18\x01 \x01(\x05\"\x1f\n\x1dScanRunHistoryCleanupResponse\"}\n\x12\x44ownloadRunHistory\x12\x0e\n\x06\x65ntity\x18\x01 \x01(\t\x12\x0f\n\x07project\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x14\n\x0c\x64ownload_dir\x18\x04 \x01(\t\x12 \n\x18require_complete_history\x18\x05 \x01(\x08\"L\n\x1a\x44ownloadRunHistoryResponse\x12\x12\n\nfile_names\x18\x01 \x03(\t\x12\x1a\n\x12\x63ontains_live_data\x18\x02 \x01(\x08\"\x1b\n\x19IncompleteRunHistoryError*@\n\tErrorType\x12\x11\n\rUNKNOWN_ERROR\x10\x00\x12 \n\x1cINCOMPLETE_RUN_HISTORY_ERROR\x10\x01\x42\x1bZ\x19\x63ore/pkg/service_go_protob\x06proto3')

_ERRORTYPE = DESCRIPTOR.enum_types_by_name['ErrorType']
ErrorType = enum_type_wrapper.EnumTypeWrapper(_ERRORTYPE)
UNKNOWN_ERROR = 0
INCOMPLETE_RUN_HISTORY_ERROR = 1


_SERVERAPIINITREQUEST = DESCRIPTOR.message_types_by_name['ServerApiInitRequest']
_APIREQUEST = DESCRIPTOR.message_types_by_name['ApiRequest']
_SERVERAPIINITRESPONSE = DESCRIPTOR.message_types_by_name['ServerApiInitResponse']
_APIRESPONSE = DESCRIPTOR.message_types_by_name['ApiResponse']
_APIERRORRESPONSE = DESCRIPTOR.message_types_by_name['ApiErrorResponse']
_READRUNHISTORYREQUEST = DESCRIPTOR.message_types_by_name['ReadRunHistoryRequest']
_READRUNHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['ReadRunHistoryResponse']
_SCANRUNHISTORYINIT = DESCRIPTOR.message_types_by_name['ScanRunHistoryInit']
_SCANRUNHISTORYINITRESPONSE = DESCRIPTOR.message_types_by_name['ScanRunHistoryInitResponse']
_SCANRUNHISTORY = DESCRIPTOR.message_types_by_name['ScanRunHistory']
_RUNHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['RunHistoryResponse']
_HISTORYROW = DESCRIPTOR.message_types_by_name['HistoryRow']
_PARQUETHISTORYITEM = DESCRIPTOR.message_types_by_name['ParquetHistoryItem']
_SCANRUNHISTORYCLEANUP = DESCRIPTOR.message_types_by_name['ScanRunHistoryCleanup']
_SCANRUNHISTORYCLEANUPRESPONSE = DESCRIPTOR.message_types_by_name['ScanRunHistoryCleanupResponse']
_DOWNLOADRUNHISTORY = DESCRIPTOR.message_types_by_name['DownloadRunHistory']
_DOWNLOADRUNHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['DownloadRunHistoryResponse']
_INCOMPLETERUNHISTORYERROR = DESCRIPTOR.message_types_by_name['IncompleteRunHistoryError']
ServerApiInitRequest = _reflection.GeneratedProtocolMessageType('ServerApiInitRequest', (_message.Message,), {
  'DESCRIPTOR' : _SERVERAPIINITREQUEST,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ServerApiInitRequest)
  })
_sym_db.RegisterMessage(ServerApiInitRequest)

ApiRequest = _reflection.GeneratedProtocolMessageType('ApiRequest', (_message.Message,), {
  'DESCRIPTOR' : _APIREQUEST,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ApiRequest)
  })
_sym_db.RegisterMessage(ApiRequest)

ServerApiInitResponse = _reflection.GeneratedProtocolMessageType('ServerApiInitResponse', (_message.Message,), {
  'DESCRIPTOR' : _SERVERAPIINITRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ServerApiInitResponse)
  })
_sym_db.RegisterMessage(ServerApiInitResponse)

ApiResponse = _reflection.GeneratedProtocolMessageType('ApiResponse', (_message.Message,), {
  'DESCRIPTOR' : _APIRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ApiResponse)
  })
_sym_db.RegisterMessage(ApiResponse)

ApiErrorResponse = _reflection.GeneratedProtocolMessageType('ApiErrorResponse', (_message.Message,), {
  'DESCRIPTOR' : _APIERRORRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ApiErrorResponse)
  })
_sym_db.RegisterMessage(ApiErrorResponse)

ReadRunHistoryRequest = _reflection.GeneratedProtocolMessageType('ReadRunHistoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _READRUNHISTORYREQUEST,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ReadRunHistoryRequest)
  })
_sym_db.RegisterMessage(ReadRunHistoryRequest)

ReadRunHistoryResponse = _reflection.GeneratedProtocolMessageType('ReadRunHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _READRUNHISTORYRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ReadRunHistoryResponse)
  })
_sym_db.RegisterMessage(ReadRunHistoryResponse)

ScanRunHistoryInit = _reflection.GeneratedProtocolMessageType('ScanRunHistoryInit', (_message.Message,), {
  'DESCRIPTOR' : _SCANRUNHISTORYINIT,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ScanRunHistoryInit)
  })
_sym_db.RegisterMessage(ScanRunHistoryInit)

ScanRunHistoryInitResponse = _reflection.GeneratedProtocolMessageType('ScanRunHistoryInitResponse', (_message.Message,), {
  'DESCRIPTOR' : _SCANRUNHISTORYINITRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ScanRunHistoryInitResponse)
  })
_sym_db.RegisterMessage(ScanRunHistoryInitResponse)

ScanRunHistory = _reflection.GeneratedProtocolMessageType('ScanRunHistory', (_message.Message,), {
  'DESCRIPTOR' : _SCANRUNHISTORY,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ScanRunHistory)
  })
_sym_db.RegisterMessage(ScanRunHistory)

RunHistoryResponse = _reflection.GeneratedProtocolMessageType('RunHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _RUNHISTORYRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.RunHistoryResponse)
  })
_sym_db.RegisterMessage(RunHistoryResponse)

HistoryRow = _reflection.GeneratedProtocolMessageType('HistoryRow', (_message.Message,), {
  'DESCRIPTOR' : _HISTORYROW,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.HistoryRow)
  })
_sym_db.RegisterMessage(HistoryRow)

ParquetHistoryItem = _reflection.GeneratedProtocolMessageType('ParquetHistoryItem', (_message.Message,), {
  'DESCRIPTOR' : _PARQUETHISTORYITEM,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ParquetHistoryItem)
  })
_sym_db.RegisterMessage(ParquetHistoryItem)

ScanRunHistoryCleanup = _reflection.GeneratedProtocolMessageType('ScanRunHistoryCleanup', (_message.Message,), {
  'DESCRIPTOR' : _SCANRUNHISTORYCLEANUP,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ScanRunHistoryCleanup)
  })
_sym_db.RegisterMessage(ScanRunHistoryCleanup)

ScanRunHistoryCleanupResponse = _reflection.GeneratedProtocolMessageType('ScanRunHistoryCleanupResponse', (_message.Message,), {
  'DESCRIPTOR' : _SCANRUNHISTORYCLEANUPRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.ScanRunHistoryCleanupResponse)
  })
_sym_db.RegisterMessage(ScanRunHistoryCleanupResponse)

DownloadRunHistory = _reflection.GeneratedProtocolMessageType('DownloadRunHistory', (_message.Message,), {
  'DESCRIPTOR' : _DOWNLOADRUNHISTORY,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.DownloadRunHistory)
  })
_sym_db.RegisterMessage(DownloadRunHistory)

DownloadRunHistoryResponse = _reflection.GeneratedProtocolMessageType('DownloadRunHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _DOWNLOADRUNHISTORYRESPONSE,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.DownloadRunHistoryResponse)
  })
_sym_db.RegisterMessage(DownloadRunHistoryResponse)

IncompleteRunHistoryError = _reflection.GeneratedProtocolMessageType('IncompleteRunHistoryError', (_message.Message,), {
  'DESCRIPTOR' : _INCOMPLETERUNHISTORYERROR,
  '__module__' : 'wandb.proto.wandb_api_pb2'
  # @@protoc_insertion_point(class_scope:wandb_internal.IncompleteRunHistoryError)
  })
_sym_db.RegisterMessage(IncompleteRunHistoryError)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\031core/pkg/service_go_proto'
  _ERRORTYPE._serialized_start=2038
  _ERRORTYPE._serialized_end=2102
  _SERVERAPIINITREQUEST._serialized_start=81
  _SERVERAPIINITREQUEST._serialized_end=147
  _APIREQUEST._serialized_start=149
  _APIREQUEST._serialized_end=247
  _SERVERAPIINITRESPONSE._serialized_start=249
  _SERVERAPIINITRESPONSE._serialized_end=295
  _APIRESPONSE._serialized_start=298
  _APIRESPONSE._serialized_end=549
  _APIERRORRESPONSE._serialized_start=551
  _APIERRORRESPONSE._serialized_end=653
  _READRUNHISTORYREQUEST._serialized_start=656
  _READRUNHISTORYREQUEST._serialized_end=962
  _READRUNHISTORYRESPONSE._serialized_start=965
  _READRUNHISTORYRESPONSE._serialized_end=1296
  _SCANRUNHISTORYINIT._serialized_start=1298
  _SCANRUNHISTORYINIT._serialized_end=1400
  _SCANRUNHISTORYINITRESPONSE._serialized_start=1402
  _SCANRUNHISTORYINITRESPONSE._serialized_end=1450
  _SCANRUNHISTORY._serialized_start=1452
  _SCANRUNHISTORY._serialized_end=1524
  _RUNHISTORYRESPONSE._serialized_start=1526
  _RUNHISTORYRESPONSE._serialized_end=1596
  _HISTORYROW._serialized_start=1598
  _HISTORYROW._serialized_end=1669
  _PARQUETHISTORYITEM._serialized_start=1671
  _PARQUETHISTORYITEM._serialized_end=1724
  _SCANRUNHISTORYCLEANUP._serialized_start=1726
  _SCANRUNHISTORYCLEANUP._serialized_end=1769
  _SCANRUNHISTORYCLEANUPRESPONSE._serialized_start=1771
  _SCANRUNHISTORYCLEANUPRESPONSE._serialized_end=1802
  _DOWNLOADRUNHISTORY._serialized_start=1804
  _DOWNLOADRUNHISTORY._serialized_end=1929
  _DOWNLOADRUNHISTORYRESPONSE._serialized_start=1931
  _DOWNLOADRUNHISTORYRESPONSE._serialized_end=2007
  _INCOMPLETERUNHISTORYERROR._serialized_start=2009
  _INCOMPLETERUNHISTORYERROR._serialized_end=2036
# @@protoc_insertion_point(module_scope)
