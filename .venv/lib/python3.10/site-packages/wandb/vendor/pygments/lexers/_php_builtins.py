# -*- coding: utf-8 -*-
"""
    pygments.lexers._php_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    This file loads the function names and their modules from the
    php webpage and generates itself.

    Do not alter the MODULES dict by hand!

    WARNING: the generation transfers quite much data over your
             internet connection. don't run that at home, use
             a server ;-)

    :copyright: Copyright 2006-2017 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

from __future__ import print_function

MODULES = {'.NET': ('dotnet_load',),
 'APC': ('apc_add',
         'apc_bin_dump',
         'apc_bin_dumpfile',
         'apc_bin_load',
         'apc_bin_loadfile',
         'apc_cache_info',
         'apc_cas',
         'apc_clear_cache',
         'apc_compile_file',
         'apc_dec',
         'apc_define_constants',
         'apc_delete_file',
         'apc_delete',
         'apc_exists',
         'apc_fetch',
         'apc_inc',
         'apc_load_constants',
         'apc_sma_info',
         'apc_store'),
 'APD': ('apd_breakpoint',
         'apd_callstack',
         'apd_clunk',
         'apd_continue',
         'apd_croak',
         'apd_dump_function_table',
         'apd_dump_persistent_resources',
         'apd_dump_regular_resources',
         'apd_echo',
         'apd_get_active_symbols',
         'apd_set_pprof_trace',
         'apd_set_session_trace_socket',
         'apd_set_session_trace',
         'apd_set_session',
         'override_function',
         'rename_function'),
 'Aliases and deprecated Mysqli': ('mysqli_bind_param',
                                   'mysqli_bind_result',
                                   'mysqli_client_encoding',
                                   'mysqli_connect',
                                   'mysqli_disable_rpl_parse',
                                   'mysqli_enable_reads_from_master',
                                   'mysqli_enable_rpl_parse',
                                   'mysqli_escape_string',
                                   'mysqli_execute',
                                   'mysqli_fetch',
                                   'mysqli_get_cache_stats',
                                   'mysqli_get_metadata',
                                   'mysqli_master_query',
                                   'mysqli_param_count',
                                   'mysqli_report',
                                   'mysqli_rpl_parse_enabled',
                                   'mysqli_rpl_probe',
                                   'mysqli_send_long_data',
                                   'mysqli_slave_query'),
 'Apache': ('apache_child_terminate',
            'apache_get_modules',
            'apache_get_version',
            'apache_getenv',
            'apache_lookup_uri',
            'apache_note',
            'apache_request_headers',
            'apache_reset_timeout',
            'apache_response_headers',
            'apache_setenv',
            'getallheaders',
            'virtual'),
 'Array': ('array_change_key_case',
           'array_chunk',
           'array_column',
           'array_combine',
           'array_count_values',
           'array_diff_assoc',
           'array_diff_key',
           'array_diff_uassoc',
           'array_diff_ukey',
           'array_diff',
           'array_fill_keys',
           'array_fill',
           'array_filter',
           'array_flip',
           'array_intersect_assoc',
           'array_intersect_key',
           'array_intersect_uassoc',
           'array_intersect_ukey',
           'array_intersect',
           'array_key_exists',
           'array_keys',
           'array_map',
           'array_merge_recursive',
           'array_merge',
           'array_multisort',
           'array_pad',
           'array_pop',
           'array_product',
           'array_push',
           'array_rand',
           'array_reduce',
           'array_replace_recursive',
           'array_replace',
           'array_reverse',
           'array_search',
           'array_shift',
           'array_slice',
           'array_splice',
           'array_sum',
           'array_udiff_assoc',
           'array_udiff_uassoc',
           'array_udiff',
           'array_uintersect_assoc',
           'array_uintersect_uassoc',
           'array_uintersect',
           'array_unique',
           'array_unshift',
           'array_values',
           'array_walk_recursive',
           'array_walk',
           'array',
           'arsort',
           'asort',
           'compact',
           'count',
           'current',
           'each',
           'end',
           'extract',
           'in_array',
           'key_exists',
           'key',
           'krsort',
           'ksort',
           'list',
           'natcasesort',
           'natsort',
           'next',
           'pos',
           'prev',
           'range',
           'reset',
           'rsort',
           'shuffle',
           'sizeof',
           'sort',
           'uasort',
           'uksort',
           'usort'),
 'BBCode': ('bbcode_add_element',
            'bbcode_add_smiley',
            'bbcode_create',
            'bbcode_destroy',
            'bbcode_parse',
            'bbcode_set_arg_parser',
            'bbcode_set_flags'),
 'BC Math': ('bcadd',
             'bccomp',
             'bcdiv',
             'bcmod',
             'bcmul',
             'bcpow',
             'bcpowmod',
             'bcscale',
             'bcsqrt',
             'bcsub'),
 'Blenc': ('blenc_encrypt',),
 'Bzip2': ('bzclose',
           'bzcompress',
           'bzdecompress',
           'bzerrno',
           'bzerror',
           'bzerrstr',
           'bzflush',
           'bzopen',
           'bzread',
           'bzwrite'),
 'COM': ('com_addref',
         'com_create_guid',
         'com_event_sink',
         'com_get_active_object',
         'com_get',
         'com_invoke',
         'com_isenum',
         'com_load_typelib',
         'com_load',
         'com_message_pump',
         'com_print_typeinfo',
         'com_propget',
         'com_propput',
         'com_propset',
         'com_release',
         'com_set',
         'variant_abs',
         'variant_add',
         'variant_and',
         'variant_cast',
         'variant_cat',
         'variant_cmp',
         'variant_date_from_timestamp',
         'variant_date_to_timestamp',
         'variant_div',
         'variant_eqv',
         'variant_fix',
         'variant_get_type',
         'variant_idiv',
         'variant_imp',
         'variant_int',
         'variant_mod',
         'variant_mul',
         'variant_neg',
         'variant_not',
         'variant_or',
         'variant_pow',
         'variant_round',
         'variant_set_type',
         'variant_set',
         'variant_sub',
         'variant_xor'),
 'CUBRID': ('cubrid_bind',
            'cubrid_close_prepare',
            'cubrid_close_request',
            'cubrid_col_get',
            'cubrid_col_size',
            'cubrid_column_names',
            'cubrid_column_types',
            'cubrid_commit',
            'cubrid_connect_with_url',
            'cubrid_connect',
            'cubrid_current_oid',
            'cubrid_disconnect',
            'cubrid_drop',
            'cubrid_error_code_facility',
            'cubrid_error_code',
            'cubrid_error_msg',
            'cubrid_execute',
            'cubrid_fetch',
            'cubrid_free_result',
            'cubrid_get_autocommit',
            'cubrid_get_charset',
            'cubrid_get_class_name',
            'cubrid_get_client_info',
            'cubrid_get_db_parameter',
            'cubrid_get_query_timeout',
            'cubrid_get_server_info',
            'cubrid_get',
            'cubrid_insert_id',
            'cubrid_is_instance',
            'cubrid_lob_close',
            'cubrid_lob_export',
            'cubrid_lob_get',
            'cubrid_lob_send',
            'cubrid_lob_size',
            'cubrid_lob2_bind',
            'cubrid_lob2_close',
            'cubrid_lob2_export',
            'cubrid_lob2_import',
            'cubrid_lob2_new',
            'cubrid_lob2_read',
            'cubrid_lob2_seek64',
            'cubrid_lob2_seek',
            'cubrid_lob2_size64',
            'cubrid_lob2_size',
            'cubrid_lob2_tell64',
            'cubrid_lob2_tell',
            'cubrid_lob2_write',
            'cubrid_lock_read',
            'cubrid_lock_write',
            'cubrid_move_cursor',
            'cubrid_next_result',
            'cubrid_num_cols',
            'cubrid_num_rows',
            'cubrid_pconnect_with_url',
            'cubrid_pconnect',
            'cubrid_prepare',
            'cubrid_put',
            'cubrid_rollback',
            'cubrid_schema',
            'cubrid_seq_drop',
            'cubrid_seq_insert',
            'cubrid_seq_put',
            'cubrid_set_add',
            'cubrid_set_autocommit',
            'cubrid_set_db_parameter',
            'cubrid_set_drop',
            'cubrid_set_query_timeout',
            'cubrid_version'),
 'Cairo': ('cairo_create',
           'cairo_font_face_get_type',
           'cairo_font_face_status',
           'cairo_font_options_create',
           'cairo_font_options_equal',
           'cairo_font_options_get_antialias',
           'cairo_font_options_get_hint_metrics',
           'cairo_font_options_get_hint_style',
           'cairo_font_options_get_subpixel_order',
           'cairo_font_options_hash',
           'cairo_font_options_merge',
           'cairo_font_options_set_antialias',
           'cairo_font_options_set_hint_metrics',
           'cairo_font_options_set_hint_style',
           'cairo_font_options_set_subpixel_order',
           'cairo_font_options_status',
           'cairo_format_stride_for_width',
           'cairo_image_surface_create_for_data',
           'cairo_image_surface_create_from_png',
           'cairo_image_surface_create',
           'cairo_image_surface_get_data',
           'cairo_image_surface_get_format',
           'cairo_image_surface_get_height',
           'cairo_image_surface_get_stride',
           'cairo_image_surface_get_width',
           'cairo_matrix_create_scale',
           'cairo_matrix_create_translate',
           'cairo_matrix_invert',
           'cairo_matrix_multiply',
           'cairo_matrix_rotate',
           'cairo_matrix_transform_distance',
           'cairo_matrix_transform_point',
           'cairo_matrix_translate',
           'cairo_pattern_add_color_stop_rgb',
           'cairo_pattern_add_color_stop_rgba',
           'cairo_pattern_create_for_surface',
           'cairo_pattern_create_linear',
           'cairo_pattern_create_radial',
           'cairo_pattern_create_rgb',
           'cairo_pattern_create_rgba',
           'cairo_pattern_get_color_stop_count',
           'cairo_pattern_get_color_stop_rgba',
           'cairo_pattern_get_extend',
           'cairo_pattern_get_filter',
           'cairo_pattern_get_linear_points',
           'cairo_pattern_get_matrix',
           'cairo_pattern_get_radial_circles',
           'cairo_pattern_get_rgba',
           'cairo_pattern_get_surface',
           'cairo_pattern_get_type',
           'cairo_pattern_set_extend',
           'cairo_pattern_set_filter',
           'cairo_pattern_set_matrix',
           'cairo_pattern_status',
           'cairo_pdf_surface_create',
           'cairo_pdf_surface_set_size',
           'cairo_ps_get_levels',
           'cairo_ps_level_to_string',
           'cairo_ps_surface_create',
           'cairo_ps_surface_dsc_begin_page_setup',
           'cairo_ps_surface_dsc_begin_setup',
           'cairo_ps_surface_dsc_comment',
           'cairo_ps_surface_get_eps',
           'cairo_ps_surface_restrict_to_level',
           'cairo_ps_surface_set_eps',
           'cairo_ps_surface_set_size',
           'cairo_scaled_font_create',
           'cairo_scaled_font_extents',
           'cairo_scaled_font_get_ctm',
           'cairo_scaled_font_get_font_face',
           'cairo_scaled_font_get_font_matrix',
           'cairo_scaled_font_get_font_options',
           'cairo_scaled_font_get_scale_matrix',
           'cairo_scaled_font_get_type',
           'cairo_scaled_font_glyph_extents',
           'cairo_scaled_font_status',
           'cairo_scaled_font_text_extents',
           'cairo_surface_copy_page',
           'cairo_surface_create_similar',
           'cairo_surface_finish',
           'cairo_surface_flush',
           'cairo_surface_get_content',
           'cairo_surface_get_device_offset',
           'cairo_surface_get_font_options',
           'cairo_surface_get_type',
           'cairo_surface_mark_dirty_rectangle',
           'cairo_surface_mark_dirty',
           'cairo_surface_set_device_offset',
           'cairo_surface_set_fallback_resolution',
           'cairo_surface_show_page',
           'cairo_surface_status',
           'cairo_surface_write_to_png',
           'cairo_svg_surface_create',
           'cairo_svg_surface_restrict_to_version',
           'cairo_svg_version_to_string'),
 'Calendar': ('cal_days_in_month',
              'cal_from_jd',
              'cal_info',
              'cal_to_jd',
              'easter_date',
              'easter_days',
              'FrenchToJD',
              'GregorianToJD',
              'JDDayOfWeek',
              'JDMonthName',
              'JDToFrench',
              'JDToGregorian',
              'jdtojewish',
              'JDToJulian',
              'jdtounix',
              'JewishToJD',
              'JulianToJD',
              'unixtojd'),
 'Classes/Object': ('__autoload',
                    'call_user_method_array',
                    'call_user_method',
                    'class_alias',
                    'class_exists',
                    'get_called_class',
                    'get_class_methods',
                    'get_class_vars',
                    'get_class',
                    'get_declared_classes',
                    'get_declared_interfaces',
                    'get_declared_traits',
                    'get_object_vars',
                    'get_parent_class',
                    'interface_exists',
                    'is_a',
                    'is_subclass_of',
                    'method_exists',
                    'property_exists',
                    'trait_exists'),
 'Classkit': ('classkit_import',
              'classkit_method_add',
              'classkit_method_copy',
              'classkit_method_redefine',
              'classkit_method_remove',
              'classkit_method_rename'),
 'Crack': ('crack_check',
           'crack_closedict',
           'crack_getlastmessage',
           'crack_opendict'),
 'Ctype': ('ctype_alnum',
           'ctype_alpha',
           'ctype_cntrl',
           'ctype_digit',
           'ctype_graph',
           'ctype_lower',
           'ctype_print',
           'ctype_punct',
           'ctype_space',
           'ctype_upper',
           'ctype_xdigit'),
 'Cyrus': ('cyrus_authenticate',
           'cyrus_bind',
           'cyrus_close',
           'cyrus_connect',
           'cyrus_query',
           'cyrus_unbind'),
 'DB++': ('dbplus_add',
          'dbplus_aql',
          'dbplus_chdir',
          'dbplus_close',
          'dbplus_curr',
          'dbplus_errcode',
          'dbplus_errno',
          'dbplus_find',
          'dbplus_first',
          'dbplus_flush',
          'dbplus_freealllocks',
          'dbplus_freelock',
          'dbplus_freerlocks',
          'dbplus_getlock',
          'dbplus_getunique',
          'dbplus_info',
          'dbplus_last',
          'dbplus_lockrel',
          'dbplus_next',
          'dbplus_open',
          'dbplus_prev',
          'dbplus_rchperm',
          'dbplus_rcreate',
          'dbplus_rcrtexact',
          'dbplus_rcrtlike',
          'dbplus_resolve',
          'dbplus_restorepos',
          'dbplus_rkeys',
          'dbplus_ropen',
          'dbplus_rquery',
          'dbplus_rrename',
          'dbplus_rsecindex',
          'dbplus_runlink',
          'dbplus_rzap',
          'dbplus_savepos',
          'dbplus_setindex',
          'dbplus_setindexbynumber',
          'dbplus_sql',
          'dbplus_tcl',
          'dbplus_tremove',
          'dbplus_undo',
          'dbplus_undoprepare',
          'dbplus_unlockrel',
          'dbplus_unselect',
          'dbplus_update',
          'dbplus_xlockrel',
          'dbplus_xunlockrel'),
 'DBA': ('dba_close',
         'dba_delete',
         'dba_exists',
         'dba_fetch',
         'dba_firstkey',
         'dba_handlers',
         'dba_insert',
         'dba_key_split',
         'dba_list',
         'dba_nextkey',
         'dba_open',
         'dba_optimize',
         'dba_popen',
         'dba_replace',
         'dba_sync'),
 'DOM': ('dom_import_simplexml',),
 'Date/Time': ('checkdate',
               'date_add',
               'date_create_from_format',
               'date_create_immutable_from_format',
               'date_create_immutable',
               'date_create',
               'date_date_set',
               'date_default_timezone_get',
               'date_default_timezone_set',
               'date_diff',
               'date_format',
               'date_get_last_errors',
               'date_interval_create_from_date_string',
               'date_interval_format',
               'date_isodate_set',
               'date_modify',
               'date_offset_get',
               'date_parse_from_format',
               'date_parse',
               'date_sub',
               'date_sun_info',
               'date_sunrise',
               'date_sunset',
               'date_time_set',
               'date_timestamp_get',
               'date_timestamp_set',
               'date_timezone_get',
               'date_timezone_set',
               'date',
               'getdate',
               'gettimeofday',
               'gmdate',
               'gmmktime',
               'gmstrftime',
               'idate',
               'localtime',
               'microtime',
               'mktime',
               'strftime',
               'strptime',
               'strtotime',
               'time',
               'timezone_abbreviations_list',
               'timezone_identifiers_list',
               'timezone_location_get',
               'timezone_name_from_abbr',
               'timezone_name_get',
               'timezone_offset_get',
               'timezone_open',
               'timezone_transitions_get',
               'timezone_version_get'),
 'Direct IO': ('dio_close',
               'dio_fcntl',
               'dio_open',
               'dio_read',
               'dio_seek',
               'dio_stat',
               'dio_tcsetattr',
               'dio_truncate',
               'dio_write'),
 'Directory': ('chdir',
               'chroot',
               'closedir',
               'dir',
               'getcwd',
               'opendir',
               'readdir',
               'rewinddir',
               'scandir'),
 'Eio': ('eio_busy',
         'eio_cancel',
         'eio_chmod',
         'eio_chown',
         'eio_close',
         'eio_custom',
         'eio_dup2',
         'eio_event_loop',
         'eio_fallocate',
         'eio_fchmod',
         'eio_fchown',
         'eio_fdatasync',
         'eio_fstat',
         'eio_fstatvfs',
         'eio_fsync',
         'eio_ftruncate',
         'eio_futime',
         'eio_get_event_stream',
         'eio_get_last_error',
         'eio_grp_add',
         'eio_grp_cancel',
         'eio_grp_limit',
         'eio_grp',
         'eio_init',
         'eio_link',
         'eio_lstat',
         'eio_mkdir',
         'eio_mknod',
         'eio_nop',
         'eio_npending',
         'eio_nready',
         'eio_nreqs',
         'eio_nthreads',
         'eio_open',
         'eio_poll',
         'eio_read',
         'eio_readahead',
         'eio_readdir',
         'eio_readlink',
         'eio_realpath',
         'eio_rename',
         'eio_rmdir',
         'eio_seek',
         'eio_sendfile',
         'eio_set_max_idle',
         'eio_set_max_parallel',
         'eio_set_max_poll_reqs',
         'eio_set_max_poll_time',
         'eio_set_min_parallel',
         'eio_stat',
         'eio_statvfs',
         'eio_symlink',
         'eio_sync_file_range',
         'eio_sync',
         'eio_syncfs',
         'eio_truncate',
         'eio_unlink',
         'eio_utime',
         'eio_write'),
 'Enchant': ('enchant_broker_describe',
             'enchant_broker_dict_exists',
             'enchant_broker_free_dict',
             'enchant_broker_free',
             'enchant_broker_get_error',
             'enchant_broker_init',
             'enchant_broker_list_dicts',
             'enchant_broker_request_dict',
             'enchant_broker_request_pwl_dict',
             'enchant_broker_set_ordering',
             'enchant_dict_add_to_personal',
             'enchant_dict_add_to_session',
             'enchant_dict_check',
             'enchant_dict_describe',
             'enchant_dict_get_error',
             'enchant_dict_is_in_session',
             'enchant_dict_quick_check',
             'enchant_dict_store_replacement',
             'enchant_dict_suggest'),
 'Error Handling': ('debug_backtrace',
                    'debug_print_backtrace',
                    'error_get_last',
                    'error_log',
                    'error_reporting',
                    'restore_error_handler',
                    'restore_exception_handler',
                    'set_error_handler',
                    'set_exception_handler',
                    'trigger_error',
                    'user_error'),
 'Exif': ('exif_imagetype',
          'exif_read_data',
          'exif_tagname',
          'exif_thumbnail',
          'read_exif_data'),
 'Expect': ('expect_expectl', 'expect_popen'),
 'FAM': ('fam_cancel_monitor',
         'fam_close',
         'fam_monitor_collection',
         'fam_monitor_directory',
         'fam_monitor_file',
         'fam_next_event',
         'fam_open',
         'fam_pending',
         'fam_resume_monitor',
         'fam_suspend_monitor'),
 'FDF': ('fdf_add_doc_javascript',
         'fdf_add_template',
         'fdf_close',
         'fdf_create',
         'fdf_enum_values',
         'fdf_errno',
         'fdf_error',
         'fdf_get_ap',
         'fdf_get_attachment',
         'fdf_get_encoding',
         'fdf_get_file',
         'fdf_get_flags',
         'fdf_get_opt',
         'fdf_get_status',
         'fdf_get_value',
         'fdf_get_version',
         'fdf_header',
         'fdf_next_field_name',
         'fdf_open_string',
         'fdf_open',
         'fdf_remove_item',
         'fdf_save_string',
         'fdf_save',
         'fdf_set_ap',
         'fdf_set_encoding',
         'fdf_set_file',
         'fdf_set_flags',
         'fdf_set_javascript_action',
         'fdf_set_on_import_javascript',
         'fdf_set_opt',
         'fdf_set_status',
         'fdf_set_submit_form_action',
         'fdf_set_target_frame',
         'fdf_set_value',
         'fdf_set_version'),
 'FPM': ('fastcgi_finish_request',),
 'FTP': ('ftp_alloc',
         'ftp_cdup',
         'ftp_chdir',
         'ftp_chmod',
         'ftp_close',
         'ftp_connect',
         'ftp_delete',
         'ftp_exec',
         'ftp_fget',
         'ftp_fput',
         'ftp_get_option',
         'ftp_get',
         'ftp_login',
         'ftp_mdtm',
         'ftp_mkdir',
         'ftp_nb_continue',
         'ftp_nb_fget',
         'ftp_nb_fput',
         'ftp_nb_get',
         'ftp_nb_put',
         'ftp_nlist',
         'ftp_pasv',
         'ftp_put',
         'ftp_pwd',
         'ftp_quit',
         'ftp_raw',
         'ftp_rawlist',
         'ftp_rename',
         'ftp_rmdir',
         'ftp_set_option',
         'ftp_site',
         'ftp_size',
         'ftp_ssl_connect',
         'ftp_systype'),
 'Fann': ('fann_cascadetrain_on_data',
          'fann_cascadetrain_on_file',
          'fann_clear_scaling_params',
          'fann_copy',
          'fann_create_from_file',
          'fann_create_shortcut_array',
          'fann_create_shortcut',
          'fann_create_sparse_array',
          'fann_create_sparse',
          'fann_create_standard_array',
          'fann_create_standard',
          'fann_create_train_from_callback',
          'fann_create_train',
          'fann_descale_input',
          'fann_descale_output',
          'fann_descale_train',
          'fann_destroy_train',
          'fann_destroy',
          'fann_duplicate_train_data',
          'fann_get_activation_function',
          'fann_get_activation_steepness',
          'fann_get_bias_array',
          'fann_get_bit_fail_limit',
          'fann_get_bit_fail',
          'fann_get_cascade_activation_functions_count',
          'fann_get_cascade_activation_functions',
          'fann_get_cascade_activation_steepnesses_count',
          'fann_get_cascade_activation_steepnesses',
          'fann_get_cascade_candidate_change_fraction',
          'fann_get_cascade_candidate_limit',
          'fann_get_cascade_candidate_stagnation_epochs',
          'fann_get_cascade_max_cand_epochs',
          'fann_get_cascade_max_out_epochs',
          'fann_get_cascade_min_cand_epochs',
          'fann_get_cascade_min_out_epochs',
          'fann_get_cascade_num_candidate_groups',
          'fann_get_cascade_num_candidates',
          'fann_get_cascade_output_change_fraction',
          'fann_get_cascade_output_stagnation_epochs',
          'fann_get_cascade_weight_multiplier',
          'fann_get_connection_array',
          'fann_get_connection_rate',
          'fann_get_errno',
          'fann_get_errstr',
          'fann_get_layer_array',
          'fann_get_learning_momentum',
          'fann_get_learning_rate',
          'fann_get_MSE',
          'fann_get_network_type',
          'fann_get_num_input',
          'fann_get_num_layers',
          'fann_get_num_output',
          'fann_get_quickprop_decay',
          'fann_get_quickprop_mu',
          'fann_get_rprop_decrease_factor',
          'fann_get_rprop_delta_max',
          'fann_get_rprop_delta_min',
          'fann_get_rprop_delta_zero',
          'fann_get_rprop_increase_factor',
          'fann_get_sarprop_step_error_shift',
          'fann_get_sarprop_step_error_threshold_factor',
          'fann_get_sarprop_temperature',
          'fann_get_sarprop_weight_decay_shift',
          'fann_get_total_connections',
          'fann_get_total_neurons',
          'fann_get_train_error_function',
          'fann_get_train_stop_function',
          'fann_get_training_algorithm',
          'fann_init_weights',
          'fann_length_train_data',
          'fann_merge_train_data',
          'fann_num_input_train_data',
          'fann_num_output_train_data',
          'fann_print_error',
          'fann_randomize_weights',
          'fann_read_train_from_file',
          'fann_reset_errno',
          'fann_reset_errstr',
          'fann_reset_MSE',
          'fann_run',
          'fann_save_train',
          'fann_save',
          'fann_scale_input_train_data',
          'fann_scale_input',
          'fann_scale_output_train_data',
          'fann_scale_output',
          'fann_scale_train_data',
          'fann_scale_train',
          'fann_set_activation_function_hidden',
          'fann_set_activation_function_layer',
          'fann_set_activation_function_output',
          'fann_set_activation_function',
          'fann_set_activation_steepness_hidden',
          'fann_set_activation_steepness_layer',
          'fann_set_activation_steepness_output',
          'fann_set_activation_steepness',
          'fann_set_bit_fail_limit',
          'fann_set_callback',
          'fann_set_cascade_activation_functions',
          'fann_set_cascade_activation_steepnesses',
          'fann_set_cascade_candidate_change_fraction',
          'fann_set_cascade_candidate_limit',
          'fann_set_cascade_candidate_stagnation_epochs',
          'fann_set_cascade_max_cand_epochs',
          'fann_set_cascade_max_out_epochs',
          'fann_set_cascade_min_cand_epochs',
          'fann_set_cascade_min_out_epochs',
          'fann_set_cascade_num_candidate_groups',
          'fann_set_cascade_output_change_fraction',
          'fann_set_cascade_output_stagnation_epochs',
          'fann_set_cascade_weight_multiplier',
          'fann_set_error_log',
          'fann_set_input_scaling_params',
          'fann_set_learning_momentum',
          'fann_set_learning_rate',
          'fann_set_output_scaling_params',
          'fann_set_quickprop_decay',
          'fann_set_quickprop_mu',
          'fann_set_rprop_decrease_factor',
          'fann_set_rprop_delta_max',
          'fann_set_rprop_delta_min',
          'fann_set_rprop_delta_zero',
          'fann_set_rprop_increase_factor',
          'fann_set_sarprop_step_error_shift',
          'fann_set_sarprop_step_error_threshold_factor',
          'fann_set_sarprop_temperature',
          'fann_set_sarprop_weight_decay_shift',
          'fann_set_scaling_params',
          'fann_set_train_error_function',
          'fann_set_train_stop_function',
          'fann_set_training_algorithm',
          'fann_set_weight_array',
          'fann_set_weight',
          'fann_shuffle_train_data',
          'fann_subset_train_data',
          'fann_test_data',
          'fann_test',
          'fann_train_epoch',
          'fann_train_on_data',
          'fann_train_on_file',
          'fann_train'),
 'Fileinfo': ('finfo_buffer',
              'finfo_close',
              'finfo_file',
              'finfo_open',
              'finfo_set_flags',
              'mime_content_type'),
 'Filesystem': ('basename',
                'chgrp',
                'chmod',
                'chown',
                'clearstatcache',
                'copy',
                'dirname',
                'disk_free_space',
                'disk_total_space',
                'diskfreespace',
                'fclose',
                'feof',
                'fflush',
                'fgetc',
                'fgetcsv',
                'fgets',
                'fgetss',
                'file_exists',
                'file_get_contents',
                'file_put_contents',
                'file',
                'fileatime',
                'filectime',
                'filegroup',
                'fileinode',
                'filemtime',
                'fileowner',
                'fileperms',
                'filesize',
                'filetype',
                'flock',
                'fnmatch',
                'fopen',
                'fpassthru',
                'fputcsv',
                'fputs',
                'fread',
                'fscanf',
                'fseek',
                'fstat',
                'ftell',
                'ftruncate',
                'fwrite',
                'glob',
                'is_dir',
                'is_executable',
                'is_file',
                'is_link',
                'is_readable',
                'is_uploaded_file',
                'is_writable',
                'is_writeable',
                'lchgrp',
                'lchown',
                'link',
                'linkinfo',
                'lstat',
                'mkdir',
                'move_uploaded_file',
                'parse_ini_file',
                'parse_ini_string',
                'pathinfo',
                'pclose',
                'popen',
                'readfile',
                'readlink',
                'realpath_cache_get',
                'realpath_cache_size',
                'realpath',
                'rename',
                'rewind',
                'rmdir',
                'set_file_buffer',
                'stat',
                'symlink',
                'tempnam',
                'tmpfile',
                'touch',
                'umask',
                'unlink'),
 'Filter': ('filter_has_var',
            'filter_id',
            'filter_input_array',
            'filter_input',
            'filter_list',
            'filter_var_array',
            'filter_var'),
 'Firebird/InterBase': ('ibase_add_user',
                        'ibase_affected_rows',
                        'ibase_backup',
                        'ibase_blob_add',
                        'ibase_blob_cancel',
                        'ibase_blob_close',
                        'ibase_blob_create',
                        'ibase_blob_echo',
                        'ibase_blob_get',
                        'ibase_blob_import',
                        'ibase_blob_info',
                        'ibase_blob_open',
                        'ibase_close',
                        'ibase_commit_ret',
                        'ibase_commit',
                        'ibase_connect',
                        'ibase_db_info',
                        'ibase_delete_user',
                        'ibase_drop_db',
                        'ibase_errcode',
                        'ibase_errmsg',
                        'ibase_execute',
                        'ibase_fetch_assoc',
                        'ibase_fetch_object',
                        'ibase_fetch_row',
                        'ibase_field_info',
                        'ibase_free_event_handler',
                        'ibase_free_query',
                        'ibase_free_result',
                        'ibase_gen_id',
                        'ibase_maintain_db',
                        'ibase_modify_user',
                        'ibase_name_result',
                        'ibase_num_fields',
                        'ibase_num_params',
                        'ibase_param_info',
                        'ibase_pconnect',
                        'ibase_prepare',
                        'ibase_query',
                        'ibase_restore',
                        'ibase_rollback_ret',
                        'ibase_rollback',
                        'ibase_server_info',
                        'ibase_service_attach',
                        'ibase_service_detach',
                        'ibase_set_event_handler',
                        'ibase_trans',
                        'ibase_wait_event'),
 'FriBiDi': ('fribidi_log2vis',),
 'FrontBase': ('fbsql_affected_rows',
               'fbsql_autocommit',
               'fbsql_blob_size',
               'fbsql_change_user',
               'fbsql_clob_size',
               'fbsql_close',
               'fbsql_commit',
               'fbsql_connect',
               'fbsql_create_blob',
               'fbsql_create_clob',
               'fbsql_create_db',
               'fbsql_data_seek',
               'fbsql_database_password',
               'fbsql_database',
               'fbsql_db_query',
               'fbsql_db_status',
               'fbsql_drop_db',
               'fbsql_errno',
               'fbsql_error',
               'fbsql_fetch_array',
               'fbsql_fetch_assoc',
               'fbsql_fetch_field',
               'fbsql_fetch_lengths',
               'fbsql_fetch_object',
               'fbsql_fetch_row',
               'fbsql_field_flags',
               'fbsql_field_len',
               'fbsql_field_name',
               'fbsql_field_seek',
               'fbsql_field_table',
               'fbsql_field_type',
               'fbsql_free_result',
               'fbsql_get_autostart_info',
               'fbsql_hostname',
               'fbsql_insert_id',
               'fbsql_list_dbs',
               'fbsql_list_fields',
               'fbsql_list_tables',
               'fbsql_next_result',
               'fbsql_num_fields',
               'fbsql_num_rows',
               'fbsql_password',
               'fbsql_pconnect',
               'fbsql_query',
               'fbsql_read_blob',
               'fbsql_read_clob',
               'fbsql_result',
               'fbsql_rollback',
               'fbsql_rows_fetched',
               'fbsql_select_db',
               'fbsql_set_characterset',
               'fbsql_set_lob_mode',
               'fbsql_set_password',
               'fbsql_set_transaction',
               'fbsql_start_db',
               'fbsql_stop_db',
               'fbsql_table_name',
               'fbsql_tablename',
               'fbsql_username',
               'fbsql_warnings'),
 'Function handling': ('call_user_func_array',
                       'call_user_func',
                       'create_function',
                       'forward_static_call_array',
                       'forward_static_call',
                       'func_get_arg',
                       'func_get_args',
                       'func_num_args',
                       'function_exists',
                       'get_defined_functions',
                       'register_shutdown_function',
                       'register_tick_function',
                       'unregister_tick_function'),
 'GD and Image': ('gd_info',
                  'getimagesize',
                  'getimagesizefromstring',
                  'image_type_to_extension',
                  'image_type_to_mime_type',
                  'image2wbmp',
                  'imageaffine',
                  'imageaffinematrixconcat',
                  'imageaffinematrixget',
                  'imagealphablending',
                  'imageantialias',
                  'imagearc',
                  'imagechar',
                  'imagecharup',
                  'imagecolorallocate',
                  'imagecolorallocatealpha',
                  'imagecolorat',
                  'imagecolorclosest',
                  'imagecolorclosestalpha',
                  'imagecolorclosesthwb',
                  'imagecolordeallocate',
                  'imagecolorexact',
                  'imagecolorexactalpha',
                  'imagecolormatch',
                  'imagecolorresolve',
                  'imagecolorresolvealpha',
                  'imagecolorset',
                  'imagecolorsforindex',
                  'imagecolorstotal',
                  'imagecolortransparent',
                  'imageconvolution',
                  'imagecopy',
                  'imagecopymerge',
                  'imagecopymergegray',
                  'imagecopyresampled',
                  'imagecopyresized',
                  'imagecreate',
                  'imagecreatefromgd2',
                  'imagecreatefromgd2part',
                  'imagecreatefromgd',
                  'imagecreatefromgif',
                  'imagecreatefromjpeg',
                  'imagecreatefrompng',
                  'imagecreatefromstring',
                  'imagecreatefromwbmp',
                  'imagecreatefromwebp',
                  'imagecreatefromxbm',
                  'imagecreatefromxpm',
                  'imagecreatetruecolor',
                  'imagecrop',
                  'imagecropauto',
                  'imagedashedline',
                  'imagedestroy',
                  'imageellipse',
                  'imagefill',
                  'imagefilledarc',
                  'imagefilledellipse',
                  'imagefilledpolygon',
                  'imagefilledrectangle',
                  'imagefilltoborder',
                  'imagefilter',
                  'imageflip',
                  'imagefontheight',
                  'imagefontwidth',
                  'imageftbbox',
                  'imagefttext',
                  'imagegammacorrect',
                  'imagegd2',
                  'imagegd',
                  'imagegif',
                  'imagegrabscreen',
                  'imagegrabwindow',
                  'imageinterlace',
                  'imageistruecolor',
                  'imagejpeg',
                  'imagelayereffect',
                  'imageline',
                  'imageloadfont',
                  'imagepalettecopy',
                  'imagepalettetotruecolor',
                  'imagepng',
                  'imagepolygon',
                  'imagepsbbox',
                  'imagepsencodefont',
                  'imagepsextendfont',
                  'imagepsfreefont',
                  'imagepsloadfont',
                  'imagepsslantfont',
                  'imagepstext',
                  'imagerectangle',
                  'imagerotate',
                  'imagesavealpha',
                  'imagescale',
                  'imagesetbrush',
                  'imagesetinterpolation',
                  'imagesetpixel',
                  'imagesetstyle',
                  'imagesetthickness',
                  'imagesettile',
                  'imagestring',
                  'imagestringup',
                  'imagesx',
                  'imagesy',
                  'imagetruecolortopalette',
                  'imagettfbbox',
                  'imagettftext',
                  'imagetypes',
                  'imagewbmp',
                  'imagewebp',
                  'imagexbm',
                  'iptcembed',
                  'iptcparse',
                  'jpeg2wbmp',
                  'png2wbmp'),
 'GMP': ('gmp_abs',
         'gmp_add',
         'gmp_and',
         'gmp_clrbit',
         'gmp_cmp',
         'gmp_com',
         'gmp_div_q',
         'gmp_div_qr',
         'gmp_div_r',
         'gmp_div',
         'gmp_divexact',
         'gmp_fact',
         'gmp_gcd',
         'gmp_gcdext',
         'gmp_hamdist',
         'gmp_init',
         'gmp_intval',
         'gmp_invert',
         'gmp_jacobi',
         'gmp_legendre',
         'gmp_mod',
         'gmp_mul',
         'gmp_neg',
         'gmp_nextprime',
         'gmp_or',
         'gmp_perfect_square',
         'gmp_popcount',
         'gmp_pow',
         'gmp_powm',
         'gmp_prob_prime',
         'gmp_random',
         'gmp_scan0',
         'gmp_scan1',
         'gmp_setbit',
         'gmp_sign',
         'gmp_sqrt',
         'gmp_sqrtrem',
         'gmp_strval',
         'gmp_sub',
         'gmp_testbit',
         'gmp_xor'),
 'GeoIP': ('geoip_asnum_by_name',
           'geoip_continent_code_by_name',
           'geoip_country_code_by_name',
           'geoip_country_code3_by_name',
           'geoip_country_name_by_name',
           'geoip_database_info',
           'geoip_db_avail',
           'geoip_db_filename',
           'geoip_db_get_all_info',
           'geoip_domain_by_name',
           'geoip_id_by_name',
           'geoip_isp_by_name',
           'geoip_netspeedcell_by_name',
           'geoip_org_by_name',
           'geoip_record_by_name',
           'geoip_region_by_name',
           'geoip_region_name_by_code',
           'geoip_setup_custom_directory',
           'geoip_time_zone_by_country_and_region'),
 'Gettext': ('bind_textdomain_codeset',
             'bindtextdomain',
             'dcgettext',
             'dcngettext',
             'dgettext',
             'dngettext',
             'gettext',
             'ngettext',
             'textdomain'),
 'GnuPG': ('gnupg_adddecryptkey',
           'gnupg_addencryptkey',
           'gnupg_addsignkey',
           'gnupg_cleardecryptkeys',
           'gnupg_clearencryptkeys',
           'gnupg_clearsignkeys',
           'gnupg_decrypt',
           'gnupg_decryptverify',
           'gnupg_encrypt',
           'gnupg_encryptsign',
           'gnupg_export',
           'gnupg_geterror',
           'gnupg_getprotocol',
           'gnupg_import',
           'gnupg_init',
           'gnupg_keyinfo',
           'gnupg_setarmor',
           'gnupg_seterrormode',
           'gnupg_setsignmode',
           'gnupg_sign',
           'gnupg_verify'),
 'Gopher': ('gopher_parsedir',),
 'Grapheme': ('grapheme_extract',
              'grapheme_stripos',
              'grapheme_stristr',
              'grapheme_strlen',
              'grapheme_strpos',
              'grapheme_strripos',
              'grapheme_strrpos',
              'grapheme_strstr',
              'grapheme_substr'),
 'Gupnp': ('gupnp_context_get_host_ip',
           'gupnp_context_get_port',
           'gupnp_context_get_subscription_timeout',
           'gupnp_context_host_path',
           'gupnp_context_new',
           'gupnp_context_set_subscription_timeout',
           'gupnp_context_timeout_add',
           'gupnp_context_unhost_path',
           'gupnp_control_point_browse_start',
           'gupnp_control_point_browse_stop',
           'gupnp_control_point_callback_set',
           'gupnp_control_point_new',
           'gupnp_device_action_callback_set',
           'gupnp_device_info_get_service',
           'gupnp_device_info_get',
           'gupnp_root_device_get_available',
           'gupnp_root_device_get_relative_location',
           'gupnp_root_device_new',
           'gupnp_root_device_set_available',
           'gupnp_root_device_start',
           'gupnp_root_device_stop',
           'gupnp_service_action_get',
           'gupnp_service_action_return_error',
           'gupnp_service_action_return',
           'gupnp_service_action_set',
           'gupnp_service_freeze_notify',
           'gupnp_service_info_get_introspection',
           'gupnp_service_info_get',
           'gupnp_service_introspection_get_state_variable',
           'gupnp_service_notify',
           'gupnp_service_proxy_action_get',
           'gupnp_service_proxy_action_set',
           'gupnp_service_proxy_add_notify',
           'gupnp_service_proxy_callback_set',
           'gupnp_service_proxy_get_subscribed',
           'gupnp_service_proxy_remove_notify',
           'gupnp_service_proxy_set_subscribed',
           'gupnp_service_thaw_notify'),
 'HTTP': ('http_cache_etag',
          'http_cache_last_modified',
          'http_chunked_decode',
          'http_deflate',
          'http_inflate',
          'http_build_cookie',
          'http_date',
          'http_get_request_body_stream',
          'http_get_request_body',
          'http_get_request_headers',
          'http_match_etag',
          'http_match_modified',
          'http_match_request_header',
          'http_support',
          'http_negotiate_charset',
          'http_negotiate_content_type',
          'http_negotiate_language',
          'ob_deflatehandler',
          'ob_etaghandler',
          'ob_inflatehandler',
          'http_parse_cookie',
          'http_parse_headers',
          'http_parse_message',
          'http_parse_params',
          'http_persistent_handles_clean',
          'http_persistent_handles_count',
          'http_persistent_handles_ident',
          'http_get',
          'http_head',
          'http_post_data',
          'http_post_fields',
          'http_put_data',
          'http_put_file',
          'http_put_stream',
          'http_request_body_encode',
          'http_request_method_exists',
          'http_request_method_name',
          'http_request_method_register',
          'http_request_method_unregister',
          'http_request',
          'http_redirect',
          'http_send_content_disposition',
          'http_send_content_type',
          'http_send_data',
          'http_send_file',
          'http_send_last_modified',
          'http_send_status',
          'http_send_stream',
          'http_throttle',
          'http_build_str',
          'http_build_url'),
 'Hash': ('hash_algos',
          'hash_copy',
          'hash_file',
          'hash_final',
          'hash_hmac_file',
          'hash_hmac',
          'hash_init',
          'hash_pbkdf2',
          'hash_update_file',
          'hash_update_stream',
          'hash_update',
          'hash'),
 'Hyperwave': ('hw_Array2Objrec',
               'hw_changeobject',
               'hw_Children',
               'hw_ChildrenObj',
               'hw_Close',
               'hw_Connect',
               'hw_connection_info',
               'hw_cp',
               'hw_Deleteobject',
               'hw_DocByAnchor',
               'hw_DocByAnchorObj',
               'hw_Document_Attributes',
               'hw_Document_BodyTag',
               'hw_Document_Content',
               'hw_Document_SetContent',
               'hw_Document_Size',
               'hw_dummy',
               'hw_EditText',
               'hw_Error',
               'hw_ErrorMsg',
               'hw_Free_Document',
               'hw_GetAnchors',
               'hw_GetAnchorsObj',
               'hw_GetAndLock',
               'hw_GetChildColl',
               'hw_GetChildCollObj',
               'hw_GetChildDocColl',
               'hw_GetChildDocCollObj',
               'hw_GetObject',
               'hw_GetObjectByQuery',
               'hw_GetObjectByQueryColl',
               'hw_GetObjectByQueryCollObj',
               'hw_GetObjectByQueryObj',
               'hw_GetParents',
               'hw_GetParentsObj',
               'hw_getrellink',
               'hw_GetRemote',
               'hw_getremotechildren',
               'hw_GetSrcByDestObj',
               'hw_GetText',
               'hw_getusername',
               'hw_Identify',
               'hw_InCollections',
               'hw_Info',
               'hw_InsColl',
               'hw_InsDoc',
               'hw_insertanchors',
               'hw_InsertDocument',
               'hw_InsertObject',
               'hw_mapid',
               'hw_Modifyobject',
               'hw_mv',
               'hw_New_Document',
               'hw_objrec2array',
               'hw_Output_Document',
               'hw_pConnect',
               'hw_PipeDocument',
               'hw_Root',
               'hw_setlinkroot',
               'hw_stat',
               'hw_Unlock',
               'hw_Who'),
 'Hyperwave API': ('hwapi_attribute_new',
                   'hwapi_content_new',
                   'hwapi_hgcsp',
                   'hwapi_object_new'),
 'IBM DB2': ('db2_autocommit',
             'db2_bind_param',
             'db2_client_info',
             'db2_close',
             'db2_column_privileges',
             'db2_columns',
             'db2_commit',
             'db2_conn_error',
             'db2_conn_errormsg',
             'db2_connect',
             'db2_cursor_type',
             'db2_escape_string',
             'db2_exec',
             'db2_execute',
             'db2_fetch_array',
             'db2_fetch_assoc',
             'db2_fetch_both',
             'db2_fetch_object',
             'db2_fetch_row',
             'db2_field_display_size',
             'db2_field_name',
             'db2_field_num',
             'db2_field_precision',
             'db2_field_scale',
             'db2_field_type',
             'db2_field_width',
             'db2_foreign_keys',
             'db2_free_result',
             'db2_free_stmt',
             'db2_get_option',
             'db2_last_insert_id',
             'db2_lob_read',
             'db2_next_result',
             'db2_num_fields',
             'db2_num_rows',
             'db2_pclose',
             'db2_pconnect',
             'db2_prepare',
             'db2_primary_keys',
             'db2_procedure_columns',
             'db2_procedures',
             'db2_result',
             'db2_rollback',
             'db2_server_info',
             'db2_set_option',
             'db2_special_columns',
             'db2_statistics',
             'db2_stmt_error',
             'db2_stmt_errormsg',
             'db2_table_privileges',
             'db2_tables'),
 'ID3': ('id3_get_frame_long_name',
         'id3_get_frame_short_name',
         'id3_get_genre_id',
         'id3_get_genre_list',
         'id3_get_genre_name',
         'id3_get_tag',
         'id3_get_version',
         'id3_remove_tag',
         'id3_set_tag'),
 'IDN': ('grapheme_substr', 'idn_to_ascii', 'idn_to_unicode', 'idn_to_utf8'),
 'IIS': ('iis_add_server',
         'iis_get_dir_security',
         'iis_get_script_map',
         'iis_get_server_by_comment',
         'iis_get_server_by_path',
         'iis_get_server_rights',
         'iis_get_service_state',
         'iis_remove_server',
         'iis_set_app_settings',
         'iis_set_dir_security',
         'iis_set_script_map',
         'iis_set_server_rights',
         'iis_start_server',
         'iis_start_service',
         'iis_stop_server',
         'iis_stop_service'),
 'IMAP': ('imap_8bit',
          'imap_alerts',
          'imap_append',
          'imap_base64',
          'imap_binary',
          'imap_body',
          'imap_bodystruct',
          'imap_check',
          'imap_clearflag_full',
          'imap_close',
          'imap_create',
          'imap_createmailbox',
          'imap_delete',
          'imap_deletemailbox',
          'imap_errors',
          'imap_expunge',
          'imap_fetch_overview',
          'imap_fetchbody',
          'imap_fetchheader',
          'imap_fetchmime',
          'imap_fetchstructure',
          'imap_fetchtext',
          'imap_gc',
          'imap_get_quota',
          'imap_get_quotaroot',
          'imap_getacl',
          'imap_getmailboxes',
          'imap_getsubscribed',
          'imap_header',
          'imap_headerinfo',
          'imap_headers',
          'imap_last_error',
          'imap_list',
          'imap_listmailbox',
          'imap_listscan',
          'imap_listsubscribed',
          'imap_lsub',
          'imap_mail_compose',
          'imap_mail_copy',
          'imap_mail_move',
          'imap_mail',
          'imap_mailboxmsginfo',
          'imap_mime_header_decode',
          'imap_msgno',
          'imap_num_msg',
          'imap_num_recent',
          'imap_open',
          'imap_ping',
          'imap_qprint',
          'imap_rename',
          'imap_renamemailbox',
          'imap_reopen',
          'imap_rfc822_parse_adrlist',
          'imap_rfc822_parse_headers',
          'imap_rfc822_write_address',
          'imap_savebody',
          'imap_scan',
          'imap_scanmailbox',
          'imap_search',
          'imap_set_quota',
          'imap_setacl',
          'imap_setflag_full',
          'imap_sort',
          'imap_status',
          'imap_subscribe',
          'imap_thread',
          'imap_timeout',
          'imap_uid',
          'imap_undelete',
          'imap_unsubscribe',
          'imap_utf7_decode',
          'imap_utf7_encode',
          'imap_utf8'),
 'Informix': ('ifx_affected_rows',
              'ifx_blobinfile_mode',
              'ifx_byteasvarchar',
              'ifx_close',
              'ifx_connect',
              'ifx_copy_blob',
              'ifx_create_blob',
              'ifx_create_char',
              'ifx_do',
              'ifx_error',
              'ifx_errormsg',
              'ifx_fetch_row',
              'ifx_fieldproperties',
              'ifx_fieldtypes',
              'ifx_free_blob',
              'ifx_free_char',
              'ifx_free_result',
              'ifx_get_blob',
              'ifx_get_char',
              'ifx_getsqlca',
              'ifx_htmltbl_result',
              'ifx_nullformat',
              'ifx_num_fields',
              'ifx_num_rows',
              'ifx_pconnect',
              'ifx_prepare',
              'ifx_query',
              'ifx_textasvarchar',
              'ifx_update_blob',
              'ifx_update_char',
              'ifxus_close_slob',
              'ifxus_create_slob',
              'ifxus_free_slob',
              'ifxus_open_slob',
              'ifxus_read_slob',
              'ifxus_seek_slob',
              'ifxus_tell_slob',
              'ifxus_write_slob'),
 'Ingres': ('ingres_autocommit_state',
            'ingres_autocommit',
            'ingres_charset',
            'ingres_close',
            'ingres_commit',
            'ingres_connect',
            'ingres_cursor',
            'ingres_errno',
            'ingres_error',
            'ingres_errsqlstate',
            'ingres_escape_string',
            'ingres_execute',
            'ingres_fetch_array',
            'ingres_fetch_assoc',
            'ingres_fetch_object',
            'ingres_fetch_proc_return',
            'ingres_fetch_row',
            'ingres_field_length',
            'ingres_field_name',
            'ingres_field_nullable',
            'ingres_field_precision',
            'ingres_field_scale',
            'ingres_field_type',
            'ingres_free_result',
            'ingres_next_error',
            'ingres_num_fields',
            'ingres_num_rows',
            'ingres_pconnect',
            'ingres_prepare',
            'ingres_query',
            'ingres_result_seek',
            'ingres_rollback',
            'ingres_set_environment',
            'ingres_unbuffered_query'),
 'Inotify': ('inotify_add_watch',
             'inotify_init',
             'inotify_queue_len',
             'inotify_read',
             'inotify_rm_watch'),
 'JSON': ('json_decode',
          'json_encode',
          'json_last_error_msg',
          'json_last_error'),
 'Java': ('java_last_exception_clear', 'java_last_exception_get'),
 'Judy': ('judy_type', 'judy_version'),
 'KADM5': ('kadm5_chpass_principal',
           'kadm5_create_principal',
           'kadm5_delete_principal',
           'kadm5_destroy',
           'kadm5_flush',
           'kadm5_get_policies',
           'kadm5_get_principal',
           'kadm5_get_principals',
           'kadm5_init_with_password',
           'kadm5_modify_principal'),
 'LDAP': ('ldap_8859_to_t61',
          'ldap_add',
          'ldap_bind',
          'ldap_close',
          'ldap_compare',
          'ldap_connect',
          'ldap_control_paged_result_response',
          'ldap_control_paged_result',
          'ldap_count_entries',
          'ldap_delete',
          'ldap_dn2ufn',
          'ldap_err2str',
          'ldap_errno',
          'ldap_error',
          'ldap_explode_dn',
          'ldap_first_attribute',
          'ldap_first_entry',
          'ldap_first_reference',
          'ldap_free_result',
          'ldap_get_attributes',
          'ldap_get_dn',
          'ldap_get_entries',
          'ldap_get_option',
          'ldap_get_values_len',
          'ldap_get_values',
          'ldap_list',
          'ldap_mod_add',
          'ldap_mod_del',
          'ldap_mod_replace',
          'ldap_modify',
          'ldap_next_attribute',
          'ldap_next_entry',
          'ldap_next_reference',
          'ldap_parse_reference',
          'ldap_parse_result',
          'ldap_read',
          'ldap_rename',
          'ldap_sasl_bind',
          'ldap_search',
          'ldap_set_option',
          'ldap_set_rebind_proc',
          'ldap_sort',
          'ldap_start_tls',
          'ldap_t61_to_8859',
          'ldap_unbind'),
 'LZF': ('lzf_compress', 'lzf_decompress', 'lzf_optimized_for'),
 'Libevent': ('event_add',
              'event_base_free',
              'event_base_loop',
              'event_base_loopbreak',
              'event_base_loopexit',
              'event_base_new',
              'event_base_priority_init',
              'event_base_set',
              'event_buffer_base_set',
              'event_buffer_disable',
              'event_buffer_enable',
              'event_buffer_fd_set',
              'event_buffer_free',
              'event_buffer_new',
              'event_buffer_priority_set',
              'event_buffer_read',
              'event_buffer_set_callback',
              'event_buffer_timeout_set',
              'event_buffer_watermark_set',
              'event_buffer_write',
              'event_del',
              'event_free',
              'event_new',
              'event_set'),
 'Lotus Notes': ('notes_body',
                 'notes_copy_db',
                 'notes_create_db',
                 'notes_create_note',
                 'notes_drop_db',
                 'notes_find_note',
                 'notes_header_info',
                 'notes_list_msgs',
                 'notes_mark_read',
                 'notes_mark_unread',
                 'notes_nav_create',
                 'notes_search',
                 'notes_unread',
                 'notes_version'),
 'MCVE': ('m_checkstatus',
          'm_completeauthorizations',
          'm_connect',
          'm_connectionerror',
          'm_deletetrans',
          'm_destroyconn',
          'm_destroyengine',
          'm_getcell',
          'm_getcellbynum',
          'm_getcommadelimited',
          'm_getheader',
          'm_initconn',
          'm_initengine',
          'm_iscommadelimited',
          'm_maxconntimeout',
          'm_monitor',
          'm_numcolumns',
          'm_numrows',
          'm_parsecommadelimited',
          'm_responsekeys',
          'm_responseparam',
          'm_returnstatus',
          'm_setblocking',
          'm_setdropfile',
          'm_setip',
          'm_setssl_cafile',
          'm_setssl_files',
          'm_setssl',
          'm_settimeout',
          'm_sslcert_gen_hash',
          'm_transactionssent',
          'm_transinqueue',
          'm_transkeyval',
          'm_transnew',
          'm_transsend',
          'm_uwait',
          'm_validateidentifier',
          'm_verifyconnection',
          'm_verifysslcert'),
 'Mail': ('ezmlm_hash', 'mail'),
 'Mailparse': ('mailparse_determine_best_xfer_encoding',
               'mailparse_msg_create',
               'mailparse_msg_extract_part_file',
               'mailparse_msg_extract_part',
               'mailparse_msg_extract_whole_part_file',
               'mailparse_msg_free',
               'mailparse_msg_get_part_data',
               'mailparse_msg_get_part',
               'mailparse_msg_get_structure',
               'mailparse_msg_parse_file',
               'mailparse_msg_parse',
               'mailparse_rfc822_parse_addresses',
               'mailparse_stream_encode',
               'mailparse_uudecode_all'),
 'Math': ('abs',
          'acos',
          'acosh',
          'asin',
          'asinh',
          'atan2',
          'atan',
          'atanh',
          'base_convert',
          'bindec',
          'ceil',
          'cos',
          'cosh',
          'decbin',
          'dechex',
          'decoct',
          'deg2rad',
          'exp',
          'expm1',
          'floor',
          'fmod',
          'getrandmax',
          'hexdec',
          'hypot',
          'is_finite',
          'is_infinite',
          'is_nan',
          'lcg_value',
          'log10',
          'log1p',
          'log',
          'max',
          'min',
          'mt_getrandmax',
          'mt_rand',
          'mt_srand',
          'octdec',
          'pi',
          'pow',
          'rad2deg',
          'rand',
          'round',
          'sin',
          'sinh',
          'sqrt',
          'srand',
          'tan',
          'tanh'),
 'MaxDB': ('maxdb_affected_rows',
           'maxdb_autocommit',
           'maxdb_bind_param',
           'maxdb_bind_result',
           'maxdb_change_user',
           'maxdb_character_set_name',
           'maxdb_client_encoding',
           'maxdb_close_long_data',
           'maxdb_close',
           'maxdb_commit',
           'maxdb_connect_errno',
           'maxdb_connect_error',
           'maxdb_connect',
           'maxdb_data_seek',
           'maxdb_debug',
           'maxdb_disable_reads_from_master',
           'maxdb_disable_rpl_parse',
           'maxdb_dump_debug_info',
           'maxdb_embedded_connect',
           'maxdb_enable_reads_from_master',
           'maxdb_enable_rpl_parse',
           'maxdb_errno',
           'maxdb_error',
           'maxdb_escape_string',
           'maxdb_execute',
           'maxdb_fetch_array',
           'maxdb_fetch_assoc',
           'maxdb_fetch_field_direct',
           'maxdb_fetch_field',
           'maxdb_fetch_fields',
           'maxdb_fetch_lengths',
           'maxdb_fetch_object',
           'maxdb_fetch_row',
           'maxdb_fetch',
           'maxdb_field_count',
           'maxdb_field_seek',
           'maxdb_field_tell',
           'maxdb_free_result',
           'maxdb_get_client_info',
           'maxdb_get_client_version',
           'maxdb_get_host_info',
           'maxdb_get_metadata',
           'maxdb_get_proto_info',
           'maxdb_get_server_info',
           'maxdb_get_server_version',
           'maxdb_info',
           'maxdb_init',
           'maxdb_insert_id',
           'maxdb_kill',
           'maxdb_master_query',
           'maxdb_more_results',
           'maxdb_multi_query',
           'maxdb_next_result',
           'maxdb_num_fields',
           'maxdb_num_rows',
           'maxdb_options',
           'maxdb_param_count',
           'maxdb_ping',
           'maxdb_prepare',
           'maxdb_query',
           'maxdb_real_connect',
           'maxdb_real_escape_string',
           'maxdb_real_query',
           'maxdb_report',
           'maxdb_rollback',
           'maxdb_rpl_parse_enabled',
           'maxdb_rpl_probe',
           'maxdb_rpl_query_type',
           'maxdb_select_db',
           'maxdb_send_long_data',
           'maxdb_send_query',
           'maxdb_server_end',
           'maxdb_server_init',
           'maxdb_set_opt',
           'maxdb_sqlstate',
           'maxdb_ssl_set',
           'maxdb_stat',
           'maxdb_stmt_affected_rows',
           'maxdb_stmt_bind_param',
           'maxdb_stmt_bind_result',
           'maxdb_stmt_close_long_data',
           'maxdb_stmt_close',
           'maxdb_stmt_data_seek',
           'maxdb_stmt_errno',
           'maxdb_stmt_error',
           'maxdb_stmt_execute',
           'maxdb_stmt_fetch',
           'maxdb_stmt_free_result',
           'maxdb_stmt_init',
           'maxdb_stmt_num_rows',
           'maxdb_stmt_param_count',
           'maxdb_stmt_prepare',
           'maxdb_stmt_reset',
           'maxdb_stmt_result_metadata',
           'maxdb_stmt_send_long_data',
           'maxdb_stmt_sqlstate',
           'maxdb_stmt_store_result',
           'maxdb_store_result',
           'maxdb_thread_id',
           'maxdb_thread_safe',
           'maxdb_use_result',
           'maxdb_warning_count'),
 'Mcrypt': ('mcrypt_cbc',
            'mcrypt_cfb',
            'mcrypt_create_iv',
            'mcrypt_decrypt',
            'mcrypt_ecb',
            'mcrypt_enc_get_algorithms_name',
            'mcrypt_enc_get_block_size',
            'mcrypt_enc_get_iv_size',
            'mcrypt_enc_get_key_size',
            'mcrypt_enc_get_modes_name',
            'mcrypt_enc_get_supported_key_sizes',
            'mcrypt_enc_is_block_algorithm_mode',
            'mcrypt_enc_is_block_algorithm',
            'mcrypt_enc_is_block_mode',
            'mcrypt_enc_self_test',
            'mcrypt_encrypt',
            'mcrypt_generic_deinit',
            'mcrypt_generic_end',
            'mcrypt_generic_init',
            'mcrypt_generic',
            'mcrypt_get_block_size',
            'mcrypt_get_cipher_name',
            'mcrypt_get_iv_size',
            'mcrypt_get_key_size',
            'mcrypt_list_algorithms',
            'mcrypt_list_modes',
            'mcrypt_module_close',
            'mcrypt_module_get_algo_block_size',
            'mcrypt_module_get_algo_key_size',
            'mcrypt_module_get_supported_key_sizes',
            'mcrypt_module_is_block_algorithm_mode',
            'mcrypt_module_is_block_algorithm',
            'mcrypt_module_is_block_mode',
            'mcrypt_module_open',
            'mcrypt_module_self_test',
            'mcrypt_ofb',
            'mdecrypt_generic'),
 'Memcache': ('memcache_debug',),
 'Mhash': ('mhash_count',
           'mhash_get_block_size',
           'mhash_get_hash_name',
           'mhash_keygen_s2k',
           'mhash'),
 'Ming': ('ming_keypress',
          'ming_setcubicthreshold',
          'ming_setscale',
          'ming_setswfcompression',
          'ming_useconstants',
          'ming_useswfversion'),
 'Misc.': ('connection_aborted',
           'connection_status',
           'connection_timeout',
           'constant',
           'define',
           'defined',
           'die',
           'eval',
           'exit',
           'get_browser',
           '__halt_compiler',
           'highlight_file',
           'highlight_string',
           'ignore_user_abort',
           'pack',
           'php_check_syntax',
           'php_strip_whitespace',
           'show_source',
           'sleep',
           'sys_getloadavg',
           'time_nanosleep',
           'time_sleep_until',
           'uniqid',
           'unpack',
           'usleep'),
 'Mongo': ('bson_decode', 'bson_encode'),
 'Msession': ('msession_connect',
              'msession_count',
              'msession_create',
              'msession_destroy',
              'msession_disconnect',
              'msession_find',
              'msession_get_array',
              'msession_get_data',
              'msession_get',
              'msession_inc',
              'msession_list',
              'msession_listvar',
              'msession_lock',
              'msession_plugin',
              'msession_randstr',
              'msession_set_array',
              'msession_set_data',
              'msession_set',
              'msession_timeout',
              'msession_uniq',
              'msession_unlock'),
 'Mssql': ('mssql_bind',
           'mssql_close',
           'mssql_connect',
           'mssql_data_seek',
           'mssql_execute',
           'mssql_fetch_array',
           'mssql_fetch_assoc',
           'mssql_fetch_batch',
           'mssql_fetch_field',
           'mssql_fetch_object',
           'mssql_fetch_row',
           'mssql_field_length',
           'mssql_field_name',
           'mssql_field_seek',
           'mssql_field_type',
           'mssql_free_result',
           'mssql_free_statement',
           'mssql_get_last_message',
           'mssql_guid_string',
           'mssql_init',
           'mssql_min_error_severity',
           'mssql_min_message_severity',
           'mssql_next_result',
           'mssql_num_fields',
           'mssql_num_rows',
           'mssql_pconnect',
           'mssql_query',
           'mssql_result',
           'mssql_rows_affected',
           'mssql_select_db'),
 'Multibyte String': ('mb_check_encoding',
                      'mb_convert_case',
                      'mb_convert_encoding',
                      'mb_convert_kana',
                      'mb_convert_variables',
                      'mb_decode_mimeheader',
                      'mb_decode_numericentity',
                      'mb_detect_encoding',
                      'mb_detect_order',
                      'mb_encode_mimeheader',
                      'mb_encode_numericentity',
                      'mb_encoding_aliases',
                      'mb_ereg_match',
                      'mb_ereg_replace_callback',
                      'mb_ereg_replace',
                      'mb_ereg_search_getpos',
                      'mb_ereg_search_getregs',
                      'mb_ereg_search_init',
                      'mb_ereg_search_pos',
                      'mb_ereg_search_regs',
                      'mb_ereg_search_setpos',
                      'mb_ereg_search',
                      'mb_ereg',
                      'mb_eregi_replace',
                      'mb_eregi',
                      'mb_get_info',
                      'mb_http_input',
                      'mb_http_output',
                      'mb_internal_encoding',
                      'mb_language',
                      'mb_list_encodings',
                      'mb_output_handler',
                      'mb_parse_str',
                      'mb_preferred_mime_name',
                      'mb_regex_encoding',
                      'mb_regex_set_options',
                      'mb_send_mail',
                      'mb_split',
                      'mb_strcut',
                      'mb_strimwidth',
                      'mb_stripos',
                      'mb_stristr',
                      'mb_strlen',
                      'mb_strpos',
                      'mb_strrchr',
                      'mb_strrichr',
                      'mb_strripos',
                      'mb_strrpos',
                      'mb_strstr',
                      'mb_strtolower',
                      'mb_strtoupper',
                      'mb_strwidth',
                      'mb_substitute_character',
                      'mb_substr_count',
                      'mb_substr'),
 'MySQL': ('mysql_affected_rows',
           'mysql_client_encoding',
           'mysql_close',
           'mysql_connect',
           'mysql_create_db',
           'mysql_data_seek',
           'mysql_db_name',
           'mysql_db_query',
           'mysql_drop_db',
           'mysql_errno',
           'mysql_error',
           'mysql_escape_string',
           'mysql_fetch_array',
           'mysql_fetch_assoc',
           'mysql_fetch_field',
           'mysql_fetch_lengths',
           'mysql_fetch_object',
           'mysql_fetch_row',
           'mysql_field_flags',
           'mysql_field_len',
           'mysql_field_name',
           'mysql_field_seek',
           'mysql_field_table',
           'mysql_field_type',
           'mysql_free_result',
           'mysql_get_client_info',
           'mysql_get_host_info',
           'mysql_get_proto_info',
           'mysql_get_server_info',
           'mysql_info',
           'mysql_insert_id',
           'mysql_list_dbs',
           'mysql_list_fields',
           'mysql_list_processes',
           'mysql_list_tables',
           'mysql_num_fields',
           'mysql_num_rows',
           'mysql_pconnect',
           'mysql_ping',
           'mysql_query',
           'mysql_real_escape_string',
           'mysql_result',
           'mysql_select_db',
           'mysql_set_charset',
           'mysql_stat',
           'mysql_tablename',
           'mysql_thread_id',
           'mysql_unbuffered_query'),
 'Mysqlnd_memcache': ('mysqlnd_memcache_get_config', 'mysqlnd_memcache_set'),
 'Mysqlnd_ms': ('mysqlnd_ms_dump_servers',
                'mysqlnd_ms_fabric_select_global',
                'mysqlnd_ms_fabric_select_shard',
                'mysqlnd_ms_get_last_gtid',
                'mysqlnd_ms_get_last_used_connection',
                'mysqlnd_ms_get_stats',
                'mysqlnd_ms_match_wild',
                'mysqlnd_ms_query_is_select',
                'mysqlnd_ms_set_qos',
                'mysqlnd_ms_set_user_pick_server'),
 'Mysqlnd_uh': ('mysqlnd_uh_convert_to_mysqlnd',
                'mysqlnd_uh_set_connection_proxy',
                'mysqlnd_uh_set_statement_proxy'),
 'NSAPI': ('nsapi_request_headers', 'nsapi_response_headers', 'nsapi_virtual'),
 'Ncurses': ('ncurses_addch',
             'ncurses_addchnstr',
             'ncurses_addchstr',
             'ncurses_addnstr',
             'ncurses_addstr',
             'ncurses_assume_default_colors',
             'ncurses_attroff',
             'ncurses_attron',
             'ncurses_attrset',
             'ncurses_baudrate',
             'ncurses_beep',
             'ncurses_bkgd',
             'ncurses_bkgdset',
             'ncurses_border',
             'ncurses_bottom_panel',
             'ncurses_can_change_color',
             'ncurses_cbreak',
             'ncurses_clear',
             'ncurses_clrtobot',
             'ncurses_clrtoeol',
             'ncurses_color_content',
             'ncurses_color_set',
             'ncurses_curs_set',
             'ncurses_def_prog_mode',
             'ncurses_def_shell_mode',
             'ncurses_define_key',
             'ncurses_del_panel',
             'ncurses_delay_output',
             'ncurses_delch',
             'ncurses_deleteln',
             'ncurses_delwin',
             'ncurses_doupdate',
             'ncurses_echo',
             'ncurses_echochar',
             'ncurses_end',
             'ncurses_erase',
             'ncurses_erasechar',
             'ncurses_filter',
             'ncurses_flash',
             'ncurses_flushinp',
             'ncurses_getch',
             'ncurses_getmaxyx',
             'ncurses_getmouse',
             'ncurses_getyx',
             'ncurses_halfdelay',
             'ncurses_has_colors',
             'ncurses_has_ic',
             'ncurses_has_il',
             'ncurses_has_key',
             'ncurses_hide_panel',
             'ncurses_hline',
             'ncurses_inch',
             'ncurses_init_color',
             'ncurses_init_pair',
             'ncurses_init',
             'ncurses_insch',
             'ncurses_insdelln',
             'ncurses_insertln',
             'ncurses_insstr',
             'ncurses_instr',
             'ncurses_isendwin',
             'ncurses_keyok',
             'ncurses_keypad',
             'ncurses_killchar',
             'ncurses_longname',
             'ncurses_meta',
             'ncurses_mouse_trafo',
             'ncurses_mouseinterval',
             'ncurses_mousemask',
             'ncurses_move_panel',
             'ncurses_move',
             'ncurses_mvaddch',
             'ncurses_mvaddchnstr',
             'ncurses_mvaddchstr',
             'ncurses_mvaddnstr',
             'ncurses_mvaddstr',
             'ncurses_mvcur',
             'ncurses_mvdelch',
             'ncurses_mvgetch',
             'ncurses_mvhline',
             'ncurses_mvinch',
             'ncurses_mvvline',
             'ncurses_mvwaddstr',
             'ncurses_napms',
             'ncurses_new_panel',
             'ncurses_newpad',
             'ncurses_newwin',
             'ncurses_nl',
             'ncurses_nocbreak',
             'ncurses_noecho',
             'ncurses_nonl',
             'ncurses_noqiflush',
             'ncurses_noraw',
             'ncurses_pair_content',
             'ncurses_panel_above',
             'ncurses_panel_below',
             'ncurses_panel_window',
             'ncurses_pnoutrefresh',
             'ncurses_prefresh',
             'ncurses_putp',
             'ncurses_qiflush',
             'ncurses_raw',
             'ncurses_refresh',
             'ncurses_replace_panel',
             'ncurses_reset_prog_mode',
             'ncurses_reset_shell_mode',
             'ncurses_resetty',
             'ncurses_savetty',
             'ncurses_scr_dump',
             'ncurses_scr_init',
             'ncurses_scr_restore',
             'ncurses_scr_set',
             'ncurses_scrl',
             'ncurses_show_panel',
             'ncurses_slk_attr',
             'ncurses_slk_attroff',
             'ncurses_slk_attron',
             'ncurses_slk_attrset',
             'ncurses_slk_clear',
             'ncurses_slk_color',
             'ncurses_slk_init',
             'ncurses_slk_noutrefresh',
             'ncurses_slk_refresh',
             'ncurses_slk_restore',
             'ncurses_slk_set',
             'ncurses_slk_touch',
             'ncurses_standend',
             'ncurses_standout',
             'ncurses_start_color',
             'ncurses_termattrs',
             'ncurses_termname',
             'ncurses_timeout',
             'ncurses_top_panel',
             'ncurses_typeahead',
             'ncurses_ungetch',
             'ncurses_ungetmouse',
             'ncurses_update_panels',
             'ncurses_use_default_colors',
             'ncurses_use_env',
             'ncurses_use_extended_names',
             'ncurses_vidattr',
             'ncurses_vline',
             'ncurses_waddch',
             'ncurses_waddstr',
             'ncurses_wattroff',
             'ncurses_wattron',
             'ncurses_wattrset',
             'ncurses_wborder',
             'ncurses_wclear',
             'ncurses_wcolor_set',
             'ncurses_werase',
             'ncurses_wgetch',
             'ncurses_whline',
             'ncurses_wmouse_trafo',
             'ncurses_wmove',
             'ncurses_wnoutrefresh',
             'ncurses_wrefresh',
             'ncurses_wstandend',
             'ncurses_wstandout',
             'ncurses_wvline'),
 'Network': ('checkdnsrr',
             'closelog',
             'define_syslog_variables',
             'dns_check_record',
             'dns_get_mx',
             'dns_get_record',
             'fsockopen',
             'gethostbyaddr',
             'gethostbyname',
             'gethostbynamel',
             'gethostname',
             'getmxrr',
             'getprotobyname',
             'getprotobynumber',
             'getservbyname',
             'getservbyport',
             'header_register_callback',
             'header_remove',
             'header',
             'headers_list',
             'headers_sent',
             'http_response_code',
             'inet_ntop',
             'inet_pton',
             'ip2long',
             'long2ip',
             'openlog',
             'pfsockopen',
             'setcookie',
             'setrawcookie',
             'socket_get_status',
             'socket_set_blocking',
             'socket_set_timeout',
             'syslog'),
 'Newt': ('newt_bell',
          'newt_button_bar',
          'newt_button',
          'newt_centered_window',
          'newt_checkbox_get_value',
          'newt_checkbox_set_flags',
          'newt_checkbox_set_value',
          'newt_checkbox_tree_add_item',
          'newt_checkbox_tree_find_item',
          'newt_checkbox_tree_get_current',
          'newt_checkbox_tree_get_entry_value',
          'newt_checkbox_tree_get_multi_selection',
          'newt_checkbox_tree_get_selection',
          'newt_checkbox_tree_multi',
          'newt_checkbox_tree_set_current',
          'newt_checkbox_tree_set_entry_value',
          'newt_checkbox_tree_set_entry',
          'newt_checkbox_tree_set_width',
          'newt_checkbox_tree',
          'newt_checkbox',
          'newt_clear_key_buffer',
          'newt_cls',
          'newt_compact_button',
          'newt_component_add_callback',
          'newt_component_takes_focus',
          'newt_create_grid',
          'newt_cursor_off',
          'newt_cursor_on',
          'newt_delay',
          'newt_draw_form',
          'newt_draw_root_text',
          'newt_entry_get_value',
          'newt_entry_set_filter',
          'newt_entry_set_flags',
          'newt_entry_set',
          'newt_entry',
          'newt_finished',
          'newt_form_add_component',
          'newt_form_add_components',
          'newt_form_add_hot_key',
          'newt_form_destroy',
          'newt_form_get_current',
          'newt_form_run',
          'newt_form_set_background',
          'newt_form_set_height',
          'newt_form_set_size',
          'newt_form_set_timer',
          'newt_form_set_width',
          'newt_form_watch_fd',
          'newt_form',
          'newt_get_screen_size',
          'newt_grid_add_components_to_form',
          'newt_grid_basic_window',
          'newt_grid_free',
          'newt_grid_get_size',
          'newt_grid_h_close_stacked',
          'newt_grid_h_stacked',
          'newt_grid_place',
          'newt_grid_set_field',
          'newt_grid_simple_window',
          'newt_grid_v_close_stacked',
          'newt_grid_v_stacked',
          'newt_grid_wrapped_window_at',
          'newt_grid_wrapped_window',
          'newt_init',
          'newt_label_set_text',
          'newt_label',
          'newt_listbox_append_entry',
          'newt_listbox_clear_selection',
          'newt_listbox_clear',
          'newt_listbox_delete_entry',
          'newt_listbox_get_current',
          'newt_listbox_get_selection',
          'newt_listbox_insert_entry',
          'newt_listbox_item_count',
          'newt_listbox_select_item',
          'newt_listbox_set_current_by_key',
          'newt_listbox_set_current',
          'newt_listbox_set_data',
          'newt_listbox_set_entry',
          'newt_listbox_set_width',
          'newt_listbox',
          'newt_listitem_get_data',
          'newt_listitem_set',
          'newt_listitem',
          'newt_open_window',
          'newt_pop_help_line',
          'newt_pop_window',
          'newt_push_help_line',
          'newt_radio_get_current',
          'newt_radiobutton',
          'newt_redraw_help_line',
          'newt_reflow_text',
          'newt_refresh',
          'newt_resize_screen',
          'newt_resume',
          'newt_run_form',
          'newt_scale_set',
          'newt_scale',
          'newt_scrollbar_set',
          'newt_set_help_callback',
          'newt_set_suspend_callback',
          'newt_suspend',
          'newt_textbox_get_num_lines',
          'newt_textbox_reflowed',
          'newt_textbox_set_height',
          'newt_textbox_set_text',
          'newt_textbox',
          'newt_vertical_scrollbar',
          'newt_wait_for_key',
          'newt_win_choice',
          'newt_win_entries',
          'newt_win_menu',
          'newt_win_message',
          'newt_win_messagev',
          'newt_win_ternary'),
 'OAuth': ('oauth_get_sbs', 'oauth_urlencode'),
 'OCI8': ('oci_bind_array_by_name',
          'oci_bind_by_name',
          'oci_cancel',
          'oci_client_version',
          'oci_close',
          'oci_commit',
          'oci_connect',
          'oci_define_by_name',
          'oci_error',
          'oci_execute',
          'oci_fetch_all',
          'oci_fetch_array',
          'oci_fetch_assoc',
          'oci_fetch_object',
          'oci_fetch_row',
          'oci_fetch',
          'oci_field_is_null',
          'oci_field_name',
          'oci_field_precision',
          'oci_field_scale',
          'oci_field_size',
          'oci_field_type_raw',
          'oci_field_type',
          'oci_free_descriptor',
          'oci_free_statement',
          'oci_get_implicit_resultset',
          'oci_internal_debug',
          'oci_lob_copy',
          'oci_lob_is_equal',
          'oci_new_collection',
          'oci_new_connect',
          'oci_new_cursor',
          'oci_new_descriptor',
          'oci_num_fields',
          'oci_num_rows',
          'oci_parse',
          'oci_password_change',
          'oci_pconnect',
          'oci_result',
          'oci_rollback',
          'oci_server_version',
          'oci_set_action',
          'oci_set_client_identifier',
          'oci_set_client_info',
          'oci_set_edition',
          'oci_set_module_name',
          'oci_set_prefetch',
          'oci_statement_type'),
 'ODBC': ('odbc_autocommit',
          'odbc_binmode',
          'odbc_close_all',
          'odbc_close',
          'odbc_columnprivileges',
          'odbc_columns',
          'odbc_commit',
          'odbc_connect',
          'odbc_cursor',
          'odbc_data_source',
          'odbc_do',
          'odbc_error',
          'odbc_errormsg',
          'odbc_exec',
          'odbc_execute',
          'odbc_fetch_array',
          'odbc_fetch_into',
          'odbc_fetch_object',
          'odbc_fetch_row',
          'odbc_field_len',
          'odbc_field_name',
          'odbc_field_num',
          'odbc_field_precision',
          'odbc_field_scale',
          'odbc_field_type',
          'odbc_foreignkeys',
          'odbc_free_result',
          'odbc_gettypeinfo',
          'odbc_longreadlen',
          'odbc_next_result',
          'odbc_num_fields',
          'odbc_num_rows',
          'odbc_pconnect',
          'odbc_prepare',
          'odbc_primarykeys',
          'odbc_procedurecolumns',
          'odbc_procedures',
          'odbc_result_all',
          'odbc_result',
          'odbc_rollback',
          'odbc_setoption',
          'odbc_specialcolumns',
          'odbc_statistics',
          'odbc_tableprivileges',
          'odbc_tables'),
 'OPcache': ('opcache_compile_file',
             'opcache_get_configuration',
             'opcache_get_status',
             'opcache_invalidate',
             'opcache_reset'),
 'Object Aggregation': ('aggregate_info',
                        'aggregate_methods_by_list',
                        'aggregate_methods_by_regexp',
                        'aggregate_methods',
                        'aggregate_properties_by_list',
                        'aggregate_properties_by_regexp',
                        'aggregate_properties',
                        'aggregate',
                        'aggregation_info',
                        'deaggregate'),
 'OpenAL': ('openal_buffer_create',
            'openal_buffer_data',
            'openal_buffer_destroy',
            'openal_buffer_get',
            'openal_buffer_loadwav',
            'openal_context_create',
            'openal_context_current',
            'openal_context_destroy',
            'openal_context_process',
            'openal_context_suspend',
            'openal_device_close',
            'openal_device_open',
            'openal_listener_get',
            'openal_listener_set',
            'openal_source_create',
            'openal_source_destroy',
            'openal_source_get',
            'openal_source_pause',
            'openal_source_play',
            'openal_source_rewind',
            'openal_source_set',
            'openal_source_stop',
            'openal_stream'),
 'OpenSSL': ('openssl_cipher_iv_length',
             'openssl_csr_export_to_file',
             'openssl_csr_export',
             'openssl_csr_get_public_key',
             'openssl_csr_get_subject',
             'openssl_csr_new',
             'openssl_csr_sign',
             'openssl_decrypt',
             'openssl_dh_compute_key',
             'openssl_digest',
             'openssl_encrypt',
             'openssl_error_string',
             'openssl_free_key',
             'openssl_get_cipher_methods',
             'openssl_get_md_methods',
             'openssl_get_privatekey',
             'openssl_get_publickey',
             'openssl_open',
             'openssl_pbkdf2',
             'openssl_pkcs12_export_to_file',
             'openssl_pkcs12_export',
             'openssl_pkcs12_read',
             'openssl_pkcs7_decrypt',
             'openssl_pkcs7_encrypt',
             'openssl_pkcs7_sign',
             'openssl_pkcs7_verify',
             'openssl_pkey_export_to_file',
             'openssl_pkey_export',
             'openssl_pkey_free',
             'openssl_pkey_get_details',
             'openssl_pkey_get_private',
             'openssl_pkey_get_public',
             'openssl_pkey_new',
             'openssl_private_decrypt',
             'openssl_private_encrypt',
             'openssl_public_decrypt',
             'openssl_public_encrypt',
             'openssl_random_pseudo_bytes',
             'openssl_seal',
             'openssl_sign',
             'openssl_spki_export_challenge',
             'openssl_spki_export',
             'openssl_spki_new',
             'openssl_spki_verify',
             'openssl_verify',
             'openssl_x509_check_private_key',
             'openssl_x509_checkpurpose',
             'openssl_x509_export_to_file',
             'openssl_x509_export',
             'openssl_x509_free',
             'openssl_x509_parse',
             'openssl_x509_read'),
 'Output Control': ('flush',
                    'ob_clean',
                    'ob_end_clean',
                    'ob_end_flush',
                    'ob_flush',
                    'ob_get_clean',
                    'ob_get_contents',
                    'ob_get_flush',
                    'ob_get_length',
                    'ob_get_level',
                    'ob_get_status',
                    'ob_gzhandler',
                    'ob_implicit_flush',
                    'ob_list_handlers',
                    'ob_start',
                    'output_add_rewrite_var',
                    'output_reset_rewrite_vars'),
 'Ovrimos SQL': ('ovrimos_close',
                 'ovrimos_commit',
                 'ovrimos_connect',
                 'ovrimos_cursor',
                 'ovrimos_exec',
                 'ovrimos_execute',
                 'ovrimos_fetch_into',
                 'ovrimos_fetch_row',
                 'ovrimos_field_len',
                 'ovrimos_field_name',
                 'ovrimos_field_num',
                 'ovrimos_field_type',
                 'ovrimos_free_result',
                 'ovrimos_longreadlen',
                 'ovrimos_num_fields',
                 'ovrimos_num_rows',
                 'ovrimos_prepare',
                 'ovrimos_result_all',
                 'ovrimos_result',
                 'ovrimos_rollback'),
 'PCNTL': ('pcntl_alarm',
           'pcntl_errno',
           'pcntl_exec',
           'pcntl_fork',
           'pcntl_get_last_error',
           'pcntl_getpriority',
           'pcntl_setpriority',
           'pcntl_signal_dispatch',
           'pcntl_signal',
           'pcntl_sigprocmask',
           'pcntl_sigtimedwait',
           'pcntl_sigwaitinfo',
           'pcntl_strerror',
           'pcntl_wait',
           'pcntl_waitpid',
           'pcntl_wexitstatus',
           'pcntl_wifexited',
           'pcntl_wifsignaled',
           'pcntl_wifstopped',
           'pcntl_wstopsig',
           'pcntl_wtermsig'),
 'PCRE': ('preg_filter',
          'preg_grep',
          'preg_last_error',
          'preg_match_all',
          'preg_match',
          'preg_quote',
          'preg_replace_callback',
          'preg_replace',
          'preg_split'),
 'PDF': ('PDF_activate_item',
         'PDF_add_annotation',
         'PDF_add_bookmark',
         'PDF_add_launchlink',
         'PDF_add_locallink',
         'PDF_add_nameddest',
         'PDF_add_note',
         'PDF_add_outline',
         'PDF_add_pdflink',
         'PDF_add_table_cell',
         'PDF_add_textflow',
         'PDF_add_thumbnail',
         'PDF_add_weblink',
         'PDF_arc',
         'PDF_arcn',
         'PDF_attach_file',
         'PDF_begin_document',
         'PDF_begin_font',
         'PDF_begin_glyph',
         'PDF_begin_item',
         'PDF_begin_layer',
         'PDF_begin_page_ext',
         'PDF_begin_page',
         'PDF_begin_pattern',
         'PDF_begin_template_ext',
         'PDF_begin_template',
         'PDF_circle',
         'PDF_clip',
         'PDF_close_image',
         'PDF_close_pdi_page',
         'PDF_close_pdi',
         'PDF_close',
         'PDF_closepath_fill_stroke',
         'PDF_closepath_stroke',
         'PDF_closepath',
         'PDF_concat',
         'PDF_continue_text',
         'PDF_create_3dview',
         'PDF_create_action',
         'PDF_create_annotation',
         'PDF_create_bookmark',
         'PDF_create_field',
         'PDF_create_fieldgroup',
         'PDF_create_gstate',
         'PDF_create_pvf',
         'PDF_create_textflow',
         'PDF_curveto',
         'PDF_define_layer',
         'PDF_delete_pvf',
         'PDF_delete_table',
         'PDF_delete_textflow',
         'PDF_delete',
         'PDF_encoding_set_char',
         'PDF_end_document',
         'PDF_end_font',
         'PDF_end_glyph',
         'PDF_end_item',
         'PDF_end_layer',
         'PDF_end_page_ext',
         'PDF_end_page',
         'PDF_end_pattern',
         'PDF_end_template',
         'PDF_endpath',
         'PDF_fill_imageblock',
         'PDF_fill_pdfblock',
         'PDF_fill_stroke',
         'PDF_fill_textblock',
         'PDF_fill',
         'PDF_findfont',
         'PDF_fit_image',
         'PDF_fit_pdi_page',
         'PDF_fit_table',
         'PDF_fit_textflow',
         'PDF_fit_textline',
         'PDF_get_apiname',
         'PDF_get_buffer',
         'PDF_get_errmsg',
         'PDF_get_errnum',
         'PDF_get_font',
         'PDF_get_fontname',
         'PDF_get_fontsize',
         'PDF_get_image_height',
         'PDF_get_image_width',
         'PDF_get_majorversion',
         'PDF_get_minorversion',
         'PDF_get_parameter',
         'PDF_get_pdi_parameter',
         'PDF_get_pdi_value',
         'PDF_get_value',
         'PDF_info_font',
         'PDF_info_matchbox',
         'PDF_info_table',
         'PDF_info_textflow',
         'PDF_info_textline',
         'PDF_initgraphics',
         'PDF_lineto',
         'PDF_load_3ddata',
         'PDF_load_font',
         'PDF_load_iccprofile',
         'PDF_load_image',
         'PDF_makespotcolor',
         'PDF_moveto',
         'PDF_new',
         'PDF_open_ccitt',
         'PDF_open_file',
         'PDF_open_gif',
         'PDF_open_image_file',
         'PDF_open_image',
         'PDF_open_jpeg',
         'PDF_open_memory_image',
         'PDF_open_pdi_document',
         'PDF_open_pdi_page',
         'PDF_open_pdi',
         'PDF_open_tiff',
         'PDF_pcos_get_number',
         'PDF_pcos_get_stream',
         'PDF_pcos_get_string',
         'PDF_place_image',
         'PDF_place_pdi_page',
         'PDF_process_pdi',
         'PDF_rect',
         'PDF_restore',
         'PDF_resume_page',
         'PDF_rotate',
         'PDF_save',
         'PDF_scale',
         'PDF_set_border_color',
         'PDF_set_border_dash',
         'PDF_set_border_style',
         'PDF_set_char_spacing',
         'PDF_set_duration',
         'PDF_set_gstate',
         'PDF_set_horiz_scaling',
         'PDF_set_info_author',
         'PDF_set_info_creator',
         'PDF_set_info_keywords',
         'PDF_set_info_subject',
         'PDF_set_info_title',
         'PDF_set_info',
         'PDF_set_layer_dependency',
         'PDF_set_leading',
         'PDF_set_parameter',
         'PDF_set_text_matrix',
         'PDF_set_text_pos',
         'PDF_set_text_rendering',
         'PDF_set_text_rise',
         'PDF_set_value',
         'PDF_set_word_spacing',
         'PDF_setcolor',
         'PDF_setdash',
         'PDF_setdashpattern',
         'PDF_setflat',
         'PDF_setfont',
         'PDF_setgray_fill',
         'PDF_setgray_stroke',
         'PDF_setgray',
         'PDF_setlinecap',
         'PDF_setlinejoin',
         'PDF_setlinewidth',
         'PDF_setmatrix',
         'PDF_setmiterlimit',
         'PDF_setpolydash',
         'PDF_setrgbcolor_fill',
         'PDF_setrgbcolor_stroke',
         'PDF_setrgbcolor',
         'PDF_shading_pattern',
         'PDF_shading',
         'PDF_shfill',
         'PDF_show_boxed',
         'PDF_show_xy',
         'PDF_show',
         'PDF_skew',
         'PDF_stringwidth',
         'PDF_stroke',
         'PDF_suspend_page',
         'PDF_translate',
         'PDF_utf16_to_utf8',
         'PDF_utf32_to_utf16',
         'PDF_utf8_to_utf16'),
 'PHP Options/Info': ('assert_options',
                      'assert',
                      'cli_get_process_title',
                      'cli_set_process_title',
                      'dl',
                      'extension_loaded',
                      'gc_collect_cycles',
                      'gc_disable',
                      'gc_enable',
                      'gc_enabled',
                      'get_cfg_var',
                      'get_current_user',
                      'get_defined_constants',
                      'get_extension_funcs',
                      'get_include_path',
                      'get_included_files',
                      'get_loaded_extensions',
                      'get_magic_quotes_gpc',
                      'get_magic_quotes_runtime',
                      'get_required_files',
                      'getenv',
                      'getlastmod',
                      'getmygid',
                      'getmyinode',
                      'getmypid',
                      'getmyuid',
                      'getopt',
                      'getrusage',
                      'ini_alter',
                      'ini_get_all',
                      'ini_get',
                      'ini_restore',
                      'ini_set',
                      'magic_quotes_runtime',
                      'memory_get_peak_usage',
                      'memory_get_usage',
                      'php_ini_loaded_file',
                      'php_ini_scanned_files',
                      'php_logo_guid',
                      'php_sapi_name',
                      'php_uname',
                      'phpcredits',
                      'phpinfo',
                      'phpversion',
                      'putenv',
                      'restore_include_path',
                      'set_include_path',
                      'set_magic_quotes_runtime',
                      'set_time_limit',
                      'sys_get_temp_dir',
                      'version_compare',
                      'zend_logo_guid',
                      'zend_thread_id',
                      'zend_version'),
 'POSIX': ('posix_access',
           'posix_ctermid',
           'posix_errno',
           'posix_get_last_error',
           'posix_getcwd',
           'posix_getegid',
           'posix_geteuid',
           'posix_getgid',
           'posix_getgrgid',
           'posix_getgrnam',
           'posix_getgroups',
           'posix_getlogin',
           'posix_getpgid',
           'posix_getpgrp',
           'posix_getpid',
           'posix_getppid',
           'posix_getpwnam',
           'posix_getpwuid',
           'posix_getrlimit',
           'posix_getsid',
           'posix_getuid',
           'posix_initgroups',
           'posix_isatty',
           'posix_kill',
           'posix_mkfifo',
           'posix_mknod',
           'posix_setegid',
           'posix_seteuid',
           'posix_setgid',
           'posix_setpgid',
           'posix_setsid',
           'posix_setuid',
           'posix_strerror',
           'posix_times',
           'posix_ttyname',
           'posix_uname'),
 'POSIX Regex': ('ereg_replace',
                 'ereg',
                 'eregi_replace',
                 'eregi',
                 'split',
                 'spliti',
                 'sql_regcase'),
 'PS': ('ps_add_bookmark',
        'ps_add_launchlink',
        'ps_add_locallink',
        'ps_add_note',
        'ps_add_pdflink',
        'ps_add_weblink',
        'ps_arc',
        'ps_arcn',
        'ps_begin_page',
        'ps_begin_pattern',
        'ps_begin_template',
        'ps_circle',
        'ps_clip',
        'ps_close_image',
        'ps_close',
        'ps_closepath_stroke',
        'ps_closepath',
        'ps_continue_text',
        'ps_curveto',
        'ps_delete',
        'ps_end_page',
        'ps_end_pattern',
        'ps_end_template',
        'ps_fill_stroke',
        'ps_fill',
        'ps_findfont',
        'ps_get_buffer',
        'ps_get_parameter',
        'ps_get_value',
        'ps_hyphenate',
        'ps_include_file',
        'ps_lineto',
        'ps_makespotcolor',
        'ps_moveto',
        'ps_new',
        'ps_open_file',
        'ps_open_image_file',
        'ps_open_image',
        'ps_open_memory_image',
        'ps_place_image',
        'ps_rect',
        'ps_restore',
        'ps_rotate',
        'ps_save',
        'ps_scale',
        'ps_set_border_color',
        'ps_set_border_dash',
        'ps_set_border_style',
        'ps_set_info',
        'ps_set_parameter',
        'ps_set_text_pos',
        'ps_set_value',
        'ps_setcolor',
        'ps_setdash',
        'ps_setflat',
        'ps_setfont',
        'ps_setgray',
        'ps_setlinecap',
        'ps_setlinejoin',
        'ps_setlinewidth',
        'ps_setmiterlimit',
        'ps_setoverprintmode',
        'ps_setpolydash',
        'ps_shading_pattern',
        'ps_shading',
        'ps_shfill',
        'ps_show_boxed',
        'ps_show_xy2',
        'ps_show_xy',
        'ps_show2',
        'ps_show',
        'ps_string_geometry',
        'ps_stringwidth',
        'ps_stroke',
        'ps_symbol_name',
        'ps_symbol_width',
        'ps_symbol',
        'ps_translate'),
 'Paradox': ('px_close',
             'px_create_fp',
             'px_date2string',
             'px_delete_record',
             'px_delete',
             'px_get_field',
             'px_get_info',
             'px_get_parameter',
             'px_get_record',
             'px_get_schema',
             'px_get_value',
             'px_insert_record',
             'px_new',
             'px_numfields',
             'px_numrecords',
             'px_open_fp',
             'px_put_record',
             'px_retrieve_record',
             'px_set_blob_file',
             'px_set_parameter',
             'px_set_tablename',
             'px_set_targetencoding',
             'px_set_value',
             'px_timestamp2string',
             'px_update_record'),
 'Parsekit': ('parsekit_compile_file',
              'parsekit_compile_string',
              'parsekit_func_arginfo'),
 'Password Hashing': ('password_get_info',
                      'password_hash',
                      'password_needs_rehash',
                      'password_verify'),
 'PostgreSQL': ('pg_affected_rows',
                'pg_cancel_query',
                'pg_client_encoding',
                'pg_close',
                'pg_connect',
                'pg_connection_busy',
                'pg_connection_reset',
                'pg_connection_status',
                'pg_convert',
                'pg_copy_from',
                'pg_copy_to',
                'pg_dbname',
                'pg_delete',
                'pg_end_copy',
                'pg_escape_bytea',
                'pg_escape_identifier',
                'pg_escape_literal',
                'pg_escape_string',
                'pg_execute',
                'pg_fetch_all_columns',
                'pg_fetch_all',
                'pg_fetch_array',
                'pg_fetch_assoc',
                'pg_fetch_object',
                'pg_fetch_result',
                'pg_fetch_row',
                'pg_field_is_null',
                'pg_field_name',
                'pg_field_num',
                'pg_field_prtlen',
                'pg_field_size',
                'pg_field_table',
                'pg_field_type_oid',
                'pg_field_type',
                'pg_free_result',
                'pg_get_notify',
                'pg_get_pid',
                'pg_get_result',
                'pg_host',
                'pg_insert',
                'pg_last_error',
                'pg_last_notice',
                'pg_last_oid',
                'pg_lo_close',
                'pg_lo_create',
                'pg_lo_export',
                'pg_lo_import',
                'pg_lo_open',
                'pg_lo_read_all',
                'pg_lo_read',
                'pg_lo_seek',
                'pg_lo_tell',
                'pg_lo_truncate',
                'pg_lo_unlink',
                'pg_lo_write',
                'pg_meta_data',
                'pg_num_fields',
                'pg_num_rows',
                'pg_options',
                'pg_parameter_status',
                'pg_pconnect',
                'pg_ping',
                'pg_port',
                'pg_prepare',
                'pg_put_line',
                'pg_query_params',
                'pg_query',
                'pg_result_error_field',
                'pg_result_error',
                'pg_result_seek',
                'pg_result_status',
                'pg_select',
                'pg_send_execute',
                'pg_send_prepare',
                'pg_send_query_params',
                'pg_send_query',
                'pg_set_client_encoding',
                'pg_set_error_verbosity',
                'pg_trace',
                'pg_transaction_status',
                'pg_tty',
                'pg_unescape_bytea',
                'pg_untrace',
                'pg_update',
                'pg_version'),
 'Printer': ('printer_abort',
             'printer_close',
             'printer_create_brush',
             'printer_create_dc',
             'printer_create_font',
             'printer_create_pen',
             'printer_delete_brush',
             'printer_delete_dc',
             'printer_delete_font',
             'printer_delete_pen',
             'printer_draw_bmp',
             'printer_draw_chord',
             'printer_draw_elipse',
             'printer_draw_line',
             'printer_draw_pie',
             'printer_draw_rectangle',
             'printer_draw_roundrect',
             'printer_draw_text',
             'printer_end_doc',
             'printer_end_page',
             'printer_get_option',
             'printer_list',
             'printer_logical_fontheight',
             'printer_open',
             'printer_select_brush',
             'printer_select_font',
             'printer_select_pen',
             'printer_set_option',
             'printer_start_doc',
             'printer_start_page',
             'printer_write'),
 'Proctitle': ('setproctitle', 'setthreadtitle'),
 'Program execution': ('escapeshellarg',
                       'escapeshellcmd',
                       'exec',
                       'passthru',
                       'proc_close',
                       'proc_get_status',
                       'proc_nice',
                       'proc_open',
                       'proc_terminate',
                       'shell_exec',
                       'system'),
 'Pspell': ('pspell_add_to_personal',
            'pspell_add_to_session',
            'pspell_check',
            'pspell_clear_session',
            'pspell_config_create',
            'pspell_config_data_dir',
            'pspell_config_dict_dir',
            'pspell_config_ignore',
            'pspell_config_mode',
            'pspell_config_personal',
            'pspell_config_repl',
            'pspell_config_runtogether',
            'pspell_config_save_repl',
            'pspell_new_config',
            'pspell_new_personal',
            'pspell_new',
            'pspell_save_wordlist',
            'pspell_store_replacement',
            'pspell_suggest'),
 'RPM Reader': ('rpm_close',
                'rpm_get_tag',
                'rpm_is_valid',
                'rpm_open',
                'rpm_version'),
 'RRD': ('rrd_create',
         'rrd_error',
         'rrd_fetch',
         'rrd_first',
         'rrd_graph',
         'rrd_info',
         'rrd_last',
         'rrd_lastupdate',
         'rrd_restore',
         'rrd_tune',
         'rrd_update',
         'rrd_version',
         'rrd_xport',
         'rrdc_disconnect'),
 'Radius': ('radius_acct_open',
            'radius_add_server',
            'radius_auth_open',
            'radius_close',
            'radius_config',
            'radius_create_request',
            'radius_cvt_addr',
            'radius_cvt_int',
            'radius_cvt_string',
            'radius_demangle_mppe_key',
            'radius_demangle',
            'radius_get_attr',
            'radius_get_tagged_attr_data',
            'radius_get_tagged_attr_tag',
            'radius_get_vendor_attr',
            'radius_put_addr',
            'radius_put_attr',
            'radius_put_int',
            'radius_put_string',
            'radius_put_vendor_addr',
            'radius_put_vendor_attr',
            'radius_put_vendor_int',
            'radius_put_vendor_string',
            'radius_request_authenticator',
            'radius_salt_encrypt_attr',
            'radius_send_request',
            'radius_server_secret',
            'radius_strerror'),
 'Rar': ('rar_wrapper_cache_stats',),
 'Readline': ('readline_add_history',
              'readline_callback_handler_install',
              'readline_callback_handler_remove',
              'readline_callback_read_char',
              'readline_clear_history',
              'readline_completion_function',
              'readline_info',
              'readline_list_history',
              'readline_on_new_line',
              'readline_read_history',
              'readline_redisplay',
              'readline_write_history',
              'readline'),
 'Recode': ('recode_file', 'recode_string', 'recode'),
 'SNMP': ('snmp_get_quick_print',
          'snmp_get_valueretrieval',
          'snmp_read_mib',
          'snmp_set_enum_print',
          'snmp_set_oid_numeric_print',
          'snmp_set_oid_output_format',
          'snmp_set_quick_print',
          'snmp_set_valueretrieval',
          'snmp2_get',
          'snmp2_getnext',
          'snmp2_real_walk',
          'snmp2_set',
          'snmp2_walk',
          'snmp3_get',
          'snmp3_getnext',
          'snmp3_real_walk',
          'snmp3_set',
          'snmp3_walk',
          'snmpget',
          'snmpgetnext',
          'snmprealwalk',
          'snmpset',
          'snmpwalk',
          'snmpwalkoid'),
 'SOAP': ('is_soap_fault', 'use_soap_error_handler'),
 'SPL': ('class_implements',
         'class_parents',
         'class_uses',
         'iterator_apply',
         'iterator_count',
         'iterator_to_array',
         'spl_autoload_call',
         'spl_autoload_extensions',
         'spl_autoload_functions',
         'spl_autoload_register',
         'spl_autoload_unregister',
         'spl_autoload',
         'spl_classes',
         'spl_object_hash'),
 'SPPLUS': ('calcul_hmac', 'calculhmac', 'nthmac', 'signeurlpaiement'),
 'SQLSRV': ('sqlsrv_begin_transaction',
            'sqlsrv_cancel',
            'sqlsrv_client_info',
            'sqlsrv_close',
            'sqlsrv_commit',
            'sqlsrv_configure',
            'sqlsrv_connect',
            'sqlsrv_errors',
            'sqlsrv_execute',
            'sqlsrv_fetch_array',
            'sqlsrv_fetch_object',
            'sqlsrv_fetch',
            'sqlsrv_field_metadata',
            'sqlsrv_free_stmt',
            'sqlsrv_get_config',
            'sqlsrv_get_field',
            'sqlsrv_has_rows',
            'sqlsrv_next_result',
            'sqlsrv_num_fields',
            'sqlsrv_num_rows',
            'sqlsrv_prepare',
            'sqlsrv_query',
            'sqlsrv_rollback',
            'sqlsrv_rows_affected',
            'sqlsrv_send_stream_data',
            'sqlsrv_server_info'),
 'SQLite': ('sqlite_array_query',
            'sqlite_busy_timeout',
            'sqlite_changes',
            'sqlite_close',
            'sqlite_column',
            'sqlite_create_aggregate',
            'sqlite_create_function',
            'sqlite_current',
            'sqlite_error_string',
            'sqlite_escape_string',
            'sqlite_exec',
            'sqlite_factory',
            'sqlite_fetch_all',
            'sqlite_fetch_array',
            'sqlite_fetch_column_types',
            'sqlite_fetch_object',
            'sqlite_fetch_single',
            'sqlite_fetch_string',
            'sqlite_field_name',
            'sqlite_has_more',
            'sqlite_has_prev',
            'sqlite_key',
            'sqlite_last_error',
            'sqlite_last_insert_rowid',
            'sqlite_libencoding',
            'sqlite_libversion',
            'sqlite_next',
            'sqlite_num_fields',
            'sqlite_num_rows',
            'sqlite_open',
            'sqlite_popen',
            'sqlite_prev',
            'sqlite_query',
            'sqlite_rewind',
            'sqlite_seek',
            'sqlite_single_query',
            'sqlite_udf_decode_binary',
            'sqlite_udf_encode_binary',
            'sqlite_unbuffered_query',
            'sqlite_valid'),
 'SSH2': ('ssh2_auth_agent',
          'ssh2_auth_hostbased_file',
          'ssh2_auth_none',
          'ssh2_auth_password',
          'ssh2_auth_pubkey_file',
          'ssh2_connect',
          'ssh2_exec',
          'ssh2_fetch_stream',
          'ssh2_fingerprint',
          'ssh2_methods_negotiated',
          'ssh2_publickey_add',
          'ssh2_publickey_init',
          'ssh2_publickey_list',
          'ssh2_publickey_remove',
          'ssh2_scp_recv',
          'ssh2_scp_send',
          'ssh2_sftp_chmod',
          'ssh2_sftp_lstat',
          'ssh2_sftp_mkdir',
          'ssh2_sftp_readlink',
          'ssh2_sftp_realpath',
          'ssh2_sftp_rename',
          'ssh2_sftp_rmdir',
          'ssh2_sftp_stat',
          'ssh2_sftp_symlink',
          'ssh2_sftp_unlink',
          'ssh2_sftp',
          'ssh2_shell',
          'ssh2_tunnel'),
 'SVN': ('svn_add',
         'svn_auth_get_parameter',
         'svn_auth_set_parameter',
         'svn_blame',
         'svn_cat',
         'svn_checkout',
         'svn_cleanup',
         'svn_client_version',
         'svn_commit',
         'svn_delete',
         'svn_diff',
         'svn_export',
         'svn_fs_abort_txn',
         'svn_fs_apply_text',
         'svn_fs_begin_txn2',
         'svn_fs_change_node_prop',
         'svn_fs_check_path',
         'svn_fs_contents_changed',
         'svn_fs_copy',
         'svn_fs_delete',
         'svn_fs_dir_entries',
         'svn_fs_file_contents',
         'svn_fs_file_length',
         'svn_fs_is_dir',
         'svn_fs_is_file',
         'svn_fs_make_dir',
         'svn_fs_make_file',
         'svn_fs_node_created_rev',
         'svn_fs_node_prop',
         'svn_fs_props_changed',
         'svn_fs_revision_prop',
         'svn_fs_revision_root',
         'svn_fs_txn_root',
         'svn_fs_youngest_rev',
         'svn_import',
         'svn_log',
         'svn_ls',
         'svn_mkdir',
         'svn_repos_create',
         'svn_repos_fs_begin_txn_for_commit',
         'svn_repos_fs_commit_txn',
         'svn_repos_fs',
         'svn_repos_hotcopy',
         'svn_repos_open',
         'svn_repos_recover',
         'svn_revert',
         'svn_status',
         'svn_update'),
 'SWF': ('swf_actiongeturl',
         'swf_actiongotoframe',
         'swf_actiongotolabel',
         'swf_actionnextframe',
         'swf_actionplay',
         'swf_actionprevframe',
         'swf_actionsettarget',
         'swf_actionstop',
         'swf_actiontogglequality',
         'swf_actionwaitforframe',
         'swf_addbuttonrecord',
         'swf_addcolor',
         'swf_closefile',
         'swf_definebitmap',
         'swf_definefont',
         'swf_defineline',
         'swf_definepoly',
         'swf_definerect',
         'swf_definetext',
         'swf_endbutton',
         'swf_enddoaction',
         'swf_endshape',
         'swf_endsymbol',
         'swf_fontsize',
         'swf_fontslant',
         'swf_fonttracking',
         'swf_getbitmapinfo',
         'swf_getfontinfo',
         'swf_getframe',
         'swf_labelframe',
         'swf_lookat',
         'swf_modifyobject',
         'swf_mulcolor',
         'swf_nextid',
         'swf_oncondition',
         'swf_openfile',
         'swf_ortho2',
         'swf_ortho',
         'swf_perspective',
         'swf_placeobject',
         'swf_polarview',
         'swf_popmatrix',
         'swf_posround',
         'swf_pushmatrix',
         'swf_removeobject',
         'swf_rotate',
         'swf_scale',
         'swf_setfont',
         'swf_setframe',
         'swf_shapearc',
         'swf_shapecurveto3',
         'swf_shapecurveto',
         'swf_shapefillbitmapclip',
         'swf_shapefillbitmaptile',
         'swf_shapefilloff',
         'swf_shapefillsolid',
         'swf_shapelinesolid',
         'swf_shapelineto',
         'swf_shapemoveto',
         'swf_showframe',
         'swf_startbutton',
         'swf_startdoaction',
         'swf_startshape',
         'swf_startsymbol',
         'swf_textwidth',
         'swf_translate',
         'swf_viewport'),
 'Semaphore': ('ftok',
               'msg_get_queue',
               'msg_queue_exists',
               'msg_receive',
               'msg_remove_queue',
               'msg_send',
               'msg_set_queue',
               'msg_stat_queue',
               'sem_acquire',
               'sem_get',
               'sem_release',
               'sem_remove',
               'shm_attach',
               'shm_detach',
               'shm_get_var',
               'shm_has_var',
               'shm_put_var',
               'shm_remove_var',
               'shm_remove'),
 'Session': ('session_cache_expire',
             'session_cache_limiter',
             'session_commit',
             'session_decode',
             'session_destroy',
             'session_encode',
             'session_get_cookie_params',
             'session_id',
             'session_is_registered',
             'session_module_name',
             'session_name',
             'session_regenerate_id',
             'session_register_shutdown',
             'session_register',
             'session_save_path',
             'session_set_cookie_params',
             'session_set_save_handler',
             'session_start',
             'session_status',
             'session_unregister',
             'session_unset',
             'session_write_close'),
 'Session PgSQL': ('session_pgsql_add_error',
                   'session_pgsql_get_error',
                   'session_pgsql_get_field',
                   'session_pgsql_reset',
                   'session_pgsql_set_field',
                   'session_pgsql_status'),
 'Shared Memory': ('shmop_close',
                   'shmop_delete',
                   'shmop_open',
                   'shmop_read',
                   'shmop_size',
                   'shmop_write'),
 'SimpleXML': ('simplexml_import_dom',
               'simplexml_load_file',
               'simplexml_load_string'),
 'Socket': ('socket_accept',
            'socket_bind',
            'socket_clear_error',
            'socket_close',
            'socket_cmsg_space',
            'socket_connect',
            'socket_create_listen',
            'socket_create_pair',
            'socket_create',
            'socket_get_option',
            'socket_getpeername',
            'socket_getsockname',
            'socket_import_stream',
            'socket_last_error',
            'socket_listen',
            'socket_read',
            'socket_recv',
            'socket_recvfrom',
            'socket_recvmsg',
            'socket_select',
            'socket_send',
            'socket_sendmsg',
            'socket_sendto',
            'socket_set_block',
            'socket_set_nonblock',
            'socket_set_option',
            'socket_shutdown',
            'socket_strerror',
            'socket_write'),
 'Solr': ('solr_get_version',),
 'Statistic': ('stats_absolute_deviation',
               'stats_cdf_beta',
               'stats_cdf_binomial',
               'stats_cdf_cauchy',
               'stats_cdf_chisquare',
               'stats_cdf_exponential',
               'stats_cdf_f',
               'stats_cdf_gamma',
               'stats_cdf_laplace',
               'stats_cdf_logistic',
               'stats_cdf_negative_binomial',
               'stats_cdf_noncentral_chisquare',
               'stats_cdf_noncentral_f',
               'stats_cdf_poisson',
               'stats_cdf_t',
               'stats_cdf_uniform',
               'stats_cdf_weibull',
               'stats_covariance',
               'stats_den_uniform',
               'stats_dens_beta',
               'stats_dens_cauchy',
               'stats_dens_chisquare',
               'stats_dens_exponential',
               'stats_dens_f',
               'stats_dens_gamma',
               'stats_dens_laplace',
               'stats_dens_logistic',
               'stats_dens_negative_binomial',
               'stats_dens_normal',
               'stats_dens_pmf_binomial',
               'stats_dens_pmf_hypergeometric',
               'stats_dens_pmf_poisson',
               'stats_dens_t',
               'stats_dens_weibull',
               'stats_harmonic_mean',
               'stats_kurtosis',
               'stats_rand_gen_beta',
               'stats_rand_gen_chisquare',
               'stats_rand_gen_exponential',
               'stats_rand_gen_f',
               'stats_rand_gen_funiform',
               'stats_rand_gen_gamma',
               'stats_rand_gen_ibinomial_negative',
               'stats_rand_gen_ibinomial',
               'stats_rand_gen_int',
               'stats_rand_gen_ipoisson',
               'stats_rand_gen_iuniform',
               'stats_rand_gen_noncenral_chisquare',
               'stats_rand_gen_noncentral_f',
               'stats_rand_gen_noncentral_t',
               'stats_rand_gen_normal',
               'stats_rand_gen_t',
               'stats_rand_get_seeds',
               'stats_rand_phrase_to_seeds',
               'stats_rand_ranf',
               'stats_rand_setall',
               'stats_skew',
               'stats_standard_deviation',
               'stats_stat_binomial_coef',
               'stats_stat_correlation',
               'stats_stat_gennch',
               'stats_stat_independent_t',
               'stats_stat_innerproduct',
               'stats_stat_noncentral_t',
               'stats_stat_paired_t',
               'stats_stat_percentile',
               'stats_stat_powersum',
               'stats_variance'),
 'Stomp': ('stomp_connect_error', 'stomp_version'),
 'Stream': ('set_socket_blocking',
            'stream_bucket_append',
            'stream_bucket_make_writeable',
            'stream_bucket_new',
            'stream_bucket_prepend',
            'stream_context_create',
            'stream_context_get_default',
            'stream_context_get_options',
            'stream_context_get_params',
            'stream_context_set_default',
            'stream_context_set_option',
            'stream_context_set_params',
            'stream_copy_to_stream',
            'stream_encoding',
            'stream_filter_append',
            'stream_filter_prepend',
            'stream_filter_register',
            'stream_filter_remove',
            'stream_get_contents',
            'stream_get_filters',
            'stream_get_line',
            'stream_get_meta_data',
            'stream_get_transports',
            'stream_get_wrappers',
            'stream_is_local',
            'stream_notification_callback',
            'stream_register_wrapper',
            'stream_resolve_include_path',
            'stream_select',
            'stream_set_blocking',
            'stream_set_chunk_size',
            'stream_set_read_buffer',
            'stream_set_timeout',
            'stream_set_write_buffer',
            'stream_socket_accept',
            'stream_socket_client',
            'stream_socket_enable_crypto',
            'stream_socket_get_name',
            'stream_socket_pair',
            'stream_socket_recvfrom',
            'stream_socket_sendto',
            'stream_socket_server',
            'stream_socket_shutdown',
            'stream_supports_lock',
            'stream_wrapper_register',
            'stream_wrapper_restore',
            'stream_wrapper_unregister'),
 'String': ('addcslashes',
            'addslashes',
            'bin2hex',
            'chop',
            'chr',
            'chunk_split',
            'convert_cyr_string',
            'convert_uudecode',
            'convert_uuencode',
            'count_chars',
            'crc32',
            'crypt',
            'echo',
            'explode',
            'fprintf',
            'get_html_translation_table',
            'hebrev',
            'hebrevc',
            'hex2bin',
            'html_entity_decode',
            'htmlentities',
            'htmlspecialchars_decode',
            'htmlspecialchars',
            'implode',
            'join',
            'lcfirst',
            'levenshtein',
            'localeconv',
            'ltrim',
            'md5_file',
            'md5',
            'metaphone',
            'money_format',
            'nl_langinfo',
            'nl2br',
            'number_format',
            'ord',
            'parse_str',
            'print',
            'printf',
            'quoted_printable_decode',
            'quoted_printable_encode',
            'quotemeta',
            'rtrim',
            'setlocale',
            'sha1_file',
            'sha1',
            'similar_text',
            'soundex',
            'sprintf',
            'sscanf',
            'str_getcsv',
            'str_ireplace',
            'str_pad',
            'str_repeat',
            'str_replace',
            'str_rot13',
            'str_shuffle',
            'str_split',
            'str_word_count',
            'strcasecmp',
            'strchr',
            'strcmp',
            'strcoll',
            'strcspn',
            'strip_tags',
            'stripcslashes',
            'stripos',
            'stripslashes',
            'stristr',
            'strlen',
            'strnatcasecmp',
            'strnatcmp',
            'strncasecmp',
            'strncmp',
            'strpbrk',
            'strpos',
            'strrchr',
            'strrev',
            'strripos',
            'strrpos',
            'strspn',
            'strstr',
            'strtok',
            'strtolower',
            'strtoupper',
            'strtr',
            'substr_compare',
            'substr_count',
            'substr_replace',
            'substr',
            'trim',
            'ucfirst',
            'ucwords',
            'vfprintf',
            'vprintf',
            'vsprintf',
            'wordwrap'),
 'Sybase': ('sybase_affected_rows',
            'sybase_close',
            'sybase_connect',
            'sybase_data_seek',
            'sybase_deadlock_retry_count',
            'sybase_fetch_array',
            'sybase_fetch_assoc',
            'sybase_fetch_field',
            'sybase_fetch_object',
            'sybase_fetch_row',
            'sybase_field_seek',
            'sybase_free_result',
            'sybase_get_last_message',
            'sybase_min_client_severity',
            'sybase_min_error_severity',
            'sybase_min_message_severity',
            'sybase_min_server_severity',
            'sybase_num_fields',
            'sybase_num_rows',
            'sybase_pconnect',
            'sybase_query',
            'sybase_result',
            'sybase_select_db',
            'sybase_set_message_handler',
            'sybase_unbuffered_query'),
 'TCP': ('tcpwrap_check',),
 'Taint': ('is_tainted', 'taint', 'untaint'),
 'Tidy': ('ob_tidyhandler',
          'tidy_access_count',
          'tidy_config_count',
          'tidy_error_count',
          'tidy_get_output',
          'tidy_load_config',
          'tidy_reset_config',
          'tidy_save_config',
          'tidy_set_encoding',
          'tidy_setopt',
          'tidy_warning_count'),
 'Tokenizer': ('token_get_all', 'token_name'),
 'Trader': ('trader_acos',
            'trader_ad',
            'trader_add',
            'trader_adosc',
            'trader_adx',
            'trader_adxr',
            'trader_apo',
            'trader_aroon',
            'trader_aroonosc',
            'trader_asin',
            'trader_atan',
            'trader_atr',
            'trader_avgprice',
            'trader_bbands',
            'trader_beta',
            'trader_bop',
            'trader_cci',
            'trader_cdl2crows',
            'trader_cdl3blackcrows',
            'trader_cdl3inside',
            'trader_cdl3linestrike',
            'trader_cdl3outside',
            'trader_cdl3starsinsouth',
            'trader_cdl3whitesoldiers',
            'trader_cdlabandonedbaby',
            'trader_cdladvanceblock',
            'trader_cdlbelthold',
            'trader_cdlbreakaway',
            'trader_cdlclosingmarubozu',
            'trader_cdlconcealbabyswall',
            'trader_cdlcounterattack',
            'trader_cdldarkcloudcover',
            'trader_cdldoji',
            'trader_cdldojistar',
            'trader_cdldragonflydoji',
            'trader_cdlengulfing',
            'trader_cdleveningdojistar',
            'trader_cdleveningstar',
            'trader_cdlgapsidesidewhite',
            'trader_cdlgravestonedoji',
            'trader_cdlhammer',
            'trader_cdlhangingman',
            'trader_cdlharami',
            'trader_cdlharamicross',
            'trader_cdlhighwave',
            'trader_cdlhikkake',
            'trader_cdlhikkakemod',
            'trader_cdlhomingpigeon',
            'trader_cdlidentical3crows',
            'trader_cdlinneck',
            'trader_cdlinvertedhammer',
            'trader_cdlkicking',
            'trader_cdlkickingbylength',
            'trader_cdlladderbottom',
            'trader_cdllongleggeddoji',
            'trader_cdllongline',
            'trader_cdlmarubozu',
            'trader_cdlmatchinglow',
            'trader_cdlmathold',
            'trader_cdlmorningdojistar',
            'trader_cdlmorningstar',
            'trader_cdlonneck',
            'trader_cdlpiercing',
            'trader_cdlrickshawman',
            'trader_cdlrisefall3methods',
            'trader_cdlseparatinglines',
            'trader_cdlshootingstar',
            'trader_cdlshortline',
            'trader_cdlspinningtop',
            'trader_cdlstalledpattern',
            'trader_cdlsticksandwich',
            'trader_cdltakuri',
            'trader_cdltasukigap',
            'trader_cdlthrusting',
            'trader_cdltristar',
            'trader_cdlunique3river',
            'trader_cdlupsidegap2crows',
            'trader_cdlxsidegap3methods',
            'trader_ceil',
            'trader_cmo',
            'trader_correl',
            'trader_cos',
            'trader_cosh',
            'trader_dema',
            'trader_div',
            'trader_dx',
            'trader_ema',
            'trader_errno',
            'trader_exp',
            'trader_floor',
            'trader_get_compat',
            'trader_get_unstable_period',
            'trader_ht_dcperiod',
            'trader_ht_dcphase',
            'trader_ht_phasor',
            'trader_ht_sine',
            'trader_ht_trendline',
            'trader_ht_trendmode',
            'trader_kama',
            'trader_linearreg_angle',
            'trader_linearreg_intercept',
            'trader_linearreg_slope',
            'trader_linearreg',
            'trader_ln',
            'trader_log10',
            'trader_ma',
            'trader_macd',
            'trader_macdext',
            'trader_macdfix',
            'trader_mama',
            'trader_mavp',
            'trader_max',
            'trader_maxindex',
            'trader_medprice',
            'trader_mfi',
            'trader_midpoint',
            'trader_midprice',
            'trader_min',
            'trader_minindex',
            'trader_minmax',
            'trader_minmaxindex',
            'trader_minus_di',
            'trader_minus_dm',
            'trader_mom',
            'trader_mult',
            'trader_natr',
            'trader_obv',
            'trader_plus_di',
            'trader_plus_dm',
            'trader_ppo',
            'trader_roc',
            'trader_rocp',
            'trader_rocr100',
            'trader_rocr',
            'trader_rsi',
            'trader_sar',
            'trader_sarext',
            'trader_set_compat',
            'trader_set_unstable_period',
            'trader_sin',
            'trader_sinh',
            'trader_sma',
            'trader_sqrt',
            'trader_stddev',
            'trader_stoch',
            'trader_stochf',
            'trader_stochrsi',
            'trader_sub',
            'trader_sum',
            'trader_t3',
            'trader_tan',
            'trader_tanh',
            'trader_tema',
            'trader_trange',
            'trader_trima',
            'trader_trix',
            'trader_tsf',
            'trader_typprice',
            'trader_ultosc',
            'trader_var',
            'trader_wclprice',
            'trader_willr',
            'trader_wma'),
 'URL': ('base64_decode',
         'base64_encode',
         'get_headers',
         'get_meta_tags',
         'http_build_query',
         'parse_url',
         'rawurldecode',
         'rawurlencode',
         'urldecode',
         'urlencode'),
 'Uopz': ('uopz_backup',
          'uopz_compose',
          'uopz_copy',
          'uopz_delete',
          'uopz_extend',
          'uopz_flags',
          'uopz_function',
          'uopz_implement',
          'uopz_overload',
          'uopz_redefine',
          'uopz_rename',
          'uopz_restore',
          'uopz_undefine'),
 'Variable handling': ('boolval',
                       'debug_zval_dump',
                       'doubleval',
                       'empty',
                       'floatval',
                       'get_defined_vars',
                       'get_resource_type',
                       'gettype',
                       'import_request_variables',
                       'intval',
                       'is_array',
                       'is_bool',
                       'is_callable',
                       'is_double',
                       'is_float',
                       'is_int',
                       'is_integer',
                       'is_long',
                       'is_null',
                       'is_numeric',
                       'is_object',
                       'is_real',
                       'is_resource',
                       'is_scalar',
                       'is_string',
                       'isset',
                       'print_r',
                       'serialize',
                       'settype',
                       'strval',
                       'unserialize',
                       'unset',
                       'var_dump',
                       'var_export'),
 'W32api': ('w32api_deftype',
            'w32api_init_dtype',
            'w32api_invoke_function',
            'w32api_register_function',
            'w32api_set_call_method'),
 'WDDX': ('wddx_add_vars',
          'wddx_deserialize',
          'wddx_packet_end',
          'wddx_packet_start',
          'wddx_serialize_value',
          'wddx_serialize_vars'),
 'WinCache': ('wincache_fcache_fileinfo',
              'wincache_fcache_meminfo',
              'wincache_lock',
              'wincache_ocache_fileinfo',
              'wincache_ocache_meminfo',
              'wincache_refresh_if_changed',
              'wincache_rplist_fileinfo',
              'wincache_rplist_meminfo',
              'wincache_scache_info',
              'wincache_scache_meminfo',
              'wincache_ucache_add',
              'wincache_ucache_cas',
              'wincache_ucache_clear',
              'wincache_ucache_dec',
              'wincache_ucache_delete',
              'wincache_ucache_exists',
              'wincache_ucache_get',
              'wincache_ucache_inc',
              'wincache_ucache_info',
              'wincache_ucache_meminfo',
              'wincache_ucache_set',
              'wincache_unlock'),
 'XML Parser': ('utf8_decode',
                'utf8_encode',
                'xml_error_string',
                'xml_get_current_byte_index',
                'xml_get_current_column_number',
                'xml_get_current_line_number',
                'xml_get_error_code',
                'xml_parse_into_struct',
                'xml_parse',
                'xml_parser_create_ns',
                'xml_parser_create',
                'xml_parser_free',
                'xml_parser_get_option',
                'xml_parser_set_option',
                'xml_set_character_data_handler',
                'xml_set_default_handler',
                'xml_set_element_handler',
                'xml_set_end_namespace_decl_handler',
                'xml_set_external_entity_ref_handler',
                'xml_set_notation_decl_handler',
                'xml_set_object',
                'xml_set_processing_instruction_handler',
                'xml_set_start_namespace_decl_handler',
                'xml_set_unparsed_entity_decl_handler'),
 'XML-RPC': ('xmlrpc_decode_request',
             'xmlrpc_decode',
             'xmlrpc_encode_request',
             'xmlrpc_encode',
             'xmlrpc_get_type',
             'xmlrpc_is_fault',
             'xmlrpc_parse_method_descriptions',
             'xmlrpc_server_add_introspection_data',
             'xmlrpc_server_call_method',
             'xmlrpc_server_create',
             'xmlrpc_server_destroy',
             'xmlrpc_server_register_introspection_callback',
             'xmlrpc_server_register_method',
             'xmlrpc_set_type'),
 'XSLT (PHP 4)': ('xslt_backend_info',
                  'xslt_backend_name',
                  'xslt_backend_version',
                  'xslt_create',
                  'xslt_errno',
                  'xslt_error',
                  'xslt_free',
                  'xslt_getopt',
                  'xslt_process',
                  'xslt_set_base',
                  'xslt_set_encoding',
                  'xslt_set_error_handler',
                  'xslt_set_log',
                  'xslt_set_object',
                  'xslt_set_sax_handler',
                  'xslt_set_sax_handlers',
                  'xslt_set_scheme_handler',
                  'xslt_set_scheme_handlers',
                  'xslt_setopt'),
 'Xhprof': ('xhprof_disable',
            'xhprof_enable',
            'xhprof_sample_disable',
            'xhprof_sample_enable'),
 'YAZ': ('yaz_addinfo',
         'yaz_ccl_conf',
         'yaz_ccl_parse',
         'yaz_close',
         'yaz_connect',
         'yaz_database',
         'yaz_element',
         'yaz_errno',
         'yaz_error',
         'yaz_es_result',
         'yaz_es',
         'yaz_get_option',
         'yaz_hits',
         'yaz_itemorder',
         'yaz_present',
         'yaz_range',
         'yaz_record',
         'yaz_scan_result',
         'yaz_scan',
         'yaz_schema',
         'yaz_search',
         'yaz_set_option',
         'yaz_sort',
         'yaz_syntax',
         'yaz_wait'),
 'YP/NIS': ('yp_all',
            'yp_cat',
            'yp_err_string',
            'yp_errno',
            'yp_first',
            'yp_get_default_domain',
            'yp_master',
            'yp_match',
            'yp_next',
            'yp_order'),
 'Yaml': ('yaml_emit_file',
          'yaml_emit',
          'yaml_parse_file',
          'yaml_parse_url',
          'yaml_parse'),
 'Zip': ('zip_close',
         'zip_entry_close',
         'zip_entry_compressedsize',
         'zip_entry_compressionmethod',
         'zip_entry_filesize',
         'zip_entry_name',
         'zip_entry_open',
         'zip_entry_read',
         'zip_open',
         'zip_read'),
 'Zlib': ('gzclose',
          'gzcompress',
          'gzdecode',
          'gzdeflate',
          'gzencode',
          'gzeof',
          'gzfile',
          'gzgetc',
          'gzgets',
          'gzgetss',
          'gzinflate',
          'gzopen',
          'gzpassthru',
          'gzputs',
          'gzread',
          'gzrewind',
          'gzseek',
          'gztell',
          'gzuncompress',
          'gzwrite',
          'readgzfile',
          'zlib_decode',
          'zlib_encode',
          'zlib_get_coding_type'),
 'bcompiler': ('bcompiler_load_exe',
               'bcompiler_load',
               'bcompiler_parse_class',
               'bcompiler_read',
               'bcompiler_write_class',
               'bcompiler_write_constant',
               'bcompiler_write_exe_footer',
               'bcompiler_write_file',
               'bcompiler_write_footer',
               'bcompiler_write_function',
               'bcompiler_write_functions_from_file',
               'bcompiler_write_header',
               'bcompiler_write_included_filename'),
 'cURL': ('curl_close',
          'curl_copy_handle',
          'curl_errno',
          'curl_error',
          'curl_escape',
          'curl_exec',
          'curl_file_create',
          'curl_getinfo',
          'curl_init',
          'curl_multi_add_handle',
          'curl_multi_close',
          'curl_multi_exec',
          'curl_multi_getcontent',
          'curl_multi_info_read',
          'curl_multi_init',
          'curl_multi_remove_handle',
          'curl_multi_select',
          'curl_multi_setopt',
          'curl_multi_strerror',
          'curl_pause',
          'curl_reset',
          'curl_setopt_array',
          'curl_setopt',
          'curl_share_close',
          'curl_share_init',
          'curl_share_setopt',
          'curl_strerror',
          'curl_unescape',
          'curl_version'),
 'chdb': ('chdb_create',),
 'dBase': ('dbase_add_record',
           'dbase_close',
           'dbase_create',
           'dbase_delete_record',
           'dbase_get_header_info',
           'dbase_get_record_with_names',
           'dbase_get_record',
           'dbase_numfields',
           'dbase_numrecords',
           'dbase_open',
           'dbase_pack',
           'dbase_replace_record'),
 'dbx': ('dbx_close',
         'dbx_compare',
         'dbx_connect',
         'dbx_error',
         'dbx_escape_string',
         'dbx_fetch_row',
         'dbx_query',
         'dbx_sort'),
 'filePro': ('filepro_fieldcount',
             'filepro_fieldname',
             'filepro_fieldtype',
             'filepro_fieldwidth',
             'filepro_retrieve',
             'filepro_rowcount',
             'filepro'),
 'iconv': ('iconv_get_encoding',
           'iconv_mime_decode_headers',
           'iconv_mime_decode',
           'iconv_mime_encode',
           'iconv_set_encoding',
           'iconv_strlen',
           'iconv_strpos',
           'iconv_strrpos',
           'iconv_substr',
           'iconv',
           'ob_iconv_handler'),
 'inclued': ('inclued_get_data',),
 'intl': ('intl_error_name',
          'intl_get_error_code',
          'intl_get_error_message',
          'intl_is_failure'),
 'libxml': ('libxml_clear_errors',
            'libxml_disable_entity_loader',
            'libxml_get_errors',
            'libxml_get_last_error',
            'libxml_set_external_entity_loader',
            'libxml_set_streams_context',
            'libxml_use_internal_errors'),
 'mSQL': ('msql_affected_rows',
          'msql_close',
          'msql_connect',
          'msql_create_db',
          'msql_createdb',
          'msql_data_seek',
          'msql_db_query',
          'msql_dbname',
          'msql_drop_db',
          'msql_error',
          'msql_fetch_array',
          'msql_fetch_field',
          'msql_fetch_object',
          'msql_fetch_row',
          'msql_field_flags',
          'msql_field_len',
          'msql_field_name',
          'msql_field_seek',
          'msql_field_table',
          'msql_field_type',
          'msql_fieldflags',
          'msql_fieldlen',
          'msql_fieldname',
          'msql_fieldtable',
          'msql_fieldtype',
          'msql_free_result',
          'msql_list_dbs',
          'msql_list_fields',
          'msql_list_tables',
          'msql_num_fields',
          'msql_num_rows',
          'msql_numfields',
          'msql_numrows',
          'msql_pconnect',
          'msql_query',
          'msql_regcase',
          'msql_result',
          'msql_select_db',
          'msql_tablename',
          'msql'),
 'mnoGoSearch': ('udm_add_search_limit',
                 'udm_alloc_agent_array',
                 'udm_alloc_agent',
                 'udm_api_version',
                 'udm_cat_list',
                 'udm_cat_path',
                 'udm_check_charset',
                 'udm_check_stored',
                 'udm_clear_search_limits',
                 'udm_close_stored',
                 'udm_crc32',
                 'udm_errno',
                 'udm_error',
                 'udm_find',
                 'udm_free_agent',
                 'udm_free_ispell_data',
                 'udm_free_res',
                 'udm_get_doc_count',
                 'udm_get_res_field',
                 'udm_get_res_param',
                 'udm_hash32',
                 'udm_load_ispell_data',
                 'udm_open_stored',
                 'udm_set_agent_param'),
 'mqseries': ('mqseries_back',
              'mqseries_begin',
              'mqseries_close',
              'mqseries_cmit',
              'mqseries_conn',
              'mqseries_connx',
              'mqseries_disc',
              'mqseries_get',
              'mqseries_inq',
              'mqseries_open',
              'mqseries_put1',
              'mqseries_put',
              'mqseries_set',
              'mqseries_strerror'),
 'mysqlnd_qc': ('mysqlnd_qc_clear_cache',
                'mysqlnd_qc_get_available_handlers',
                'mysqlnd_qc_get_cache_info',
                'mysqlnd_qc_get_core_stats',
                'mysqlnd_qc_get_normalized_query_trace_log',
                'mysqlnd_qc_get_query_trace_log',
                'mysqlnd_qc_set_cache_condition',
                'mysqlnd_qc_set_is_select',
                'mysqlnd_qc_set_storage_handler',
                'mysqlnd_qc_set_user_handlers'),
 'qtdom': ('qdom_error', 'qdom_tree'),
 'runkit': ('runkit_class_adopt',
            'runkit_class_emancipate',
            'runkit_constant_add',
            'runkit_constant_redefine',
            'runkit_constant_remove',
            'runkit_function_add',
            'runkit_function_copy',
            'runkit_function_redefine',
            'runkit_function_remove',
            'runkit_function_rename',
            'runkit_import',
            'runkit_lint_file',
            'runkit_lint',
            'runkit_method_add',
            'runkit_method_copy',
            'runkit_method_redefine',
            'runkit_method_remove',
            'runkit_method_rename',
            'runkit_return_value_used',
            'runkit_sandbox_output_handler',
            'runkit_superglobals'),
 'ssdeep': ('ssdeep_fuzzy_compare',
            'ssdeep_fuzzy_hash_filename',
            'ssdeep_fuzzy_hash'),
 'vpopmail': ('vpopmail_add_alias_domain_ex',
              'vpopmail_add_alias_domain',
              'vpopmail_add_domain_ex',
              'vpopmail_add_domain',
              'vpopmail_add_user',
              'vpopmail_alias_add',
              'vpopmail_alias_del_domain',
              'vpopmail_alias_del',
              'vpopmail_alias_get_all',
              'vpopmail_alias_get',
              'vpopmail_auth_user',
              'vpopmail_del_domain_ex',
              'vpopmail_del_domain',
              'vpopmail_del_user',
              'vpopmail_error',
              'vpopmail_passwd',
              'vpopmail_set_user_quota'),
 'win32ps': ('win32_ps_list_procs', 'win32_ps_stat_mem', 'win32_ps_stat_proc'),
 'win32service': ('win32_continue_service',
                  'win32_create_service',
                  'win32_delete_service',
                  'win32_get_last_control_message',
                  'win32_pause_service',
                  'win32_query_service_status',
                  'win32_set_service_status',
                  'win32_start_service_ctrl_dispatcher',
                  'win32_start_service',
                  'win32_stop_service'),
 'xattr': ('xattr_get',
           'xattr_list',
           'xattr_remove',
           'xattr_set',
           'xattr_supported'),
 'xdiff': ('xdiff_file_bdiff_size',
           'xdiff_file_bdiff',
           'xdiff_file_bpatch',
           'xdiff_file_diff_binary',
           'xdiff_file_diff',
           'xdiff_file_merge3',
           'xdiff_file_patch_binary',
           'xdiff_file_patch',
           'xdiff_file_rabdiff',
           'xdiff_string_bdiff_size',
           'xdiff_string_bdiff',
           'xdiff_string_bpatch',
           'xdiff_string_diff_binary',
           'xdiff_string_diff',
           'xdiff_string_merge3',
           'xdiff_string_patch_binary',
           'xdiff_string_patch',
           'xdiff_string_rabdiff')}


if __name__ == '__main__':  # pragma: no cover
    import glob
    import os
    import pprint
    import re
    import shutil
    import tarfile
    try:
        from urllib import urlretrieve
    except ImportError:
        from urllib.request import urlretrieve

    PHP_MANUAL_URL     = 'http://us3.php.net/distributions/manual/php_manual_en.tar.gz'
    PHP_MANUAL_DIR     = './php-chunked-xhtml/'
    PHP_REFERENCE_GLOB = 'ref.*'
    PHP_FUNCTION_RE    = '<a href="function\..*?\.html">(.*?)</a>'
    PHP_MODULE_RE      = '<title>(.*?) Functions</title>'

    def get_php_functions():
        function_re = re.compile(PHP_FUNCTION_RE)
        module_re   = re.compile(PHP_MODULE_RE)
        modules     = {}

        for file in get_php_references():
            module = ''
            for line in open(file):
                if not module:
                    search = module_re.search(line)
                    if search:
                        module = search.group(1)
                        modules[module] = []

                elif 'href="function.' in line:
                    for match in function_re.finditer(line):
                        fn = match.group(1)
                        if '-&gt;' not in fn and '::' not in fn and fn not in modules[module]:
                            modules[module].append(fn)

            if module:
                # These are dummy manual pages, not actual functions
                if module == 'PHP Options/Info':
                    modules[module].remove('main')

                if module == 'Filesystem':
                    modules[module].remove('delete')

                if not modules[module]:
                    del modules[module]

        return modules

    def get_php_references():
        download = urlretrieve(PHP_MANUAL_URL)
        tar = tarfile.open(download[0])
        tar.extractall()
        tar.close()
        for file in glob.glob("%s%s" % (PHP_MANUAL_DIR, PHP_REFERENCE_GLOB)):
            yield file
        os.remove(download[0])

    def regenerate(filename, modules):
        with open(filename) as fp:
            content = fp.read()

        header = content[:content.find('MODULES = {')]
        footer = content[content.find("if __name__ == '__main__':"):]

        with open(filename, 'w') as fp:
            fp.write(header)
            fp.write('MODULES = %s\n\n' % pprint.pformat(modules))
            fp.write(footer)

    def run():
        print('>> Downloading Function Index')
        modules = get_php_functions()
        total = sum(len(v) for v in modules.values())
        print('%d functions found' % total)
        regenerate(__file__, modules)
        shutil.rmtree(PHP_MANUAL_DIR)

    run()
