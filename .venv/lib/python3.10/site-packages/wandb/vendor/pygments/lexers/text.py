# -*- coding: utf-8 -*-
"""
    pygments.lexers.text
    ~~~~~~~~~~~~~~~~~~~~

    Lexers for non-source code file types.

    :copyright: Copyright 2006-2017 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

from pygments.lexers.configs import ApacheConfLexer, NginxConfLexer, \
    SquidConfLexer, LighttpdConfLexer, IniLexer, RegeditLexer, PropertiesLexer
from pygments.lexers.console import PyPyLogLexer
from pygments.lexers.textedit import VimLexer
from pygments.lexers.markup import BBCodeLexer, MoinWikiLexer, Rst<PERSON><PERSON><PERSON>, \
    TexLexer, GroffLexer
from pygments.lexers.installers import DebianControlLexer, SourcesListLexer
from pygments.lexers.make import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseMakefileLexer, CMakeLexer
from pygments.lexers.haxe import Hxml<PERSON>exer
from pygments.lexers.diff import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>exer
from pygments.lexers.data import <PERSON><PERSON><PERSON><PERSON>ex<PERSON>
from pygments.lexers.textfmts import IrcLogsLexer, GettextLexer, HttpLexer

__all__ = []
