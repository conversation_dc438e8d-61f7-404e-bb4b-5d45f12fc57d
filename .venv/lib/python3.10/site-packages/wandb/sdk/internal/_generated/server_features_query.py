# Generated by ariadne-codegen
# Source: tools/graphql_codegen/utils/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult


class ServerFeaturesQuery(GQLResult):
    server_info: Optional[ServerFeaturesQueryServerInfo] = Field(alias="serverInfo")


class ServerFeaturesQueryServerInfo(GQLResult):
    features: List[Optional[ServerFeaturesQueryServerInfoFeatures]]


class ServerFeaturesQueryServerInfoFeatures(GQLResult):
    name: str
    is_enabled: bool = Field(alias="isEnabled")


ServerFeaturesQuery.model_rebuild()
ServerFeaturesQueryServerInfo.model_rebuild()
