# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import RegistryFragment


class UpsertRegistry(GQLResult):
    upsert_model: Optional[UpsertRegistryUpsertModel] = Field(alias="upsertModel")


class UpsertRegistryUpsertModel(GQLResult):
    inserted: Optional[bool]
    project: Optional[RegistryFragment]


UpsertRegistry.model_rebuild()
UpsertRegistryUpsertModel.model_rebuild()
