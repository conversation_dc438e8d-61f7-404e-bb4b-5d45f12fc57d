# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLResult

from .fragments import ArtifactFragment


class ArtifactByName(GQLResult):
    project: Optional[ArtifactByNameProject]


class ArtifactByNameProject(GQLResult):
    artifact: Optional[ArtifactFragment]


ArtifactByName.model_rebuild()
ArtifactByNameProject.model_rebuild()
