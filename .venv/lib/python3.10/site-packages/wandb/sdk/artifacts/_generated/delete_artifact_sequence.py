# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field
from typing_extensions import Literal

from wandb._pydantic import GQLResult, Typename

from .enums import ArtifactCollectionState


class DeleteArtifactSequence(GQLResult):
    result: Optional[DeleteArtifactSequenceResult]


class DeleteArtifactSequenceResult(GQLResult):
    artifact_collection: DeleteArtifactSequenceResultArtifactCollection = Field(
        alias="artifactCollection"
    )


class DeleteArtifactSequenceResultArtifactCollection(GQLResult):
    typename__: Typename[
        Literal["ArtifactCollection", "ArtifactPortfolio", "ArtifactSequence"]
    ]
    state: ArtifactCollectionState


DeleteArtifactSequence.model_rebuild()
DeleteArtifactSequenceResult.model_rebuild()
