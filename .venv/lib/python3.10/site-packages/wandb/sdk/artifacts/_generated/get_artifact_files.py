# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import FileFragment, PageInfoFragment


class GetArtifactFiles(GQLResult):
    project: Optional[GetArtifactFilesProject]


class GetArtifactFilesProject(GQLResult):
    artifact_type: Optional[GetArtifactFilesProjectArtifactType] = Field(
        alias="artifactType"
    )


class GetArtifactFilesProjectArtifactType(GQLResult):
    artifact: Optional[GetArtifactFilesProjectArtifactTypeArtifact]


class GetArtifactFilesProjectArtifactTypeArtifact(GQLResult):
    files: Optional[GetArtifactFilesProjectArtifactTypeArtifactFiles]


class GetArtifactFilesProjectArtifactTypeArtifactFiles(GQLResult):
    total_count: Optional[int] = Field(alias="totalCount", default=None)
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[GetArtifactFilesProjectArtifactTypeArtifactFilesEdges]


class GetArtifactFilesProjectArtifactTypeArtifactFilesEdges(GQLResult):
    node: Optional[FileFragment]


GetArtifactFiles.model_rebuild()
GetArtifactFilesProject.model_rebuild()
GetArtifactFilesProjectArtifactType.model_rebuild()
GetArtifactFilesProjectArtifactTypeArtifact.model_rebuild()
GetArtifactFilesProjectArtifactTypeArtifactFiles.model_rebuild()
GetArtifactFilesProjectArtifactTypeArtifactFilesEdges.model_rebuild()
