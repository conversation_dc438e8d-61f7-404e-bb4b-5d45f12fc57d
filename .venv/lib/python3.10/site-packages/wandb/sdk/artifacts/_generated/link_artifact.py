# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactMembershipFragment


class LinkArtifact(GQLResult):
    result: Optional[LinkArtifactResult]


class LinkArtifactResult(GQLResult):
    version_index: Optional[int] = Field(alias="versionIndex")
    artifact_membership: Optional[ArtifactMembershipFragment] = Field(
        alias="artifactMembership", default=None
    )


LinkArtifact.model_rebuild()
LinkArtifactResult.model_rebuild()
