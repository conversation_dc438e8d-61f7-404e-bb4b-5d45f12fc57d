# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from wandb._pydantic import GQLId, GQLResult


class DeleteArtifact(GQLResult):
    result: Optional[DeleteArtifactResult]


class DeleteArtifactResult(GQLResult):
    artifact: DeleteArtifactResultArtifact


class DeleteArtifactResultArtifact(GQLResult):
    id: GQLId


DeleteArtifact.model_rebuild()
DeleteArtifactResult.model_rebuild()
