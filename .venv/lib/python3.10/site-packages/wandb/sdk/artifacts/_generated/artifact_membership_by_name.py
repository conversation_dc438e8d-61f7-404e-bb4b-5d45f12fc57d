# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactMembershipFragment


class ArtifactMembershipByName(GQLResult):
    project: Optional[ArtifactMembershipByNameProject]


class ArtifactMembershipByNameProject(GQLResult):
    artifact_collection_membership: Optional[ArtifactMembershipFragment] = Field(
        alias="artifactCollectionMembership"
    )


ArtifactMembershipByName.model_rebuild()
ArtifactMembershipByNameProject.model_rebuild()
