# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactTypeFragment, PageInfoFragment


class ProjectArtifactTypes(GQLResult):
    project: Optional[ProjectArtifactTypesProject]


class ProjectArtifactTypesProject(GQLResult):
    artifact_types: ProjectArtifactTypesProjectArtifactTypes = Field(
        alias="artifactTypes"
    )


class ProjectArtifactTypesProjectArtifactTypes(GQLResult):
    edges: List[ProjectArtifactTypesProjectArtifactTypesEdges]
    page_info: PageInfoFragment = Field(alias="pageInfo")


class ProjectArtifactTypesProjectArtifactTypesEdges(GQLResult):
    node: Optional[ArtifactTypeFragment]


ProjectArtifactTypes.model_rebuild()
ProjectArtifactTypesProject.model_rebuild()
ProjectArtifactTypesProjectArtifactTypes.model_rebuild()
ProjectArtifactTypesProjectArtifactTypesEdges.model_rebuild()
