# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactFragment, PageInfoFragment


class RunOutputArtifacts(GQLResult):
    project: Optional[RunOutputArtifactsProject]


class RunOutputArtifactsProject(GQLResult):
    run: Optional[RunOutputArtifactsProjectRun]


class RunOutputArtifactsProjectRun(GQLResult):
    artifacts: Optional[RunOutputArtifactsProjectRunArtifacts]


class RunOutputArtifactsProjectRunArtifacts(GQLResult):
    total_count: int = Field(alias="totalCount")
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[RunOutputArtifactsProjectRunArtifactsEdges]


class RunOutputArtifactsProjectRunArtifactsEdges(GQLResult):
    node: Optional[ArtifactFragment]


RunOutputArtifacts.model_rebuild()
RunOutputArtifactsProject.model_rebuild()
RunOutputArtifactsProjectRun.model_rebuild()
RunOutputArtifactsProjectRunArtifacts.model_rebuild()
RunOutputArtifactsProjectRunArtifactsEdges.model_rebuild()
