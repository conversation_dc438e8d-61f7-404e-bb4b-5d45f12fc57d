# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactCollectionFragment


class UpdateArtifactSequenceType(GQLResult):
    result: Optional[UpdateArtifactSequenceTypeResult]


class UpdateArtifactSequenceTypeResult(GQLResult):
    artifact_collection: Optional[ArtifactCollectionFragment] = Field(
        alias="artifactCollection"
    )


UpdateArtifactSequenceType.model_rebuild()
UpdateArtifactSequenceTypeResult.model_rebuild()
