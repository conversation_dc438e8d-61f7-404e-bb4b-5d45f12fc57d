# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactAliasFragment, CollectionInfoFragment


class FetchLinkedArtifacts(GQLResult):
    artifact: Optional[FetchLinkedArtifactsArtifact]


class FetchLinkedArtifactsArtifact(GQLResult):
    artifact_memberships: FetchLinkedArtifactsArtifactArtifactMemberships = Field(
        alias="artifactMemberships"
    )


class FetchLinkedArtifactsArtifactArtifactMemberships(GQLResult):
    edges: List[FetchLinkedArtifactsArtifactArtifactMembershipsEdges]


class FetchLinkedArtifactsArtifactArtifactMembershipsEdges(GQLResult):
    node: Optional[FetchLinkedArtifactsArtifactArtifactMembershipsEdgesNode]


class FetchLinkedArtifactsArtifactArtifactMembershipsEdgesNode(GQLResult):
    version_index: Optional[int] = Field(alias="versionIndex")
    aliases: List[ArtifactAliasFragment]
    artifact_collection: Optional[CollectionInfoFragment] = Field(
        alias="artifactCollection"
    )


FetchLinkedArtifacts.model_rebuild()
FetchLinkedArtifactsArtifact.model_rebuild()
FetchLinkedArtifactsArtifactArtifactMemberships.model_rebuild()
FetchLinkedArtifactsArtifactArtifactMembershipsEdges.model_rebuild()
FetchLinkedArtifactsArtifactArtifactMembershipsEdgesNode.model_rebuild()
