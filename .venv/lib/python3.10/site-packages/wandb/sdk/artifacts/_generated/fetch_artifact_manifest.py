# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import DeferredManifestFragment


class FetchArtifactManifest(GQLResult):
    artifact: Optional[FetchArtifactManifestArtifact]


class FetchArtifactManifestArtifact(GQLResult):
    current_manifest: Optional[DeferredManifestFragment] = Field(
        alias="currentManifest"
    )


FetchArtifactManifest.model_rebuild()
FetchArtifactManifestArtifact.model_rebuild()
