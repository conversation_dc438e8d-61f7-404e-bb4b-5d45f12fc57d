# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field
from typing_extensions import Literal

from wandb._pydantic import GQLResult, Typename

from .enums import ArtifactCollectionState


class DeleteArtifactPortfolio(GQLResult):
    result: Optional[DeleteArtifactPortfolioResult]


class DeleteArtifactPortfolioResult(GQLResult):
    artifact_collection: DeleteArtifactPortfolioResultArtifactCollection = Field(
        alias="artifactCollection"
    )


class DeleteArtifactPortfolioResultArtifactCollection(GQLResult):
    typename__: Typename[
        Literal["ArtifactCollection", "ArtifactPortfolio", "ArtifactSequence"]
    ]
    state: ArtifactCollectionState


DeleteArtifactPortfolio.model_rebuild()
DeleteArtifactPortfolioResult.model_rebuild()
