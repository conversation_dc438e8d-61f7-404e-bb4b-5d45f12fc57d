# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

__all__ = [
    "ADD_ALIASES_GQL",
    "ADD_ARTIFACT_COLLECTION_TAGS_GQL",
    "ARTIFACT_BY_ID_GQL",
    "ARTIFACT_BY_NAME_GQL",
    "ARTIFACT_COLLECTION_ALIASES_GQL",
    "ARTIFACT_CREATED_BY_GQL",
    "ARTIFACT_MEMBERSHIP_BY_NAME_GQL",
    "ARTIFACT_TYPE_GQL",
    "ARTIFACT_USED_BY_GQL",
    "CREATE_REGISTRY_MEMBERS_GQL",
    "DELETE_ALIASES_GQL",
    "DELETE_ARTIFACT_COLLECTION_TAGS_GQL",
    "DELETE_ARTIFACT_GQL",
    "DELETE_ARTIFACT_PORTFOLIO_GQL",
    "DELETE_ARTIFACT_SEQUENCE_GQL",
    "DELETE_REGISTRY_GQL",
    "DELETE_REGISTRY_MEMBERS_GQL",
    "FETCH_ARTIFACT_MANIFEST_GQL",
    "FETCH_LINKED_ARTIFACTS_GQL",
    "FETCH_ORG_ENTITY_FROM_ORGANIZATION_GQL",
    "FETCH_ORG_INFO_FROM_ENTITY_GQL",
    "FETCH_REGISTRIES_GQL",
    "FETCH_REGISTRY_GQL",
    "GET_ARTIFACT_FILES_GQL",
    "GET_ARTIFACT_FILE_URLS_GQL",
    "GET_ARTIFACT_MEMBERSHIP_FILES_GQL",
    "GET_ARTIFACT_MEMBERSHIP_FILE_URLS_GQL",
    "LINK_ARTIFACT_GQL",
    "PROJECT_ARTIFACTS_GQL",
    "PROJECT_ARTIFACT_COLLECTIONS_GQL",
    "PROJECT_ARTIFACT_COLLECTION_GQL",
    "PROJECT_ARTIFACT_TYPES_GQL",
    "PROJECT_ARTIFACT_TYPE_GQL",
    "REGISTRY_COLLECTIONS_GQL",
    "REGISTRY_TEAM_MEMBERS_GQL",
    "REGISTRY_USER_MEMBERS_GQL",
    "REGISTRY_VERSIONS_GQL",
    "RENAME_REGISTRY_GQL",
    "RUN_INPUT_ARTIFACTS_GQL",
    "RUN_OUTPUT_ARTIFACTS_GQL",
    "TYPE_INFO_GQL",
    "UNLINK_ARTIFACT_GQL",
    "UPDATE_ARTIFACT_GQL",
    "UPDATE_ARTIFACT_PORTFOLIO_GQL",
    "UPDATE_ARTIFACT_SEQUENCE_GQL",
    "UPDATE_ARTIFACT_SEQUENCE_TYPE_GQL",
    "UPDATE_TEAM_REGISTRY_ROLE_GQL",
    "UPDATE_USER_REGISTRY_ROLE_GQL",
    "UPSERT_REGISTRY_GQL",
]

DELETE_ARTIFACT_SEQUENCE_GQL = """
mutation DeleteArtifactSequence($id: ID!) {
  result: deleteArtifactSequence(input: {artifactSequenceID: $id}) {
    artifactCollection {
      __typename
      state
    }
  }
}
"""

DELETE_ARTIFACT_PORTFOLIO_GQL = """
mutation DeleteArtifactPortfolio($id: ID!) {
  result: deleteArtifactPortfolio(input: {artifactPortfolioID: $id}) {
    artifactCollection {
      __typename
      state
    }
  }
}
"""

UPDATE_ARTIFACT_SEQUENCE_GQL = """
mutation UpdateArtifactSequence($input: UpdateArtifactSequenceInput!) {
  result: updateArtifactSequence(input: $input) {
    artifactCollection {
      __typename
      ...ArtifactCollectionFragment
    }
  }
}

fragment ArtifactCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

UPDATE_ARTIFACT_PORTFOLIO_GQL = """
mutation UpdateArtifactPortfolio($input: UpdateArtifactPortfolioInput!) {
  result: updateArtifactPortfolio(input: $input) {
    artifactCollection {
      __typename
      ...ArtifactCollectionFragment
    }
  }
}

fragment ArtifactCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

UPDATE_ARTIFACT_SEQUENCE_TYPE_GQL = """
mutation UpdateArtifactSequenceType($input: MoveArtifactSequenceInput!) {
  result: moveArtifactSequence(input: $input) {
    artifactCollection {
      __typename
      ...ArtifactCollectionFragment
    }
  }
}

fragment ArtifactCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

ADD_ARTIFACT_COLLECTION_TAGS_GQL = """
mutation AddArtifactCollectionTags($input: CreateArtifactCollectionTagAssignmentsInput!) {
  result: createArtifactCollectionTagAssignments(input: $input) {
    tags {
      ...TagFragment
    }
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

DELETE_ARTIFACT_COLLECTION_TAGS_GQL = """
mutation DeleteArtifactCollectionTags($input: DeleteArtifactCollectionTagAssignmentsInput!) {
  result: deleteArtifactCollectionTagAssignments(input: $input) {
    success
  }
}
"""

PROJECT_ARTIFACT_COLLECTIONS_GQL = """
query ProjectArtifactCollections($entity: String!, $project: String!, $type: String!, $cursor: String, $perPage: Int) {
  project(entityName: $entity, name: $project) {
    artifactType(name: $type) {
      artifactCollections(after: $cursor, first: $perPage) {
        totalCount
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            __typename
            ...ArtifactCollectionFragment
          }
        }
      }
    }
  }
}

fragment ArtifactCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

PROJECT_ARTIFACT_COLLECTION_GQL = """
query ProjectArtifactCollection($entity: String!, $project: String!, $type: String!, $name: String!) {
  project(entityName: $entity, name: $project) {
    artifactType(name: $type) {
      artifactCollection(name: $name) {
        __typename
        ...ArtifactCollectionFragment
      }
    }
  }
}

fragment ArtifactCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

ARTIFACT_COLLECTION_ALIASES_GQL = """
query ArtifactCollectionAliases($id: ID!, $cursor: String, $perPage: Int = 1000) {
  artifactCollection(id: $id) {
    __typename
    aliases(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFragment
      }
      edges {
        node {
          ...ArtifactAliasFragment
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

GET_ARTIFACT_FILES_GQL = """
query GetArtifactFiles($entity: String!, $project: String!, $type: String!, $name: String!, $fileNames: [String!], $cursor: String, $perPage: Int = 50) {
  project(name: $project, entityName: $entity) {
    artifactType(name: $type) {
      artifact(name: $name) {
        files(names: $fileNames, after: $cursor, first: $perPage) {
          totalCount @include(if: true)
          pageInfo {
            ...PageInfoFragment
          }
          edges {
            node {
              ...FileFragment
            }
          }
        }
      }
    }
  }
}

fragment FileFragment on File {
  __typename
  id
  name: displayName
  url
  sizeBytes
  storagePath
  mimetype
  updatedAt
  digest
  md5
  directUrl
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

GET_ARTIFACT_MEMBERSHIP_FILES_GQL = """
query GetArtifactMembershipFiles($entity: String!, $project: String!, $collection: String!, $alias: String!, $fileNames: [String!], $cursor: String, $perPage: Int = 50) {
  project(name: $project, entityName: $entity) {
    artifactCollection(name: $collection) {
      __typename
      artifactMembership(aliasName: $alias) {
        files(names: $fileNames, after: $cursor, first: $perPage) {
          totalCount @include(if: true)
          pageInfo {
            ...PageInfoFragment
          }
          edges {
            node {
              ...FileFragment
            }
          }
        }
      }
    }
  }
}

fragment FileFragment on File {
  __typename
  id
  name: displayName
  url
  sizeBytes
  storagePath
  mimetype
  updatedAt
  digest
  md5
  directUrl
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

GET_ARTIFACT_FILE_URLS_GQL = """
query GetArtifactFileUrls($id: ID!, $cursor: String, $perPage: Int) {
  artifact(id: $id) {
    files(after: $cursor, first: $perPage) {
      pageInfo {
        ...PageInfoFragment
      }
      edges {
        node {
          ...FileWithUrlFragment
        }
      }
    }
  }
}

fragment FileWithUrlFragment on File {
  __typename
  name
  directUrl
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

GET_ARTIFACT_MEMBERSHIP_FILE_URLS_GQL = """
query GetArtifactMembershipFileUrls($entity: String!, $project: String!, $collection: String!, $alias: String!, $cursor: String, $perPage: Int) {
  project(name: $project, entityName: $entity) {
    artifactCollection(name: $collection) {
      __typename
      artifactMembership(aliasName: $alias) {
        files(after: $cursor, first: $perPage) {
          pageInfo {
            ...PageInfoFragment
          }
          edges {
            node {
              ...FileWithUrlFragment
            }
          }
        }
      }
    }
  }
}

fragment FileWithUrlFragment on File {
  __typename
  name
  directUrl
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

PROJECT_ARTIFACT_TYPES_GQL = """
query ProjectArtifactTypes($entity: String!, $project: String!, $cursor: String, $perPage: Int) {
  project(name: $project, entityName: $entity) {
    artifactTypes(after: $cursor, first: $perPage) {
      edges {
        node {
          ...ArtifactTypeFragment
        }
      }
      pageInfo {
        ...PageInfoFragment
      }
    }
  }
}

fragment ArtifactTypeFragment on ArtifactType {
  __typename
  id
  name
  description
  createdAt
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}
"""

PROJECT_ARTIFACT_TYPE_GQL = """
query ProjectArtifactType($entity: String!, $project: String!, $type: String!) {
  project(entityName: $entity, name: $project) {
    artifactType(name: $type) {
      ...ArtifactTypeFragment
    }
  }
}

fragment ArtifactTypeFragment on ArtifactType {
  __typename
  id
  name
  description
  createdAt
}
"""

PROJECT_ARTIFACTS_GQL = """
query ProjectArtifacts($entity: String!, $project: String!, $type: String!, $collection: String!, $cursor: String, $perPage: Int = 50, $order: String, $filters: JSONString, $includeAliases: Boolean = true) {
  project(entityName: $entity, name: $project) {
    artifactType(name: $type) {
      artifactCollection(name: $collection) {
        __typename
        artifacts(after: $cursor, first: $perPage, order: $order, filters: $filters) {
          totalCount
          pageInfo {
            ...PageInfoFragment
          }
          edges {
            version
            node {
              ...ArtifactFragment
            }
          }
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

RUN_OUTPUT_ARTIFACTS_GQL = """
query RunOutputArtifacts($entity: String!, $project: String!, $run: String!, $cursor: String, $perPage: Int, $includeAliases: Boolean = true) {
  project(entityName: $entity, name: $project) {
    run(name: $run) {
      artifacts: outputArtifacts(after: $cursor, first: $perPage) {
        totalCount
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            ...ArtifactFragment
          }
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

RUN_INPUT_ARTIFACTS_GQL = """
query RunInputArtifacts($entity: String!, $project: String!, $run: String!, $cursor: String, $perPage: Int, $includeAliases: Boolean = true) {
  project(entityName: $entity, name: $project) {
    run(name: $run) {
      artifacts: inputArtifacts(after: $cursor, first: $perPage) {
        totalCount
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            ...ArtifactFragment
          }
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

FETCH_LINKED_ARTIFACTS_GQL = """
query FetchLinkedArtifacts($artifactID: ID!) {
  artifact(id: $artifactID) {
    artifactMemberships {
      edges {
        node {
          versionIndex
          aliases {
            ...ArtifactAliasFragment
          }
          artifactCollection {
            __typename
            ...CollectionInfoFragment
          }
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}
"""

FETCH_ARTIFACT_MANIFEST_GQL = """
query FetchArtifactManifest($id: ID!) {
  artifact(id: $id) {
    currentManifest {
      ...DeferredManifestFragment
    }
  }
}

fragment DeferredManifestFragment on ArtifactManifest {
  file {
    directUrl
  }
}
"""

ARTIFACT_BY_ID_GQL = """
query ArtifactByID($id: ID!, $includeAliases: Boolean = true) {
  artifact(id: $id) {
    ...ArtifactFragment
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

ARTIFACT_BY_NAME_GQL = """
query ArtifactByName($entity: String!, $project: String!, $name: String!, $enableTracking: Boolean, $includeAliases: Boolean = true) {
  project(name: $project, entityName: $entity) {
    artifact(name: $name, enableTracking: $enableTracking) {
      ...ArtifactFragment
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

ARTIFACT_MEMBERSHIP_BY_NAME_GQL = """
query ArtifactMembershipByName($entity: String!, $project: String!, $name: String!, $includeAliases: Boolean = false) {
  project(name: $project, entityName: $entity) {
    artifactCollectionMembership(name: $name) {
      ...ArtifactMembershipFragment
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment ArtifactMembershipFragment on ArtifactCollectionMembership {
  __typename
  id
  versionIndex
  aliases {
    ...ArtifactAliasFragment
  }
  artifactCollection {
    ...CollectionInfoFragment
  }
  artifact {
    ...ArtifactFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

ARTIFACT_USED_BY_GQL = """
query ArtifactUsedBy($id: ID!) {
  artifact(id: $id) {
    usedBy {
      edges {
        node {
          ...RunInfoFragment
        }
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment RunInfoFragment on Run {
  __typename
  id
  name
  project {
    ...ProjectInfoFragment
  }
}
"""

ARTIFACT_CREATED_BY_GQL = """
query ArtifactCreatedBy($id: ID!) {
  artifact(id: $id) {
    createdBy {
      __typename
      ... on Run {
        ...RunInfoFragment
      }
    }
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment RunInfoFragment on Run {
  __typename
  id
  name
  project {
    ...ProjectInfoFragment
  }
}
"""

ARTIFACT_TYPE_GQL = """
query ArtifactType($entity: String, $project: String, $name: String!) {
  project(name: $project, entityName: $entity) {
    artifact(name: $name) {
      artifactType {
        name
      }
    }
  }
}
"""

ADD_ALIASES_GQL = """
mutation AddAliases($input: AddAliasesInput!) {
  result: addAliases(input: $input) {
    success
  }
}
"""

DELETE_ALIASES_GQL = """
mutation DeleteAliases($input: DeleteAliasesInput!) {
  result: deleteAliases(input: $input) {
    success
  }
}
"""

UPDATE_ARTIFACT_GQL = """
mutation UpdateArtifact($input: UpdateArtifactInput!, $includeAliases: Boolean = true) {
  result: updateArtifact(input: $input) {
    artifact {
      ...ArtifactFragment
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

DELETE_ARTIFACT_GQL = """
mutation DeleteArtifact($input: DeleteArtifactInput!) {
  result: deleteArtifact(input: $input) {
    artifact {
      id
    }
  }
}
"""

LINK_ARTIFACT_GQL = """
mutation LinkArtifact($input: LinkArtifactInput!, $includeAliases: Boolean = true) {
  result: linkArtifact(input: $input) {
    versionIndex
    artifactMembership @include(if: true) {
      ...ArtifactMembershipFragment
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment ArtifactMembershipFragment on ArtifactCollectionMembership {
  __typename
  id
  versionIndex
  aliases {
    ...ArtifactAliasFragment
  }
  artifactCollection {
    ...CollectionInfoFragment
  }
  artifact {
    ...ArtifactFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

UNLINK_ARTIFACT_GQL = """
mutation UnlinkArtifact($input: UnlinkArtifactInput!) {
  result: unlinkArtifact(input: $input) {
    success
  }
}
"""

TYPE_INFO_GQL = """
query TypeInfo($name: String!) {
  __type(name: $name) {
    ...TypeInfoFragment
  }
}

fragment TypeInfoFragment on __Type {
  name
  fields {
    name
    args {
      name
    }
  }
  inputFields {
    name
  }
}
"""

FETCH_ORG_INFO_FROM_ENTITY_GQL = """
query FetchOrgInfoFromEntity($entity: String!) {
  entity(name: $entity) {
    organization {
      ...OrgInfoFragment
    }
    user {
      organizations {
        ...OrgInfoFragment
      }
    }
  }
}

fragment OrgInfoFragment on Organization {
  name
  orgEntity {
    name
  }
}
"""

FETCH_ORG_ENTITY_FROM_ORGANIZATION_GQL = """
query FetchOrgEntityFromOrganization($organization: String!) {
  organization(name: $organization) {
    orgEntity {
      name
    }
  }
}
"""

REGISTRY_VERSIONS_GQL = """
query RegistryVersions($organization: String!, $registryFilter: JSONString, $collectionFilter: JSONString, $artifactFilter: JSONString, $cursor: String, $perPage: Int, $includeAliases: Boolean = false) {
  organization(name: $organization) {
    orgEntity {
      name
      artifactMemberships(
        projectFilters: $registryFilter
        collectionFilters: $collectionFilter
        filters: $artifactFilter
        after: $cursor
        first: $perPage
      ) {
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            ...ArtifactMembershipFragment
          }
        }
      }
    }
  }
}

fragment ArtifactAliasFragment on ArtifactAlias {
  __typename
  id
  alias
}

fragment ArtifactFragment on Artifact {
  __typename
  id
  artifactSequence {
    ...SourceCollectionInfoFragment
  }
  versionIndex
  artifactType {
    name
  }
  description
  metadata
  ttlDurationSeconds
  ttlIsInherited
  tags {
    ...TagFragment
  }
  historyStep
  state
  size
  digest
  commitHash
  fileCount
  createdAt
  updatedAt
  aliases @include(if: $includeAliases) {
    artifactCollection {
      ...CollectionInfoFragment
    }
    ...ArtifactAliasFragment
  }
}

fragment ArtifactMembershipFragment on ArtifactCollectionMembership {
  __typename
  id
  versionIndex
  aliases {
    ...ArtifactAliasFragment
  }
  artifactCollection {
    ...CollectionInfoFragment
  }
  artifact {
    ...ArtifactFragment
  }
}

fragment CollectionInfoFragment on ArtifactCollection {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment SourceCollectionInfoFragment on ArtifactSequence {
  __typename
  name
  project {
    ...ProjectInfoFragment
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

REGISTRY_COLLECTIONS_GQL = """
query RegistryCollections($organization: String!, $registryFilter: JSONString, $collectionFilter: JSONString, $collectionTypes: [ArtifactCollectionType!] = [PORTFOLIO], $cursor: String, $perPage: Int) {
  organization(name: $organization) {
    orgEntity {
      name
      artifactCollections(
        projectFilters: $registryFilter
        filters: $collectionFilter
        collectionTypes: $collectionTypes
        after: $cursor
        first: $perPage
      ) {
        totalCount
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            __typename
            ...RegistryCollectionFragment
          }
        }
      }
    }
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment ProjectInfoFragment on Project {
  name
  entity {
    name
  }
}

fragment RegistryCollectionFragment on ArtifactCollection {
  __typename
  id
  name
  description
  createdAt
  project {
    ...ProjectInfoFragment
  }
  type: defaultArtifactType {
    name
  }
  tags {
    edges {
      node {
        ...TagFragment
      }
    }
  }
}

fragment TagFragment on Tag {
  __typename
  id
  name
}
"""

FETCH_REGISTRY_GQL = """
query FetchRegistry($name: String, $entity: String) {
  entity(name: $entity) {
    project(name: $name) {
      ...RegistryFragment
    }
  }
}

fragment RegistryFragment on Project {
  __typename
  id
  name
  entity {
    name
    organization {
      name
    }
  }
  description
  createdAt
  updatedAt
  access
  allowAllArtifactTypes: allowAllArtifactTypesInRegistry
  artifactTypes(includeAll: true) {
    edges {
      node {
        name
      }
    }
  }
}
"""

FETCH_REGISTRIES_GQL = """
query FetchRegistries($organization: String!, $filters: JSONString, $cursor: String, $perPage: Int) {
  organization(name: $organization) {
    orgEntity {
      projects(filters: $filters, after: $cursor, first: $perPage) {
        pageInfo {
          ...PageInfoFragment
        }
        edges {
          node {
            ...RegistryFragment
          }
        }
      }
    }
  }
}

fragment PageInfoFragment on PageInfo {
  __typename
  endCursor
  hasNextPage
}

fragment RegistryFragment on Project {
  __typename
  id
  name
  entity {
    name
    organization {
      name
    }
  }
  description
  createdAt
  updatedAt
  access
  allowAllArtifactTypes: allowAllArtifactTypesInRegistry
  artifactTypes(includeAll: true) {
    edges {
      node {
        name
      }
    }
  }
}
"""

RENAME_REGISTRY_GQL = """
mutation RenameRegistry($input: RenameProjectInput!) {
  renameProject(input: $input) {
    inserted
    project {
      ...RegistryFragment
    }
  }
}

fragment RegistryFragment on Project {
  __typename
  id
  name
  entity {
    name
    organization {
      name
    }
  }
  description
  createdAt
  updatedAt
  access
  allowAllArtifactTypes: allowAllArtifactTypesInRegistry
  artifactTypes(includeAll: true) {
    edges {
      node {
        name
      }
    }
  }
}
"""

UPSERT_REGISTRY_GQL = """
mutation UpsertRegistry($input: UpsertModelInput!) {
  upsertModel(input: $input) {
    inserted
    project {
      ...RegistryFragment
    }
  }
}

fragment RegistryFragment on Project {
  __typename
  id
  name
  entity {
    name
    organization {
      name
    }
  }
  description
  createdAt
  updatedAt
  access
  allowAllArtifactTypes: allowAllArtifactTypesInRegistry
  artifactTypes(includeAll: true) {
    edges {
      node {
        name
      }
    }
  }
}
"""

DELETE_REGISTRY_GQL = """
mutation DeleteRegistry($id: String!) {
  deleteModel(input: {id: $id}) {
    success
  }
}
"""

REGISTRY_USER_MEMBERS_GQL = """
query RegistryUserMembers($project: String!, $entity: String!) {
  project(name: $project, entityName: $entity) {
    members {
      ...UserRegistryMemberFragment
    }
  }
}

fragment RegistryRoleFragment on Role {
  name
}

fragment UserRegistryMemberFragment on ProjectMember {
  id
  name
  username
  email
  role {
    ...RegistryRoleFragment
  }
}
"""

REGISTRY_TEAM_MEMBERS_GQL = """
query RegistryTeamMembers($project: String!, $entity: String!) {
  project(name: $project, entityName: $entity) {
    teamMembers {
      ...TeamRegistryMemberFragment
    }
  }
}

fragment RegistryRoleFragment on Role {
  name
}

fragment TeamFragment on Entity {
  __typename
  id
  name
  available
  photoUrl
  readOnly
  readOnlyAdmin
  isTeam
  privateOnly
  storageBytes
  codeSavingEnabled
  defaultAccess
  isPaid
  members {
    ...TeamMemberFragment
  }
}

fragment TeamMemberFragment on Member {
  __typename
  id
  role
  pending
  email
  username
  name
  photoUrl
  accountType
  apiKey
}

fragment TeamRegistryMemberFragment on ProjectTeamMember {
  team {
    ...TeamFragment
  }
  role {
    ...RegistryRoleFragment
  }
}
"""

CREATE_REGISTRY_MEMBERS_GQL = """
mutation CreateRegistryMembers($input: CreateProjectMembersInput!) {
  result: createProjectMembers(input: $input) {
    success
  }
}
"""

DELETE_REGISTRY_MEMBERS_GQL = """
mutation DeleteRegistryMembers($input: DeleteProjectMembersInput!) {
  result: deleteProjectMembers(input: $input) {
    success
  }
}
"""

UPDATE_USER_REGISTRY_ROLE_GQL = """
mutation UpdateUserRegistryRole($input: UpdateProjectMemberInput!) {
  result: updateProjectMember(input: $input) {
    success
  }
}
"""

UPDATE_TEAM_REGISTRY_ROLE_GQL = """
mutation UpdateTeamRegistryRole($input: UpdateProjectTeamMemberInput!) {
  result: updateProjectTeamMember(input: $input) {
    success
  }
}
"""
