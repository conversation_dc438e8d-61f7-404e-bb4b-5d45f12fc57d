# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from wandb._pydantic import GQLResult

from .fragments import UserRegistryMemberFragment


class RegistryUserMembers(GQLResult):
    project: Optional[RegistryUserMembersProject]


class RegistryUserMembersProject(GQLResult):
    members: List[UserRegistryMemberFragment]


RegistryUserMembers.model_rebuild()
RegistryUserMembersProject.model_rebuild()
