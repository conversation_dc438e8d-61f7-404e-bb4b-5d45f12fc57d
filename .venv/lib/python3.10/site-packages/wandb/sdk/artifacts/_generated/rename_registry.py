# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import RegistryFragment


class RenameRegistry(GQLResult):
    rename_project: Optional[RenameRegistryRenameProject] = Field(alias="renameProject")


class RenameRegistryRenameProject(GQLResult):
    inserted: Optional[bool]
    project: Optional[RegistryFragment]


RenameRegistry.model_rebuild()
RenameRegistryRenameProject.model_rebuild()
