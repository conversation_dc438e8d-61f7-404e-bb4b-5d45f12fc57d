# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional, Union

from pydantic import Field
from typing_extensions import Annotated, Literal

from wandb._pydantic import GQLResult, Typename

from .fragments import RunInfoFragment


class ArtifactCreatedBy(GQLResult):
    artifact: Optional[ArtifactCreatedByArtifact]


class ArtifactCreatedByArtifact(GQLResult):
    created_by: Optional[
        Annotated[
            Union[RunInfoFragment, ArtifactCreatedByArtifactCreatedByUser],
            Field(discriminator="typename__"),
        ]
    ] = Field(alias="createdBy")


class ArtifactCreatedByArtifactCreatedByUser(GQLResult):
    typename__: Typename[Literal["User"]]


ArtifactCreatedBy.model_rebuild()
ArtifactCreatedByArtifact.model_rebuild()
