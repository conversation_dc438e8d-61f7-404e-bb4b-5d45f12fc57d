# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field
from typing_extensions import Literal

from wandb._pydantic import GQLResult, Typename

from .fragments import ArtifactAliasFragment, PageInfoFragment


class ArtifactCollectionAliases(GQLResult):
    artifact_collection: Optional[ArtifactCollectionAliasesArtifactCollection] = Field(
        alias="artifactCollection"
    )


class ArtifactCollectionAliasesArtifactCollection(GQLResult):
    typename__: Typename[
        Literal["ArtifactCollection", "ArtifactPortfolio", "ArtifactSequence"]
    ]
    aliases: ArtifactCollectionAliasesArtifactCollectionAliases


class ArtifactCollectionAliasesArtifactCollectionAliases(GQLResult):
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[ArtifactCollectionAliasesArtifactCollectionAliasesEdges]


class ArtifactCollectionAliasesArtifactCollectionAliasesEdges(GQLResult):
    node: Optional[ArtifactAliasFragment]


ArtifactCollectionAliases.model_rebuild()
ArtifactCollectionAliasesArtifactCollection.model_rebuild()
ArtifactCollectionAliasesArtifactCollectionAliases.model_rebuild()
ArtifactCollectionAliasesArtifactCollectionAliasesEdges.model_rebuild()
