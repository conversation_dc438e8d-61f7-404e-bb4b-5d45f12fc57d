# Generated by ariadne-codegen
# Source: core/api/graphql/schemas/schema-latest.graphql

from __future__ import annotations

from enum import Enum


class ArtifactCollectionType(str, Enum):
    SEQUENCE = "SEQUENCE"
    PORTFOLIO = "PORTFOLIO"


class ArtifactState(str, Enum):
    PENDING = "PENDING"
    COMMITTED = "COMMITTED"
    DELETED = "DELETED"


class ArtifactCollectionState(str, Enum):
    READY = "READY"
    DELETED = "DELETED"
