# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from wandb._pydantic import GQLResult

from .fragments import OrgInfoFragment


class FetchOrgInfoFromEntity(GQLResult):
    entity: Optional[FetchOrgInfoFromEntityEntity]


class FetchOrgInfoFromEntityEntity(GQLResult):
    organization: Optional[OrgInfoFragment]
    user: Optional[FetchOrgInfoFromEntityEntityUser]


class FetchOrgInfoFromEntityEntityUser(GQLResult):
    organizations: List[OrgInfoFragment]


FetchOrgInfoFromEntity.model_rebuild()
FetchOrgInfoFromEntityEntity.model_rebuild()
FetchOrgInfoFromEntityEntityUser.model_rebuild()
