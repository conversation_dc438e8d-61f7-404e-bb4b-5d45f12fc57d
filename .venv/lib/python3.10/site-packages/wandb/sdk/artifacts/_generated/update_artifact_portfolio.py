# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactCollectionFragment


class UpdateArtifactPortfolio(GQLResult):
    result: Optional[UpdateArtifactPortfolioResult]


class UpdateArtifactPortfolioResult(GQLResult):
    artifact_collection: ArtifactCollectionFragment = Field(alias="artifactCollection")


UpdateArtifactPortfolio.model_rebuild()
UpdateArtifactPortfolioResult.model_rebuild()
