# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import Optional

from pydantic import Field

from wandb._pydantic import GQLResult


class FetchOrgEntityFromOrganization(GQLResult):
    organization: Optional[FetchOrgEntityFromOrganizationOrganization]


class FetchOrgEntityFromOrganizationOrganization(GQLResult):
    org_entity: Optional[FetchOrgEntityFromOrganizationOrganizationOrgEntity] = Field(
        alias="orgEntity"
    )


class FetchOrgEntityFromOrganizationOrganizationOrgEntity(GQLResult):
    name: str


FetchOrgEntityFromOrganization.model_rebuild()
FetchOrgEntityFromOrganizationOrganization.model_rebuild()
