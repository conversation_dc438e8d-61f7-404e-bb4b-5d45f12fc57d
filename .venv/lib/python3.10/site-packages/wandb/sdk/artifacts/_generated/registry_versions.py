# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import ArtifactMembershipFragment, PageInfoFragment


class RegistryVersions(GQLResult):
    organization: Optional[RegistryVersionsOrganization]


class RegistryVersionsOrganization(GQLResult):
    org_entity: Optional[RegistryVersionsOrganizationOrgEntity] = Field(
        alias="orgEntity"
    )


class RegistryVersionsOrganizationOrgEntity(GQLResult):
    name: str
    artifact_memberships: Optional[
        RegistryVersionsOrganizationOrgEntityArtifactMemberships
    ] = Field(alias="artifactMemberships")


class RegistryVersionsOrganizationOrgEntityArtifactMemberships(GQLResult):
    page_info: PageInfoFragment = Field(alias="pageInfo")
    edges: List[RegistryVersionsOrganizationOrgEntityArtifactMembershipsEdges]


class RegistryVersionsOrganizationOrgEntityArtifactMembershipsEdges(GQLResult):
    node: Optional[ArtifactMembershipFragment]


RegistryVersions.model_rebuild()
RegistryVersionsOrganization.model_rebuild()
RegistryVersionsOrganizationOrgEntity.model_rebuild()
RegistryVersionsOrganizationOrgEntityArtifactMemberships.model_rebuild()
RegistryVersionsOrganizationOrgEntityArtifactMembershipsEdges.model_rebuild()
