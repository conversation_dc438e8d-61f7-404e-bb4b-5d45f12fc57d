# Generated by ariadne-codegen
# Source: tools/graphql_codegen/artifacts/

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from wandb._pydantic import GQLResult

from .fragments import TeamRegistryMemberFragment


class RegistryTeamMembers(GQLResult):
    project: Optional[RegistryTeamMembersProject]


class RegistryTeamMembersProject(GQLResult):
    team_members: List[TeamRegistryMemberFragment] = Field(alias="teamMembers")


RegistryTeamMembers.model_rebuild()
RegistryTeamMembersProject.model_rebuild()
