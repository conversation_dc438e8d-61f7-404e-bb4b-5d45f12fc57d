# Git
.git
.gitignore

# Python
__pycache__
*.py[cod]
*$py.class
*.so
.Python
*.egg-info/
*.egg
.eggs/
dist/
build/
*.whl

# Virtual environments
.venv
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Test and coverage
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Jupyter
.ipynb_checkpoints/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Docker
# Dockerfile
# docker-compose*.yml
# .docker/

# Documentation
*.md
!README.md
docs/

# Development files
.env
.env.*
*.local

# Large model files (if stored locally during dev)
# *.pth
# *.pt
*.ckpt
*.safetensors
*.bin

# Temporary files
tmp/
temp/
*.tmp


.github

.ruff_cache

models/*
models/electroplating_ads_predict_toolkit/weights/*
models/electroplating_ads_predict_toolkit/example/*