{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["inference"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-22 08:22:27.745845: I tensorflow/core/util/port.cc:113] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-11-22 08:22:27.785830: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:9261] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "2024-11-22 08:22:27.785861: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:607] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "2024-11-22 08:22:27.787068: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1515] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "2024-11-22 08:22:27.793653: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI AVX512_BF16 AVX_VNNI AMX_TILE AMX_INT8 AMX_BF16 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-11-22 08:22:28.499055: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n"]}, {"name": "stdout", "output_type": "stream", "text": ["fused_multi_tensor is not installed corrected\n", "fused_layer_norm is not installed corrected\n", "fused_softmax is not installed corrected\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-11-22 08:22:30 | model/unimol.py | 114 | INFO | Uni-Mol(QSAR) | Loading pretrained weights from /vepfs/fs_users/ycjin/demo/weights/mol_pre_all_h_220816.pt\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Load model successfully!\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.utils.data import DataLoader, Dataset\n", "from model import *\n", "from data import *\n", "from utils import *\n", "\n", "pth_save_path = './example/ads/pth/'\n", "\n", "mol2input = Mol2Input()\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else torch.device('cpu'))\n", "fitting_model = FittingNet(output_dim=1)\n", "unimol_model = UniMolModel(output_dim=1, data_type='molecule', remove_hs=False)\n", "fitting_model.load_state_dict(torch.load(pth_save_path +'atomic_fit_nh.pth'))\n", "unimol_model.load_state_dict(torch.load(pth_save_path + 'atomic_model_nh.pth'))\n", "fitting_model.to(device)\n", "fitting_model.eval()\n", "unimol_model.to(device)\n", "unimol_model.eval()\n", "print('Load model successfully!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["单项预测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor(-1.9053, device='cuda:0', grad_fn=<SumBackward0>)\n"]}, {"data": {"application/3dmoljs_load.v0": "<div id=\"3dmolviewer_17322637891919384\"  style=\"position: relative; width: 300px; height: 300px;\">\n        <p id=\"3dmolwarning_17322637891919384\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n        </div>\n<script>\n\nvar loadScriptAsync = function(uri){\n  return new Promise((resolve, reject) => {\n    //this is to ignore the existence of requirejs amd\n    var savedexports, savedmodule;\n    if (typeof exports !== 'undefined') savedexports = exports;\n    else exports = {}\n    if (typeof module !== 'undefined') savedmodule = module;\n    else module = {}\n\n    var tag = document.createElement('script');\n    tag.src = uri;\n    tag.async = true;\n    tag.onload = () => {\n        exports = savedexports;\n        module = savedmodule;\n        resolve();\n    };\n  var firstScriptTag = document.getElementsByTagName('script')[0];\n  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n});\n};\n\nif(typeof $3Dmolpromise === 'undefined') {\n$3Dmolpromise = null;\n  $3Dmolpromise = loadScriptAsync('https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.1.0/3Dmol-min.js');\n}\n\nvar viewer_17322637891919384 = null;\nvar warn = document.getElementById(\"3dmolwarning_17322637891919384\");\nif(warn) {\n    warn.parentNode.removeChild(warn);\n}\n$3Dmolpromise.then(function() {\nviewer_17322637891919384 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17322637891919384\"),{backgroundColor:\"white\"});\nviewer_17322637891919384.zoomTo();\n\tviewer_17322637891919384.addModel(\"25\\nProperties=species:S:1:pos:R:3 482737030=T pbc=\\\"F F F\\\"\\nC        5.18296500       1.88343300       3.65420400\\nO        4.35993000       2.55419600       2.71120400\\nC        3.20836500       1.92536400       2.30786800\\nC        2.80685600       0.65860800       2.74016600\\nC        1.61463200       0.09724400       2.27307200\\nC        0.80016200       0.79190800       1.37166800\\nC        1.21021300       2.06039200       0.93100400\\nC        2.40303900       2.63294900       1.39341000\\nO        2.87017400       3.86661800       1.01048800\\nC        2.06530500       4.63659300       0.12936500\\nC       -0.44787600       0.17542700       0.88376200\\nC       -1.59755500       0.85947300       0.83488500\\nC       -2.82127900       0.19966000       0.34412500\\nO       -3.85923800       0.84497000       0.30215300\\nC       -2.68609900      -1.21606400      -0.06804100\\nC       -3.74712700      -1.98247400      -0.56060400\\nO       -5.00359500      -1.44542400      -0.68971300\\nC       -3.56943300      -3.32915700      -0.94233600\\nO       -4.72480200      -3.91470800      -1.40028600\\nC       -4.67618200      -5.27403400      -1.81102100\\nC       -2.29790000      -3.89945800      -0.82181000\\nO       -2.05812800      -5.19729500      -1.17588000\\nC       -1.23208600      -3.14143900      -0.32988700\\nC       -1.42917700      -1.80887600       0.04368000\\nO       -0.31956600      -1.15085200       0.51538700\\n\");\n\tviewer_17322637891919384.setStyle({\"index\": 0},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 1},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a9a9a9\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 2},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 3},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7c7c7c\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 4},{\"sphere\": {\"scale\": 0.3, \"color\": \"#777777\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 5},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7f7f7f\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 6},{\"sphere\": {\"scale\": 0.3, \"color\": \"#939393\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 7},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2d2d2d\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 8},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a6a6a6\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 9},{\"sphere\": {\"scale\": 0.3, \"color\": \"#727272\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 10},{\"sphere\": {\"scale\": 0.3, \"color\": \"#8c8c8c\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 11},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 12},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b3b3b3\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 13},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b1b1b1\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 14},{\"sphere\": {\"scale\": 0.3, \"color\": \"#858585\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 15},{\"sphere\": {\"scale\": 0.3, \"color\": \"#343434\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 16},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2f2f2f\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 17},{\"sphere\": {\"scale\": 0.3, \"color\": \"#666666\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 18},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 19},{\"sphere\": {\"scale\": 0.3, \"color\": \"#818181\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 20},{\"sphere\": {\"scale\": 0.3, \"color\": \"#4c4c4c\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 21},{\"sphere\": {\"scale\": 0.3, \"color\": \"#787878\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 22},{\"sphere\": {\"scale\": 0.3, \"color\": \"#5a5a5a\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 23},{\"sphere\": {\"scale\": 0.3, \"color\": \"#838383\"}});\n\tviewer_17322637891919384.setStyle({\"index\": 24},{\"sphere\": {\"scale\": 0.3, \"color\": \"#515151\"}});\n\tviewer_17322637891919384.zoomTo();\nviewer_17322637891919384.render();\n});\n</script>", "text/html": ["<div id=\"3dmolviewer_17322637891919384\"  style=\"position: relative; width: 300px; height: 300px;\">\n", "        <p id=\"3dmolwarning_17322637891919384\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n", "        </div>\n", "<script>\n", "\n", "var loadScriptAsync = function(uri){\n", "  return new Promise((resolve, reject) => {\n", "    //this is to ignore the existence of requirejs amd\n", "    var savedexports, savedmodule;\n", "    if (typeof exports !== 'undefined') savedexports = exports;\n", "    else exports = {}\n", "    if (typeof module !== 'undefined') savedmodule = module;\n", "    else module = {}\n", "\n", "    var tag = document.createElement('script');\n", "    tag.src = uri;\n", "    tag.async = true;\n", "    tag.onload = () => {\n", "        exports = savedexports;\n", "        module = savedmodule;\n", "        resolve();\n", "    };\n", "  var firstScriptTag = document.getElementsByTagName('script')[0];\n", "  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n", "});\n", "};\n", "\n", "if(typeof $3Dmolpromise === 'undefined') {\n", "$3Dmolpromise = null;\n", "  $3Dmolpromise = loadScriptAsync('https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.1.0/3Dmol-min.js');\n", "}\n", "\n", "var viewer_17322637891919384 = null;\n", "var warn = document.getElementById(\"3dmolwarning_17322637891919384\");\n", "if(warn) {\n", "    warn.parentNode.removeChild(warn);\n", "}\n", "$3Dmolpromise.then(function() {\n", "viewer_17322637891919384 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17322637891919384\"),{backgroundColor:\"white\"});\n", "viewer_17322637891919384.zoomTo();\n", "\tviewer_17322637891919384.addModel(\"25\\nProperties=species:S:1:pos:R:3 482737030=T pbc=\\\"F F F\\\"\\nC        5.18296500       1.88343300       3.65420400\\nO        4.35993000       2.55419600       2.71120400\\nC        3.20836500       1.92536400       2.30786800\\nC        2.80685600       0.65860800       2.74016600\\nC        1.61463200       0.09724400       2.27307200\\nC        0.80016200       0.79190800       1.37166800\\nC        1.21021300       2.06039200       0.93100400\\nC        2.40303900       2.63294900       1.39341000\\nO        2.87017400       3.86661800       1.01048800\\nC        2.06530500       4.63659300       0.12936500\\nC       -0.44787600       0.17542700       0.88376200\\nC       -1.59755500       0.85947300       0.83488500\\nC       -2.82127900       0.19966000       0.34412500\\nO       -3.85923800       0.84497000       0.30215300\\nC       -2.68609900      -1.21606400      -0.06804100\\nC       -3.74712700      -1.98247400      -0.56060400\\nO       -5.00359500      -1.44542400      -0.68971300\\nC       -3.56943300      -3.32915700      -0.94233600\\nO       -4.72480200      -3.91470800      -1.40028600\\nC       -4.67618200      -5.27403400      -1.81102100\\nC       -2.29790000      -3.89945800      -0.82181000\\nO       -2.05812800      -5.19729500      -1.17588000\\nC       -1.23208600      -3.14143900      -0.32988700\\nC       -1.42917700      -1.80887600       0.04368000\\nO       -0.31956600      -1.15085200       0.51538700\\n\");\n", "\tviewer_17322637891919384.setStyle({\"index\": 0},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 1},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a9a9a9\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 2},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 3},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7c7c7c\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 4},{\"sphere\": {\"scale\": 0.3, \"color\": \"#777777\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 5},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7f7f7f\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 6},{\"sphere\": {\"scale\": 0.3, \"color\": \"#939393\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 7},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2d2d2d\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 8},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a6a6a6\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 9},{\"sphere\": {\"scale\": 0.3, \"color\": \"#727272\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 10},{\"sphere\": {\"scale\": 0.3, \"color\": \"#8c8c8c\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 11},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 12},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b3b3b3\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 13},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b1b1b1\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 14},{\"sphere\": {\"scale\": 0.3, \"color\": \"#858585\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 15},{\"sphere\": {\"scale\": 0.3, \"color\": \"#343434\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 16},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2f2f2f\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 17},{\"sphere\": {\"scale\": 0.3, \"color\": \"#666666\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 18},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 19},{\"sphere\": {\"scale\": 0.3, \"color\": \"#818181\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 20},{\"sphere\": {\"scale\": 0.3, \"color\": \"#4c4c4c\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 21},{\"sphere\": {\"scale\": 0.3, \"color\": \"#787878\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 22},{\"sphere\": {\"scale\": 0.3, \"color\": \"#5a5a5a\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 23},{\"sphere\": {\"scale\": 0.3, \"color\": \"#838383\"}});\n", "\tviewer_17322637891919384.setStyle({\"index\": 24},{\"sphere\": {\"scale\": 0.3, \"color\": \"#515151\"}});\n", "\tviewer_17322637891919384.zoomTo();\n", "viewer_17322637891919384.render();\n", "});\n", "</script>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/3dmoljs_load.v0": "<div id=\"3dmolviewer_17322637891933026\"  style=\"position: relative; width: 300px; height: 300px;\">\n        <p id=\"3dmolwarning_17322637891933026\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n        </div>\n<script>\n\nvar loadScriptAsync = function(uri){\n  return new Promise((resolve, reject) => {\n    //this is to ignore the existence of requirejs amd\n    var savedexports, savedmodule;\n    if (typeof exports !== 'undefined') savedexports = exports;\n    else exports = {}\n    if (typeof module !== 'undefined') savedmodule = module;\n    else module = {}\n\n    var tag = document.createElement('script');\n    tag.src = uri;\n    tag.async = true;\n    tag.onload = () => {\n        exports = savedexports;\n        module = savedmodule;\n        resolve();\n    };\n  var firstScriptTag = document.getElementsByTagName('script')[0];\n  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n});\n};\n\nif(typeof $3Dmolpromise === 'undefined') {\n$3Dmolpromise = null;\n  $3Dmolpromise = loadScriptAsync('https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.1.0/3Dmol-min.js');\n}\n\nvar viewer_17322637891933026 = null;\nvar warn = document.getElementById(\"3dmolwarning_17322637891933026\");\nif(warn) {\n    warn.parentNode.removeChild(warn);\n}\n$3Dmolpromise.then(function() {\nviewer_17322637891933026 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17322637891933026\"),{backgroundColor:\"white\"});\nviewer_17322637891933026.zoomTo();\n\tviewer_17322637891933026.addModel(\"25\\nProperties=species:S:1:pos:R:3 482737030=T pbc=\\\"F F F\\\"\\nC        5.18296500       1.88343300       3.65420400\\nO        4.35993000       2.55419600       2.71120400\\nC        3.20836500       1.92536400       2.30786800\\nC        2.80685600       0.65860800       2.74016600\\nC        1.61463200       0.09724400       2.27307200\\nC        0.80016200       0.79190800       1.37166800\\nC        1.21021300       2.06039200       0.93100400\\nC        2.40303900       2.63294900       1.39341000\\nO        2.87017400       3.86661800       1.01048800\\nC        2.06530500       4.63659300       0.12936500\\nC       -0.44787600       0.17542700       0.88376200\\nC       -1.59755500       0.85947300       0.83488500\\nC       -2.82127900       0.19966000       0.34412500\\nO       -3.85923800       0.84497000       0.30215300\\nC       -2.68609900      -1.21606400      -0.06804100\\nC       -3.74712700      -1.98247400      -0.56060400\\nO       -5.00359500      -1.44542400      -0.68971300\\nC       -3.56943300      -3.32915700      -0.94233600\\nO       -4.72480200      -3.91470800      -1.40028600\\nC       -4.67618200      -5.27403400      -1.81102100\\nC       -2.29790000      -3.89945800      -0.82181000\\nO       -2.05812800      -5.19729500      -1.17588000\\nC       -1.23208600      -3.14143900      -0.32988700\\nC       -1.42917700      -1.80887600       0.04368000\\nO       -0.31956600      -1.15085200       0.51538700\\n\");\n\tviewer_17322637891933026.setStyle({\"index\": 0},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 1},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a9a9a9\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 2},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 3},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7c7c7c\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 4},{\"sphere\": {\"scale\": 0.3, \"color\": \"#777777\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 5},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7f7f7f\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 6},{\"sphere\": {\"scale\": 0.3, \"color\": \"#939393\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 7},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2d2d2d\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 8},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a6a6a6\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 9},{\"sphere\": {\"scale\": 0.3, \"color\": \"#727272\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 10},{\"sphere\": {\"scale\": 0.3, \"color\": \"#8c8c8c\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 11},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 12},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b3b3b3\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 13},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b1b1b1\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 14},{\"sphere\": {\"scale\": 0.3, \"color\": \"#858585\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 15},{\"sphere\": {\"scale\": 0.3, \"color\": \"#343434\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 16},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2f2f2f\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 17},{\"sphere\": {\"scale\": 0.3, \"color\": \"#666666\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 18},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 19},{\"sphere\": {\"scale\": 0.3, \"color\": \"#818181\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 20},{\"sphere\": {\"scale\": 0.3, \"color\": \"#4c4c4c\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 21},{\"sphere\": {\"scale\": 0.3, \"color\": \"#787878\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 22},{\"sphere\": {\"scale\": 0.3, \"color\": \"#5a5a5a\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 23},{\"sphere\": {\"scale\": 0.3, \"color\": \"#838383\"}});\n\tviewer_17322637891933026.setStyle({\"index\": 24},{\"sphere\": {\"scale\": 0.3, \"color\": \"#515151\"}});\n\tviewer_17322637891933026.zoomTo();\n\tviewer_17322637891933026.setStyle({\"sphere\": {\"scale\": 0.3}});\n\tviewer_17322637891933026.zoomTo();\nviewer_17322637891933026.render();\n});\n</script>", "text/html": ["<div id=\"3dmolviewer_17322637891933026\"  style=\"position: relative; width: 300px; height: 300px;\">\n", "        <p id=\"3dmolwarning_17322637891933026\" style=\"background-color:#ffcccc;color:black\">3Dmol.js failed to load for some reason.  Please check your browser console for error messages.<br></p>\n", "        </div>\n", "<script>\n", "\n", "var loadScriptAsync = function(uri){\n", "  return new Promise((resolve, reject) => {\n", "    //this is to ignore the existence of requirejs amd\n", "    var savedexports, savedmodule;\n", "    if (typeof exports !== 'undefined') savedexports = exports;\n", "    else exports = {}\n", "    if (typeof module !== 'undefined') savedmodule = module;\n", "    else module = {}\n", "\n", "    var tag = document.createElement('script');\n", "    tag.src = uri;\n", "    tag.async = true;\n", "    tag.onload = () => {\n", "        exports = savedexports;\n", "        module = savedmodule;\n", "        resolve();\n", "    };\n", "  var firstScriptTag = document.getElementsByTagName('script')[0];\n", "  firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\n", "});\n", "};\n", "\n", "if(typeof $3Dmolpromise === 'undefined') {\n", "$3Dmolpromise = null;\n", "  $3Dmolpromise = loadScriptAsync('https://cdnjs.cloudflare.com/ajax/libs/3Dmol/2.1.0/3Dmol-min.js');\n", "}\n", "\n", "var viewer_17322637891933026 = null;\n", "var warn = document.getElementById(\"3dmolwarning_17322637891933026\");\n", "if(warn) {\n", "    warn.parentNode.removeChild(warn);\n", "}\n", "$3Dmolpromise.then(function() {\n", "viewer_17322637891933026 = $3Dmol.createViewer(document.getElementById(\"3dmolviewer_17322637891933026\"),{backgroundColor:\"white\"});\n", "viewer_17322637891933026.zoomTo();\n", "\tviewer_17322637891933026.addModel(\"25\\nProperties=species:S:1:pos:R:3 482737030=T pbc=\\\"F F F\\\"\\nC        5.18296500       1.88343300       3.65420400\\nO        4.35993000       2.55419600       2.71120400\\nC        3.20836500       1.92536400       2.30786800\\nC        2.80685600       0.65860800       2.74016600\\nC        1.61463200       0.09724400       2.27307200\\nC        0.80016200       0.79190800       1.37166800\\nC        1.21021300       2.06039200       0.93100400\\nC        2.40303900       2.63294900       1.39341000\\nO        2.87017400       3.86661800       1.01048800\\nC        2.06530500       4.63659300       0.12936500\\nC       -0.44787600       0.17542700       0.88376200\\nC       -1.59755500       0.85947300       0.83488500\\nC       -2.82127900       0.19966000       0.34412500\\nO       -3.85923800       0.84497000       0.30215300\\nC       -2.68609900      -1.21606400      -0.06804100\\nC       -3.74712700      -1.98247400      -0.56060400\\nO       -5.00359500      -1.44542400      -0.68971300\\nC       -3.56943300      -3.32915700      -0.94233600\\nO       -4.72480200      -3.91470800      -1.40028600\\nC       -4.67618200      -5.27403400      -1.81102100\\nC       -2.29790000      -3.89945800      -0.82181000\\nO       -2.05812800      -5.19729500      -1.17588000\\nC       -1.23208600      -3.14143900      -0.32988700\\nC       -1.42917700      -1.80887600       0.04368000\\nO       -0.31956600      -1.15085200       0.51538700\\n\");\n", "\tviewer_17322637891933026.setStyle({\"index\": 0},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 1},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a9a9a9\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 2},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 3},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7c7c7c\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 4},{\"sphere\": {\"scale\": 0.3, \"color\": \"#777777\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 5},{\"sphere\": {\"scale\": 0.3, \"color\": \"#7f7f7f\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 6},{\"sphere\": {\"scale\": 0.3, \"color\": \"#939393\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 7},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2d2d2d\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 8},{\"sphere\": {\"scale\": 0.3, \"color\": \"#a6a6a6\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 9},{\"sphere\": {\"scale\": 0.3, \"color\": \"#727272\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 10},{\"sphere\": {\"scale\": 0.3, \"color\": \"#8c8c8c\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 11},{\"sphere\": {\"scale\": 0.3, \"color\": \"#ababab\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 12},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b3b3b3\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 13},{\"sphere\": {\"scale\": 0.3, \"color\": \"#b1b1b1\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 14},{\"sphere\": {\"scale\": 0.3, \"color\": \"#858585\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 15},{\"sphere\": {\"scale\": 0.3, \"color\": \"#343434\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 16},{\"sphere\": {\"scale\": 0.3, \"color\": \"#2f2f2f\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 17},{\"sphere\": {\"scale\": 0.3, \"color\": \"#666666\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 18},{\"sphere\": {\"scale\": 0.3, \"color\": \"#3e3e3e\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 19},{\"sphere\": {\"scale\": 0.3, \"color\": \"#818181\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 20},{\"sphere\": {\"scale\": 0.3, \"color\": \"#4c4c4c\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 21},{\"sphere\": {\"scale\": 0.3, \"color\": \"#787878\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 22},{\"sphere\": {\"scale\": 0.3, \"color\": \"#5a5a5a\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 23},{\"sphere\": {\"scale\": 0.3, \"color\": \"#838383\"}});\n", "\tviewer_17322637891933026.setStyle({\"index\": 24},{\"sphere\": {\"scale\": 0.3, \"color\": \"#515151\"}});\n", "\tviewer_17322637891933026.zoomTo();\n", "\tviewer_17322637891933026.setStyle({\"sphere\": {\"scale\": 0.3}});\n", "\tviewer_17322637891933026.zoomTo();\n", "viewer_17322637891933026.render();\n", "});\n", "</script>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from ase import Atoms\n", "from ase.io import read,write\n", "import py3Dmol\n", "from ase.io import read\n", "\n", "input_file = './example/pub25/data/xyz/400.xyz' # input molecule file\n", "atom = read(input_file)\n", "\n", "# 去除H原子\n", "sym = np.array(atom.get_chemical_symbols())\n", "coord = [torch.tensor(atom.get_positions())[sym != 'H']]\n", "atype = [np.array(atom.get_chemical_symbols())[sym != 'H']]\n", "atom = atom[sym != 'H']\n", "\n", "# 预测\n", "input_dict = mol2input.coord2unimol_inputs(coord,atype)\n", "for k in input_dict.keys(): input_dict[k] = input_dict[k].to(device)\n", "atomic_reprs = unimol_model(return_repr=True,**input_dict)['atomic_reprs']\n", "pred = []\n", "for repr in atomic_reprs:\n", "    atomic_p = fitting_model(repr)\n", "    p = torch.sum(fitting_model(repr))\n", "\n", "print(atomic_p)# 原子贡献\n", "print(p)# 总能量\n", "\n", "\n", "# 可视化\n", "value = np.array(atomic_p.detach().cpu()).reshape(-1)\n", "value = (value-np.min(value))/(np.max(value)-np.min(value)) * 200+50\n", "value = np.int16(value)\n", "\n", "setting = {\n", "    'H':[0.3],\n", "    'C':[0.3],\n", "    'O':[0.3],\n", "    'N':[0.3],\n", "    'S':[0.3],\n", "    'F':[0.3],\n", "    'Cl':[0.3],\n", "    'Br':[0.3],\n", "    'I':[0.3]\n", "}\n", "\n", "def self_hex(n):\n", "    return hex(n)[2:].<PERSON><PERSON>(2)\n", "\n", "write('md_n.xyz', atom)\n", "\n", "# 贡献图\n", "view = py3Dmol.view(width=300, height=300)\n", "view.addModel(open('md_n.xyz').read(), format='xyz')\n", "for i,type in enumerate(atype[0]):\n", "    view.setStyle({'index':i}, {'sphere': {'scale':setting[type][0],'color':'#'+self_hex(value[i])+self_hex(value[i])+self_hex(value[i])}})\n", "view.zoomTo(animate=True)\n", "view.show()\n", "\n", "# 分子图\n", "view_2 = py3Dmol.view(width=300, height=300)\n", "view_2.addModel(open('md_n.xyz').read(), format='xyz')\n", "view.setStyle({'sphere': {'scale': 0.3}})\n", "view.zoomTo(animate=True)\n", "view.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}